asgiref==3.5.0
astroid==2.4.1
dj-database-url==0.5.0
dj-email-url==1.0.0
django==3.2.25
django-appconf==1.0.4
django-countries==7.3.2
django-debug-toolbar==2.2.1
django-debug-toolbar-request-history==0.1.1
django-extensions==2.2.9
django-filter==2.4.0
django-graphiql-debug-toolbar==0.1.4
django-measurement==3.2.3
django-mptt==0.11.0
django-phonenumber-field==4.0.0
django-prices==2.2.0
django-prices-openexchangerates==1.1.0
django-prices-vatlayer==1.1.0
django-storages==1.9.1
django-stubs==1.2.0
django-versatileimagefield==2.0
draftjs-sanitizer==1.0.0
flake8==3.8.1
google-i18n-address==2.3.5
graphene==2.1.8
graphene-django==2.10.0
git+https://github.com/my-workforce/graphene-federation.git@wounder-graph
graphql-core==2.3.2
graphql-relay==2.0.1
jaeger-client==4.3.0
jinja2==3.1.4
jsonfield==3.1.0
maxminddb-geolite2==2018.703
measurement==3.2.2
opentracing==2.3.0
phonenumberslite==8.12.3
prices==1.0.0
pyflakes==2.2.0
python-dateutil==2.8.1
python-magic==0.4.27; sys_platform != "win32"
python-magic-bin==0.4.14; sys_platform == "win32" or sys_platform == "darwin"
pytz==2020.1
regex==2020.5.7
requests==2.32.2
sentry-sdk==2.8.0
typed-ast==1.4.1
typing==*******
typing-extensions==4.4.0
unidecode==1.1.1
urllib3==1.26.19
requests_cache==0.5.2
DateTimeRange==1.0.0
django-request-logging==0.7.5
googlemaps==4.4.2
pyproj==2.6.1.post1
django-phone-verify==2.0.1
twilio==6.46.0
gunicorn==22.0.0
uvicorn==0.11.8
django-uw-keycloak==0.7.5
python-keycloak==0.26.1
sgqlc==15.0
pytest==6.2.5
pytest-django==4.5.2
pytest-mock==3.6.1
pytest-env==0.6.2
deepdiff==5.7.0
coverage==6.2
pyjwt[crypto]==2.4.0
pem==21.2.0
kafka-python==2.0.2
dataclasses-json==0.5.6
redis==4.4.4
channels-redis==3.4.0
django-channels-graphql-ws==0.9.1
django_prometheus==2.2.0
python-json-logger==2.0.2
mysqlclient==2.1.1
django-health-check==3.17.0
django-opensearch-dsl==0.5.1
pyotp==2.9.0
pycryptodome==3.19.1
pycryptodomex==3.21.0
mssql-django==1.5
pyodbc==4.0.35
SQLAlchemy==1.4.46
git+https://github.com/my-workforce/TMB-SDK.git@main
