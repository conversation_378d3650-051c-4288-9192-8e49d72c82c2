### Build and install packages
FROM python:3.8 as build-python

RUN apt-get -y update \
  && apt-get install -y gettext \
  # Install PROJ library and development headers for pyproj
  && apt-get install -y \
  libproj-dev \
  proj-data \
  proj-bin \
  libgeos-dev \
  # Cleanup apt cache
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements_dev.txt /app/
WORKDIR /app
RUN pip install -r requirements_dev.txt

### Final image
FROM python:3.8-slim

RUN groupadd -r usermanagment && useradd -r -g usermanagment usermanagment

RUN apt-get update \
  && apt-get install -y \
  gdal-bin \
  python3-gdal \
  libxml2 \
  libcairo2 \
  libpango-1.0-0 \
  libpangocairo-1.0-0 \
  libgdk-pixbuf2.0-0 \
  shared-mime-info \
  mime-support \
  alien \
  libaio1 \
  wget \
  curl \
  # Add PROJ library for pyproj runtime support
  libproj-dev \
  proj-data \
  proj-bin \
  libgeos-dev \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

COPY odbc_driver.sh /tmp/
RUN chmod +x /tmp/odbc_driver.sh
RUN /bin/bash -c '/tmp/odbc_driver.sh &> /tmp/odbc_install.log'
RUN rm /tmp/odbc_driver.sh

RUN wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
RUN dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb && rm -rf /var/lib/apt/lists/* && rm libssl1.1_1.1.1f-1ubuntu2_amd64.deb

RUN mkdir -p /app/media /app/static \
  && chown -R usermanagment:usermanagment /app/

COPY --from=build-python /usr/local/lib/python3.8/site-packages/ /usr/local/lib/python3.8/site-packages/
COPY --from=build-python /usr/local/bin/ /usr/local/bin/
COPY . /app
WORKDIR /app

EXPOSE 8000
ENV PYTHONUNBUFFERED 1
CMD ["gunicorn", "--bind", ":8000", "--workers", "8","--threads","4", "--max-requests", "1000", "--max-requests-jitter", "100", "usermanagment.wsgi:application"]
