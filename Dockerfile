### Build and install packages
FROM python:3.8 as build-python

RUN apt-get -y update \
  && apt-get install -y gettext \
  # Install PROJ library and development headers for pyproj
  && apt-get install -y \
  libproj-dev \
  proj-data \
  proj-bin \
  libgeos-dev \
  # Also install build essentials for compiling
  build-essential \
  # Cleanup apt cache
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Set environment variables for PROJ
ENV PROJ_DIR=/usr
ENV PROJ_LIBDIR=/usr/lib
ENV PROJ_INCDIR=/usr/include

# Install Python dependencies
COPY requirements_dev.txt /app/
WORKDIR /app
# First upgrade pip
RUN pip install --upgrade pip
# Install pyproj from system packages instead of pip
RUN apt-get update && apt-get install -y python3-pyproj && apt-get clean && rm -rf /var/lib/apt/lists/*
# Create a modified requirements file without pyproj
RUN grep -v "pyproj==" requirements_dev.txt > requirements_no_pyproj.txt
# Install the rest of the requirements
RUN pip install -r requirements_no_pyproj.txt

### Final image
FROM python:3.8-slim

RUN groupadd -r usermanagment && useradd -r -g usermanagment usermanagment

RUN apt-get update \
  && apt-get install -y \
  gdal-bin \
  python3-gdal \
  libxml2 \
  libcairo2 \
  libpango-1.0-0 \
  libpangocairo-1.0-0 \
  libgdk-pixbuf2.0-0 \
  shared-mime-info \
  mime-support \
  alien \
  libaio1 \
  wget \
  curl \
  # Add PROJ library for pyproj runtime support
  libproj-dev \
  proj-data \
  proj-bin \
  libgeos-dev \
  # Add pyproj system package
  python3-pyproj \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

COPY odbc_driver.sh /tmp/
RUN chmod +x /tmp/odbc_driver.sh
RUN /bin/bash -c '/tmp/odbc_driver.sh &> /tmp/odbc_install.log'
RUN rm /tmp/odbc_driver.sh

# SSL libraries are already available in the base image

RUN mkdir -p /app/media /app/static \
  && chown -R usermanagment:usermanagment /app/

COPY --from=build-python /usr/local/lib/python3.8/site-packages/ /usr/local/lib/python3.8/site-packages/
COPY --from=build-python /usr/local/bin/ /usr/local/bin/
COPY . /app
WORKDIR /app

EXPOSE 8000
ENV PYTHONUNBUFFERED 1
CMD ["gunicorn", "--bind", ":8000", "--workers", "8","--threads","4", "--max-requests", "1000", "--max-requests-jitter", "100", "usermanagment.wsgi:application"]
