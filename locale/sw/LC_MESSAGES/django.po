# Saleor
# Copyright (C) 2017 Mirumee Software
# This file is distributed under the same license as the Saleor package.
# <AUTHOR> <EMAIL>, 2017.
#
# Translators:
# <PERSON> alex <<EMAIL>>, 2019
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: master\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-14 04:02-0500\n"
"PO-Revision-Date: 2017-02-08 11:00+0000\n"
"Last-Translator: <PERSON> alex <<EMAIL>>, 2019\n"
"Language-Team: Swahili (https://www.transifex.com/mirumee/teams/34782/sw/)\n"
"Language: sw\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: saleor/account/templatetags/i18n_address_tags.py:12
#, python-format
msgctxt "Address data"
msgid "%(first_name)s %(last_name)s"
msgstr ""

#: saleor/account/templatetags/i18n_address_tags.py:16
#, python-format
msgctxt "Address data"
msgid ""
"%(street_address_1)s\n"
"%(street_address_2)s"
msgstr ""

#: templates/read_only/read_only_splash.html:48
msgid "Be aware pirate! <br> Saleor runs in <span>read only mode!</span>"
msgstr ""

#: templates/read_only/read_only_splash.html:49
msgid "Close"
msgstr ""

#: templates/templated_email/compiled/account_delete.html:174
#: templates/templated_email/compiled/confirm.html:174
#: templates/templated_email/compiled/confirm_fulfillment.html:174
#: templates/templated_email/compiled/confirm_order.html:175
#: templates/templated_email/compiled/confirm_payment.html:174
#: templates/templated_email/compiled/email_changed_notification.html:174
#: templates/templated_email/compiled/password_reset.html:174
#: templates/templated_email/compiled/request_email_change.html:174
#: templates/templated_email/compiled/set_customer_password.html:174
#: templates/templated_email/compiled/set_password.html:174
#: templates/templated_email/compiled/staff_confirm_order.html:175
#: templates/templated_email/compiled/update_fulfillment.html:174
msgctxt "Standard e-mail greeting"
msgid "Hi!"
msgstr ""

#: templates/templated_email/compiled/account_delete.html:182
#, python-format
msgctxt "Account delete e-mail text"
msgid ""
"You're receiving this e-mail because you or someone else has requested a "
"deletion of your user account at %(domain)s.<br> Click the link below to "
"delete your account. Please note that this action is permanent and cannot be "
"reversed."
msgstr ""

#: templates/templated_email/compiled/account_delete.html:255
#: templates/templated_email/compiled/confirm.html:255
#: templates/templated_email/compiled/confirm_fulfillment.html:362
#: templates/templated_email/compiled/confirm_order.html:465
#: templates/templated_email/compiled/confirm_payment.html:245
#: templates/templated_email/compiled/email_changed_notification.html:245
#: templates/templated_email/compiled/password_reset.html:255
#: templates/templated_email/compiled/request_email_change.html:255
#: templates/templated_email/compiled/set_customer_password.html:255
#: templates/templated_email/compiled/set_password.html:255
#: templates/templated_email/compiled/staff_confirm_order.html:465
#: templates/templated_email/compiled/update_fulfillment.html:350
msgctxt "Base email text"
msgid "This is an automatically generated e-mail, please do not reply."
msgstr ""

#: templates/templated_email/compiled/account_delete.html:317
#: templates/templated_email/compiled/confirm.html:317
#: templates/templated_email/compiled/confirm_fulfillment.html:424
#: templates/templated_email/compiled/confirm_order.html:527
#: templates/templated_email/compiled/confirm_payment.html:307
#: templates/templated_email/compiled/email_changed_notification.html:307
#: templates/templated_email/compiled/password_reset.html:317
#: templates/templated_email/compiled/request_email_change.html:317
#: templates/templated_email/compiled/set_customer_password.html:317
#: templates/templated_email/compiled/set_password.html:317
#: templates/templated_email/compiled/staff_confirm_order.html:527
#: templates/templated_email/compiled/update_fulfillment.html:412
#, python-format
msgctxt "Base email footer"
msgid "Sincerely, %(site_name)s"
msgstr ""

#: templates/templated_email/compiled/confirm.html:182
#, python-format
msgctxt "Account confirmation e-mail text"
msgid ""
"In order to log into %(domain)s, you have to confirm your email address "
"first. Please click the link below to do so and log into your account."
msgstr ""

#: templates/templated_email/compiled/confirm_fulfillment.html:182
msgctxt "Fulfillment confirmation email text"
msgid "Thank you for your order. Below is the list of fulfilled products."
msgstr ""

#: templates/templated_email/compiled/confirm_fulfillment.html:186
#, python-format
msgctxt "Fulfillment confirmation email text"
msgid "You can track your shipment with %(tracking_number)s code."
msgstr ""

#: templates/templated_email/compiled/confirm_fulfillment.html:198
msgctxt "Fulfillment digital products email text"
msgid "You can download your digital products by clicking in download link(s)."
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:183
#, python-format
msgctxt "Order confirmation e-mail text"
msgid ""
"Thank you for your order. Below is the list of ordered products. To see your "
"payment details please visit: <a href=\"%(order_details_url)s\">"
"%(order_details_url)s</a>"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:255
#: templates/templated_email/compiled/staff_confirm_order.html:255
msgctxt "E-mail order lines summary table"
msgid "Subtotal"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:263
#: templates/templated_email/compiled/staff_confirm_order.html:263
msgctxt "E-mail order lines summary table"
msgid "Shipping"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:272
#: templates/templated_email/compiled/staff_confirm_order.html:272
msgctxt "E-mail order lines summary table"
msgid "Taxes (included)"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:274
#: templates/templated_email/compiled/staff_confirm_order.html:274
msgctxt "E-mail order lines summary table"
msgid "Taxes"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:284
#: templates/templated_email/compiled/staff_confirm_order.html:284
msgctxt "E-mail order lines summary table"
msgid "Discount"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:293
#: templates/templated_email/compiled/staff_confirm_order.html:293
msgctxt "E-mail order lines summary table"
msgid "Total"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:302
#: templates/templated_email/compiled/staff_confirm_order.html:302
msgctxt "Ordered item name"
msgid "Item"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:303
#: templates/templated_email/compiled/staff_confirm_order.html:303
msgctxt "Quantity ordered of a product"
msgid "Quantity"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:304
#: templates/templated_email/compiled/staff_confirm_order.html:304
msgctxt "Unit price of a product"
msgid "Per unit"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:305
#: templates/templated_email/compiled/staff_confirm_order.html:305
msgctxt "Ordered item subtotal (unit price * quantity)"
msgid "Subtotal"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:383
#: templates/templated_email/compiled/staff_confirm_order.html:383
msgctxt "Order confirmation e-mail billing address"
msgid "Billing address"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:384
#: templates/templated_email/compiled/staff_confirm_order.html:384
msgctxt "Order confirmation e-mail shipping address"
msgid "Shipping address"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:393
#: templates/templated_email/compiled/staff_confirm_order.html:393
msgctxt "Order confirmation e-mail text"
msgid "No billing address"
msgstr ""

#: templates/templated_email/compiled/confirm_order.html:400
#: templates/templated_email/compiled/staff_confirm_order.html:400
msgctxt "Order confirmation e-mail text"
msgid "No shipping required"
msgstr ""

#: templates/templated_email/compiled/confirm_payment.html:182
msgctxt "Payment confirmation e-mail text"
msgid "Thank you for your payment. Your payment was successfully charged."
msgstr ""

#: templates/templated_email/compiled/email_changed_notification.html:182
#, python-format
msgctxt "Email change e-mail text"
msgid ""
"You're receiving this e-mail because you or someone else has changed email "
"for your user account at %(domain)s.<br> If you didn't request this change, "
"please contact the administrator."
msgstr ""

#: templates/templated_email/compiled/password_reset.html:182
#, python-format
msgctxt "Password reset e-mail text"
msgid ""
"You're receiving this e-mail because you or someone else has requested a "
"password for your user account at %(domain)s.<br> It can be safely ignored "
"if you did not request a password reset. Click the link below to reset your "
"password."
msgstr ""

#: templates/templated_email/compiled/request_email_change.html:182
#, python-format
msgctxt "Email change e-mail text"
msgid ""
"You're receiving this e-mail because you or someone else has requested an "
"email change for your user account at %(domain)s.<br> It can be safely "
"ignored if you did not request an email change. Click the link below to "
"confirm new email address."
msgstr ""

#: templates/templated_email/compiled/set_customer_password.html:182
#, python-format
msgctxt "Set password for customer e-mail text"
msgid ""
"You're receiving this e-mail because you have to set password for your "
"customer account at %(domain)s.<br> Click the link below to set up your "
"password."
msgstr ""

#: templates/templated_email/compiled/set_password.html:182
#, python-format
msgctxt "Set password for staff member e-mail text"
msgid ""
"You're receiving this e-mail because you have to set password for your staff "
"member account at %(domain)s.<br> Click the link below to set up your "
"password."
msgstr ""

#: templates/templated_email/compiled/staff_confirm_order.html:183
#, python-format
msgctxt "Order confirmation staff e-mail text"
msgid ""
"Someone placed a new order in your store. To see order details please visit: "
"<a href=\"%(order_details_url)s\">%(order_details_url)s</a>"
msgstr ""

#: templates/templated_email/compiled/update_fulfillment.html:182
msgctxt "Fulfillment update email text"
msgid ""
"Your shipping status has been updated. Below is the list of ordered products "
"that have been updated with new tracking number."
msgstr ""

#: templates/templated_email/compiled/update_fulfillment.html:186
#, python-format
msgctxt "Fulfillment update email text"
msgid "You can track your shipment with %(tracking_number)s code."
msgstr ""

#~ msgctxt "Form field"
#~ msgid "Email"
#~ msgstr "Barua pepe"

#~ msgctxt "Password"
#~ msgid "Password"
#~ msgstr "Neno la siri"

#~ msgctxt "Email"
#~ msgid "Email"
#~ msgstr "Barua pepe"

#~ msgctxt "Registration error"
#~ msgid "This email has already been registered."
#~ msgstr "Barua pepe hii tayari imesajiliwa"

#~ msgctxt "Customer form: Given name field"
#~ msgid "Given name"
#~ msgstr "Jina la kupewa"

#~ msgctxt "Customer form: Family name field"
#~ msgid "Family name"
#~ msgstr "Jina la ukoo"

#~ msgctxt "Address field"
#~ msgid "Area"
#~ msgstr "Eneo"

#~ msgctxt "Address field"
#~ msgid "County"
#~ msgstr "Nchi"

#~ msgctxt "Address field"
#~ msgid "Department"
#~ msgstr "Idara"

#~ msgctxt "Address field"
#~ msgid "District"
#~ msgstr "Wilaya"

#~ msgctxt "Address field"
#~ msgid "Island"
#~ msgstr "Kisiwa"

#~ msgctxt "Personal name"
#~ msgid "Given name"
#~ msgstr "Jina la kupewa"

#~ msgctxt "Personal name"
#~ msgid "Family name"
#~ msgstr "Jina la ukoo"

#~ msgctxt "City area"
#~ msgid "District"
#~ msgstr "Wilaya"

#~ msgctxt "Address form field label"
#~ msgid "Email"
#~ msgstr "Barua pepe"

#~ msgctxt "Customers table header"
#~ msgid "Email"
#~ msgstr "Barua pepe"

#~ msgctxt "Staff members table header"
#~ msgid "Email"
#~ msgstr "Barua pepe"
