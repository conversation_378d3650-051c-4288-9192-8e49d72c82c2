from django.db import models

from .. import settings
from ..core.models import AuditModel


class Message(models.Model):
    content = models.CharField(max_length=2000, null=True, blank=True)

    created = models.DateTimeField(auto_now_add=True)

    seen_date = models.DateTimeField(null=True, blank=True)

    sender = models.ForeignKey(settings.AUTH_USER_MODEL, null=False, blank=False,
                               on_delete=models.CASCADE,
                               related_name='sent_messages', db_index=True)

    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, null=True, blank=True,

                                  on_delete=models.CASCADE,
                                  related_name='received_messages', db_index=True)

    branch = models.ForeignKey("vendor.Branch", null=False, blank=False,
                               on_delete=models.CASCADE,
                               related_name='messages', db_index=True)

    attachments = models.ManyToManyField(
        "chat.Attachment",
        blank=True,
        related_name="messages",
        through='chat.MessageAttachment',
        through_fields=('message', 'attachment')
    )

    order_id = models.IntegerField(null=True, blank=True)

    class Meta:
        ordering = ("-pk",)


class Attachment(AuditModel):
    file = models.CharField(max_length=256, blank=False, null=False)
    alt = models.CharField(max_length=128, blank=True)
    content_type = models.CharField(max_length=128, blank=False, null=False)


class MessageAttachment(models.Model):
    attachment = models.ForeignKey(Attachment, related_name="messageattachment",
                                   on_delete=models.CASCADE)

    message = models.ForeignKey(Message, related_name="messageattachment",
                                on_delete=models.CASCADE)

    sort_order = models.IntegerField(editable=False, db_index=True,
                                     null=False, blank=False)

    class Meta:
        ordering = ("sort_order", "pk")


class MeetingPlatformChatLink(models.Model):
    participant_1_meeting_platform_id = models.CharField(
        max_length=255,
        null=False,
        blank=False,
    )
    participant_2_meeting_platform_id = models.CharField(
        max_length=255,
        null=False,
        blank=False,
    )
    chat_link = models.TextField(null=False, blank=False)
    chat_id = models.BigIntegerField(null=False, blank=False, unique=True)
    chat_token = models.CharField(max_length=255, null=False, blank=False)

    class Meta:
        unique_together = (
            ("participant_1_meeting_platform_id", "participant_2_meeting_platform_id"),)
