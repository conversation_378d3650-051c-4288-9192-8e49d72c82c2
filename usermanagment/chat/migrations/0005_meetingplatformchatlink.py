# Generated by Django 3.2.12 on 2022-11-23 09:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0004_alter_attachment_file'),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetingPlatformChatLink',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('participant_1_meeting_platform_id', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('participant_2_meeting_platform_id', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('chat_link', models.TextField()),
                ('chat_id', models.BigIntegerField(unique=True)),
                ('chat_token', models.Char<PERSON>ield(max_length=255)),
            ],
            options={
                'unique_together': {('participant_1_meeting_platform_id', 'participant_2_meeting_platform_id')},
            },
        ),
    ]
