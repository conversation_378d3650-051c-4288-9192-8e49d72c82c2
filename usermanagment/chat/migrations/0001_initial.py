# Generated by Django 3.0.6 on 2022-01-02 12:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('vendor', '0003_auto_20220102_1413'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(upload_to='attachments')),
                ('alt', models.CharField(blank=True, max_length=128)),
                ('content_type', models.CharField(max_length=128)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('seen_date', models.DateTimeField(blank=True, null=True)),
                ('order_id', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'ordering': ('-pk',),
                'permissions': (('manage_chat', 'Manage chat.'),),
            },
        ),
        migrations.CreateModel(
            name='MessageAttachment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(db_index=True, editable=False)),
                ('attachment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messageattachment', to='chat.Attachment')),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messageattachment', to='chat.Message')),
            ],
            options={
                'ordering': ('sort_order', 'pk'),
            },
        ),
        migrations.AddField(
            model_name='message',
            name='attachments',
            field=models.ManyToManyField(blank=True, related_name='messages', through='chat.MessageAttachment', to='chat.Attachment'),
        ),
        migrations.AddField(
            model_name='message',
            name='branch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='vendor.Branch'),
        ),
        migrations.AddField(
            model_name='message',
            name='recipient',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='message',
            name='sender',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL),
        ),
    ]
