"""ASGI config for usermanagment project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/howto/deployment/asgi/
"""

import os

from channels.routing import ProtocolTypeRouter, URLRouter
from django.core.asgi import get_asgi_application
from django.urls import path

from usermanagment.webscoket_config import GraphqlWsConsumer, WebSocketAuthMiddleware

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "usermanagment.settings")

application = ProtocolTypeRouter({
    'http': get_asgi_application(),
    'websocket': WebSocketAuthMiddleware(URLRouter([
        path('graphql/', GraphqlWsConsumer.as_asgi()),
    ]))
})
