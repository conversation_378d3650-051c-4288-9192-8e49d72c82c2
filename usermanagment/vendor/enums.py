from django.db.models import TextChoices


class DayOfWeekEnum:
    Saturday = "SAT"
    Sunday = "SUN"
    Monday = "MON"
    Tuesday = "TUE"
    Wednesday = "WED"
    Thursday = "THU"
    Friday = "FRI"

    CHOICES = [
        (Saturday, "SAT"),
        (Sunday, "SUN"),
        (Monday, "MON"),
        (Tuesday, "TUE"),
        (Wednesday, "WED"),
        (Thursday, "THU"),
        (Friday, "FRI"),
    ]

    _ = {
        0: Monday,
        1: Tuesday,
        2: Wednesday,
        3: Thursday,
        4: Friday,
        5: Saturday,
        6: Sunday,
    }

    @classmethod
    def from_python_weekday(cls, weekday):
        return cls._[weekday]


class PriceRangeEnum:
    VERY_CHEAP = "very_cheap"
    CHEAP = "cheap"
    AVERAGE = "average"
    EXPENSIVE = "expensive"
    VERY_EXPENSIVE = "very_expensive"

    CHOICES = [
        (VERY_CHEAP, "very_cheap"),
        (CHEAP, "cheap"),
        (AVERAGE, "average"),
        (EXPENSIVE, "expensive"),
        (VERY_EXPENSIVE, "very_expensive"),
    ]


class VendorTypes(TextChoices):
    HOSPITAL = "Hospital"
    PHARMACY = "Pharmacy"
    CLINIC = "Clinic"
    DIAGNOSTIC_CENTER = "Diagnostic Center"

class VendorBranchTypes(TextChoices):
    HOSPITAL = "Hospital"
    PHARMACY = "Pharmacy"
    CLINIC = "Clinic"
    DIAGNOSTIC_CENTER = "Diagnostic Center"


ALLOWED_VENDOR_BRANCH_TYPES = {
    VendorTypes.HOSPITAL: [VendorBranchTypes.HOSPITAL,VendorBranchTypes.PHARMACY],
    VendorTypes.PHARMACY: [VendorBranchTypes.PHARMACY],
    VendorTypes.CLINIC: [VendorBranchTypes.CLINIC],
    VendorTypes.DIAGNOSTIC_CENTER: [VendorBranchTypes.DIAGNOSTIC_CENTER],
}
