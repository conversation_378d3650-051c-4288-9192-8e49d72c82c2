# Generated by Django 3.2.25 on 2025-06-02 15:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0050_vendor_support_immediate_call'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserNetworkView',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField()),
                ('branch_id', models.BigIntegerField()),
            ],
            options={
                'db_table': 'user_network_view',
                'managed': False,
            },
        ),
    ]
