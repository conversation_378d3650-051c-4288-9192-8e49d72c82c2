# Generated by Django 3.2.12 on 2022-08-21 09:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0018_alter_branch_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='BranchTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=10)),
                ('name', models.CharField(max_length=255)),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='vendor.branch')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
