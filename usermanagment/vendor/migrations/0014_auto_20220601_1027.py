# Generated by Django 3.2.12 on 2022-06-01 10:27

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0013_branch_contact_email'),
    ]

    operations = [
        migrations.AlterField(
            model_name='branch',
            name='type',
            field=usermanagment.core.db.fields.EnumField(choices=[('Pharmacy', 'Pharmacy'), ('Reference Pharmacy', 'Centralized'), ('Partner Warehouse', 'Partner Warehouse')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='branchworkinghour',
            name='day',
            field=usermanagment.core.db.fields.EnumField(choices=[('SAT', 'SAT'), ('SUN', 'SUN'), ('MON', 'MON'), ('TUE', 'TUE'), ('WED', 'WED'), ('THU', 'THU'), ('FRI', 'FRI')], max_length=3),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='price_range',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('very_cheap', 'very_cheap'), ('cheap', 'cheap'), ('average', 'average'), ('expensive', 'expensive'), ('very_expensive', 'very_expensive')], max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='type',
            field=usermanagment.core.db.fields.EnumField(choices=[('PHC', 'Phc'), ('Pharmacy', 'Pharmacy'), ('Partner Warehouse', 'Partner Warehouse')], max_length=20, null=True),
        ),
    ]
