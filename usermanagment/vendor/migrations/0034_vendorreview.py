# Generated by Django 3.2.12 on 2023-08-27 11:56

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vendor', '0033_vendor_is_live_booking_integrated'),
    ]

    operations = [
        migrations.CreateModel(
            name='VendorReview',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('rating', models.IntegerField(db_index=True, validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(5)])),
                ('content', models.CharField(blank=True, max_length=255, null=True)),
                ('published', models.BooleanField(db_index=True, default=False)),
                ('created_by', models.ForeignKey(editable=False, null=True,
                                                 on_delete=django.db.models.deletion.SET_NULL,
                                                 related_name='+',
                                                 to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True,
                                                  on_delete=django.db.models.deletion.SET_NULL,
                                                  related_name='+',
                                                  to=settings.AUTH_USER_MODEL)),
                ('vendor',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                   related_name='reviews', to='vendor.vendor')),
            ],
            options={
                'unique_together': {('vendor', 'created_by')},
            },
        ),
    ]
