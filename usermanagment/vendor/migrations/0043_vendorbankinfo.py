# Generated by Django 3.2.12 on 2023-11-27 10:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0042_delete_vendorbankinfo'),
    ]

    operations = [
        migrations.CreateModel(
            name='VendorBankInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(max_length=255)),
                ('account_number', models.Char<PERSON>ield(max_length=255)),
                ('iban', models.Char<PERSON>ield(max_length=255)),
                ('account_name', models.Char<PERSON>ield(max_length=255, null=True)),
                ('vendor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_info', to='vendor.vendor')),
            ],
        ),
    ]
