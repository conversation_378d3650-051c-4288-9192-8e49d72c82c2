# Generated by Django 3.0.6 on 2022-01-02 12:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vendor', '0002_vendor_total_orders_count'),
    ]

    operations = [
        migrations.CreateModel(
            name='BranchConsumer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='vendor.Branch')),
                ('consumer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('branch', 'consumer')},
            },
        ),
        migrations.AddField(
            model_name='branch',
            name='consumers',
            field=models.ManyToManyField(blank=True, related_name='order_branches', through='vendor.BranchConsumer', to=settings.AUTH_USER_MODEL),
        ),
    ]
