# Generated by Django 3.0.6 on 2021-12-28 12:01

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import usermanagment.account.models
import versatileimagefield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('account', '0003_auto_20211228_1401'),
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('seo_title', models.CharField(blank=True, max_length=70, null=True, validators=[django.core.validators.MaxLengthValidator(70)])),
                ('seo_description', models.CharField(blank=True, max_length=300, null=True, validators=[django.core.validators.MaxLengthValidator(300)])),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('contact_number', usermanagment.account.models.PossiblePhoneNumberField(max_length=128, null=True, region=None)),
                ('address', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='branches', to='account.Address')),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'permissions': (('manage_branches', 'Manage branches.'),),
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('seo_title', models.CharField(blank=True, max_length=70, null=True, validators=[django.core.validators.MaxLengthValidator(70)])),
                ('seo_description', models.CharField(blank=True, max_length=300, null=True, validators=[django.core.validators.MaxLengthValidator(300)])),
                ('english_name', models.CharField(max_length=255, unique=True)),
                ('arabic_name', models.CharField(max_length=255, unique=True)),
                ('slug', models.SlugField(allow_unicode=True, max_length=255, unique=True)),
                ('description', models.CharField(blank=True, max_length=2000, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('logo', versatileimagefield.fields.VersatileImageField(blank=True, upload_to='vendors')),
                ('back_ground_image', versatileimagefield.fields.VersatileImageField(blank=True, upload_to='vendors-back-grounds')),
                ('commercial_registration_number', models.CharField(max_length=255, unique=True)),
                ('owner_name', models.CharField(max_length=255)),
                ('national_id', models.CharField(max_length=255, unique=True)),
                ('second_mobile_number', usermanagment.account.models.PossiblePhoneNumberField(blank=True, default='', max_length=128, region=None)),
                ('contact_number', usermanagment.account.models.PossiblePhoneNumberField(max_length=128, null=True, region=None)),
                ('trade_name', models.CharField(max_length=255)),
                ('tax_license_number', models.CharField(max_length=255, unique=True)),
                ('is_vip', models.BooleanField(default=False)),
                ('has_multiple_branches', models.BooleanField(default=False)),
                ('total_ratings', models.IntegerField(default=0)),
                ('ratings_sum', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('approved', models.BooleanField(default=False)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('delivery_min_from', models.IntegerField(default=0)),
                ('delivery_min_to', models.IntegerField(default=0)),
                ('price_range', models.CharField(blank=True, choices=[('very_cheap', 'very_cheap'), ('cheap', 'cheap'), ('average', 'average'), ('expensive', 'expensive'), ('very_expensive', 'very_expensive')], max_length=15, null=True)),
                ('order_acceptance_timeout', models.IntegerField(default=5)),
                ('auto_assign_enabled', models.BooleanField(default=True)),
                ('is_shipping_free', models.BooleanField(default=False)),
                ('order_minimum_free_delivery', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('type', models.CharField(choices=[('Clinic', 'Clinic'), ('GYM', 'Gym'), ('Hospital', 'Hospital'), ('Pharmacy', 'Pharmacy')], max_length=20, null=True)),
                ('address', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendors', to='account.Address')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'permissions': (('manage_vendors', 'Manage vendors.'),),
            },
        ),
        migrations.CreateModel(
            name='VendorRejectionReason',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('reason', models.CharField(max_length=500)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rejections', to='vendor.Vendor')),
            ],
            options={
                'ordering': ('-created',),
            },
        ),
        migrations.CreateModel(
            name='VendorRating',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.DecimalField(decimal_places=2, default=1, max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(5)])),
                ('comment', models.CharField(blank=True, max_length=255, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='vendor.Vendor')),
            ],
        ),
        migrations.CreateModel(
            name='VendorImage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(db_index=True, editable=False, null=True)),
                ('image', versatileimagefield.fields.VersatileImageField(upload_to='vendor-images')),
                ('ppoi', versatileimagefield.fields.PPOIField(default='0.5x0.5', editable=False, max_length=20)),
                ('alt', models.CharField(blank=True, max_length=128)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='vendor.Vendor')),
            ],
            options={
                'ordering': ('sort_order', 'pk'),
            },
        ),
        migrations.CreateModel(
            name='VendorCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category_id', models.IntegerField()),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to='vendor.Vendor')),
            ],
        ),
        migrations.AddField(
            model_name='vendor',
            name='created_by',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='vendor',
            name='deleted_by',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='vendor',
            name='modified_by',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='vendor',
            name='raters',
            field=models.ManyToManyField(blank=True, related_name='vendors_rated', through='vendor.VendorRating', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='CustomerFavoriteVendor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customerfavoritevendor', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customerfavoritevendor', to='vendor.Vendor')),
            ],
        ),
        migrations.CreateModel(
            name='BranchWorkingHourOverride',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('open_time', models.TimeField()),
                ('close_time', models.TimeField()),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_hours_override', to='vendor.Branch')),
            ],
        ),
        migrations.CreateModel(
            name='BranchWorkingHour',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.CharField(choices=[('SAT', 'SAT'), ('SUN', 'SUN'), ('MON', 'MON'), ('TUE', 'TUE'), ('WED', 'WED'), ('THU', 'THU'), ('FRI', 'FRI')], max_length=3)),
                ('open_time', models.TimeField()),
                ('close_time', models.TimeField()),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_hours', to='vendor.Branch')),
            ],
        ),
        migrations.CreateModel(
            name='BranchUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branchuser', to='vendor.Branch')),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branchuser', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='branch',
            name='users',
            field=models.ManyToManyField(blank=True, related_name='branches', through='vendor.BranchUser', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='branch',
            name='vendor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branches', to='vendor.Vendor'),
        ),
        migrations.CreateModel(
            name='VendorBankInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(max_length=255)),
                ('account_number', models.CharField(max_length=255)),
                ('iban', models.CharField(max_length=255)),
                ('vendor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_info', to='vendor.Vendor')),
            ],
            options={
                'unique_together': {('account_number', 'vendor'), ('iban', 'vendor'), ('account_number', 'iban'), ('account_number', 'iban', 'vendor')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='customerfavoritevendor',
            unique_together={('vendor', 'customer')},
        ),
        migrations.AlterUniqueTogether(
            name='branchworkinghouroverride',
            unique_together={('date', 'branch', 'open_time', 'close_time')},
        ),
        migrations.AlterUniqueTogether(
            name='branchworkinghour',
            unique_together={('day', 'branch', 'open_time', 'close_time')},
        ),
        migrations.AlterUniqueTogether(
            name='branchuser',
            unique_together={('branch', 'user')},
        ),
    ]
