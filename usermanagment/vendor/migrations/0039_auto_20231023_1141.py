# Generated by Django 3.2.12 on 2023-10-23 08:41

from django.db import migrations, models
import usermanagment.account.models.possible_phone_number
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0038_auto_20231003_0854'),
    ]

    operations = [
        migrations.AlterField(
            model_name='branch',
            name='accepts_delivery',
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='accepts_pickup',
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='contact_number',
            field=usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(db_index=True, max_length=128, null=True, region=None),
        ),
        migrations.AlterField(
            model_name='branch',
            name='deleted',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name='branch',
            name='health_license_number',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='name',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='name_ar',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='type',
            field=usermanagment.core.db.fields.EnumField(choices=[('Hospital', 'Hospital'), ('Pharmacy', 'Pharmacy'), ('Clinic', 'Clinic'), ('Diagnostic Center', 'Diagnostic Center')], db_index=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='department',
            name='deleted',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='approved',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='commercial_registration_number',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='contact_mobile_number',
            field=usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(blank=True, db_index=True, default='', max_length=128, region=None),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='contact_phone_number',
            field=usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(db_index=True, max_length=128, null=True, region=None),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='deleted',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='delivery_min_from',
            field=models.IntegerField(db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='delivery_min_to',
            field=models.IntegerField(db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='is_active',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='is_integrated',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='is_live_booking_integrated',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='is_vip',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='name',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='name_ar',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='national_id',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='owner_name',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='price_range',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('very_cheap', 'very_cheap'), ('cheap', 'cheap'), ('average', 'average'), ('expensive', 'expensive'), ('very_expensive', 'very_expensive')], db_index=True, max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='vendor',
            name='type',
            field=usermanagment.core.db.fields.EnumField(choices=[('Hospital', 'Hospital'), ('Pharmacy', 'Pharmacy'), ('Clinic', 'Clinic'), ('Diagnostic Center', 'Diagnostic Center')], db_index=True, max_length=20, null=True),
        ),
    ]
