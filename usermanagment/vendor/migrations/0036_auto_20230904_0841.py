# Generated by Django 3.2.12 on 2023-09-04 08:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vendor', '0035_auto_20230829_0723'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('directions', models.TextField(blank=True, null=True)),
                ('branch',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                   related_name='departments', to='vendor.branch')),
                ('created_by', models.ForeignKey(editable=False, null=True,
                                                 on_delete=django.db.models.deletion.SET_NULL,
                                                 related_name='+',
                                                 to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True,
                                                  on_delete=django.db.models.deletion.SET_NULL,
                                                  related_name='+',
                                                  to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DepartmentUser',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(editable=False, null=True,
                                                 on_delete=django.db.models.deletion.SET_NULL,
                                                 related_name='+',
                                                 to=settings.AUTH_USER_MODEL)),
                ('department',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                   related_name='departmentuser',
                                   to='vendor.department')),
                ('modified_by', models.ForeignKey(editable=False, null=True,
                                                  on_delete=django.db.models.deletion.SET_NULL,
                                                  related_name='+',
                                                  to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                           related_name='departmentuser',
                                           to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('department', 'user')},
            },
        ),
        migrations.AddField(
            model_name='department',
            name='users',
            field=models.ManyToManyField(blank=True, related_name='departments',
                                         through='vendor.DepartmentUser',
                                         to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='MedicalService',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=255)),
                ('created_by', models.ForeignKey(editable=False, null=True,
                                                 on_delete=django.db.models.deletion.SET_NULL,
                                                 related_name='+',
                                                 to=settings.AUTH_USER_MODEL)),
                ('department',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                   related_name='medical_services',
                                   to='vendor.department')),
                ('modified_by', models.ForeignKey(editable=False, null=True,
                                                  on_delete=django.db.models.deletion.SET_NULL,
                                                  related_name='+',
                                                  to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('department', 'code')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('branch', 'code'), ('branch', 'name')},
        ),
    ]
