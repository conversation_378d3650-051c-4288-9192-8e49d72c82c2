# Generated by Django 3.2.12 on 2023-10-03 05:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vendor', '0037_vendorhealthpackagesview'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='MedicalService',
            new_name='DepartmentMedicalService',
        ),
        migrations.AddField(
            model_name='department',
            name='deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='department',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='department',
            name='deleted_by',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
    ]
