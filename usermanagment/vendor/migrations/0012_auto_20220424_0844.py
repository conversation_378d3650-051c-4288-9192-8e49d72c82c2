# Generated by Django 3.2.12 on 2022-04-24 08:44

from django.db import migrations, models
import django.db.models.deletion
import usermanagment.account.models.user


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0011_auto_20220310_1142'),
    ]

    operations = [
        migrations.AddField(
            model_name='vendorbankinfo',
            name='account_name',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='VendorManagersContactInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('general_manager_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('purchasing_manager_name', models.CharField(blank=True, max_length=255, null=True)),
                ('purchasing_manager_mobile_number', usermanagment.account.models.user.PossiblePhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('purchasing_manager_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('financial_manager_name', models.CharField(blank=True, max_length=255, null=True)),
                ('financial_manager_mobile_number', usermanagment.account.models.user.PossiblePhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('financial_manager_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('vendor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='managers_contact_info', to='vendor.vendor')),
            ],
        ),
    ]
