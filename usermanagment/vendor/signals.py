from threading import Thread

from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from graphene import Node

from usermanagment.doctor.models import Doctor
from usermanagment.graphql_client.backend_client import BackendClient
from usermanagment.open_search.open_search_service import OpenSearchService
from usermanagment.vendor.models import Vendor


def notify_vendor_change(id):
    BackendClient().notify_vendor_change(id)



def sync_doctors(instance):
    doctors = Doctor.objects.filter(vendor=instance).all()
    OpenSearchService().sync_doctors(doctors)


@receiver(post_save, sender=Vendor)
def open_search_save_sync(sender, instance: Vendor, **kwargs):
    Thread(target=sync_doctors,
           args=(instance,), daemon=False).start()


def delete_doctors(instance):
    doctors = Doctor.objects.filter(vendor=instance)
    for doctor in doctors:
        id = Node.to_global_id("Doctor", doctor.id)
        OpenSearchService().delete(id)


@receiver(post_delete, sender=Vendor)
def open_search_delete_sync(sender, instance: Vendor, **kwargs):
    Thread(target=delete_doctors,
           args=(instance,), daemon=False).start()
