from django.apps import AppConfig

from usermanagment import settings


class VendorAppConfig(AppConfig):
    name = 'usermanagment.vendor'

    def ready(self):
        if settings.LOAD_CUSTOM_SETTINGS and False:
            from usermanagment.account import models
            from usermanagment.auth.enums import AppTypes, AppRoleTypes
            from usermanagment.core.permissions import get_permissions
            from usermanagment.account.utils import create_group
            from usermanagment.core.permissions import VENDOR_ADMIN_AUTH_GROUP_NAME, \
                VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS, \
                PHARMACIST_USER_AUTH_GROUP_NAME, \
                PHARMACIST_USER_AUTH_GROUP_PERMISSIONS

            vendor_admin_users = models.User.objects.filter(
                app_type=AppTypes.VENDOR, app_role=AppRoleTypes.ADMIN
            ).all()
            # create_group(VENDOR_ADMIN_AUTH_GROUP_NAME,
            #              get_permissions(permissions=[perm.value for perm in
            #                              VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS]),
            #              vendor_admin_users)

            vendor_staff_users = models.User.objects.filter(
                app_type=AppTypes.VENDOR, app_role=AppRoleTypes.USER
            )
            # create_group(VENDOR_USER_AUTH_GROUP_NAME,
            #              get_permissions(permissions=[perm.value for perm in
            #                              VENDOR_USER_AUTH_GROUP_PERMISSIONS]),
            #              vendor_staff_users)
