from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import FilteredRelation, Case, Q, When
from django.db.models.fields import Boolean<PERSON>ield
from django.utils import timezone
from .enums import DayOfWeekEnum, PriceRangeEnum, VendorTypes, VendorBranchTypes
from .. import settings
from ..account.models import PossiblePhoneNumberField
from ..core.db.fields import EnumField
from ..core.models import (
    AuditModel,
    SoftDeletionModel,
    SoftDeletionManager,
    SoftDeletionQuerySet,
    SortableModel,
    SeoModel,
    BaseTranslationModel
)
from ..graphql.utils.request_utils import get_current_user


class VendorsQueryManager(SoftDeletionManager):

    def get_queryset(self):
        if self.deleted is not None:
            return VendorQuerySet(self.model).filter(deleted=self.deleted)
        return VendorQuerySet(self.model)


class VendorQuerySet(SoftDeletionQuerySet):

    def accessible_by_user(self):
        user = get_current_user()
        qs = self
        if not user.is_superuser and not user.is_staff:
            qs = qs.filter(is_active=True)
        if user.is_consumer:
            qs = qs.annotate_favorite(user)

        return qs

    def annotate_favorite(self, user):
        qs = self
        if user.is_authenticated and user.is_consumer:
            qs = self.annotate(
                user_favorite=FilteredRelation('customerfavoritevendor',
                                               condition=Q(
                                                   customerfavoritevendor__customer=user))) \
                .annotate(is_favorite=Case(When(user_favorite__isnull=False, then=1),
                                           When(user_favorite__isnull=True, then=0),
                                           default=0,
                                           output_field=BooleanField()
                                           ))

        return qs

class UserNetworkView(models.Model):
    user_id = models.BigIntegerField()
    branch_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'user_network_view'


class Vendor(SeoModel, AuditModel, SoftDeletionModel):
    name = models.CharField(max_length=255, unique=True, db_index=True)
    name_ar = models.CharField(max_length=255, unique=True, null=True, blank=True,
                               db_index=True)
    slug = models.SlugField(max_length=255, allow_unicode=True, unique=True,
                            db_index=True)
    description = models.CharField(max_length=2000, blank=True, null=True)
    is_active = models.BooleanField(default=False, db_index=True)
    logo = models.CharField(max_length=256, null=False, blank=True)
    back_ground_image = models.CharField(max_length=256, null=False, blank=True)

    commercial_registration_number = models.CharField(max_length=255, null=False,
                                                      blank=False, unique=True,
                                                      db_index=True)
    owner_name = models.CharField(max_length=255, null=False, blank=False,
                                  db_index=True)
    national_id = models.CharField(max_length=255, null=False, blank=False, unique=True,
                                   db_index=True)
    contact_mobile_number = PossiblePhoneNumberField(blank=True, default="",
                                                     db_index=True)
    contact_phone_number = PossiblePhoneNumberField(blank=False, null=True,
                                                    db_index=True)
    trade_name = models.CharField(max_length=255, blank=False)
    tax_license_number = models.CharField(max_length=255, blank=False, unique=True)
    is_vip = models.BooleanField(default=False, db_index=True)
    has_multiple_branches = models.BooleanField(default=False)
    call_doctor_now_platform_share = models.DecimalField(decimal_places=2, max_digits=4,
                                                         null=True, blank=True)

    address = models.ForeignKey(
        "account.Address", null=True, on_delete=models.PROTECT,
        related_name="vendors", blank=False)

    approved = models.BooleanField(default=False, db_index=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=True, null=True,
        related_name="+", on_delete=models.SET_NULL
    )
    delivery_min_from = models.IntegerField(default=0, db_index=True)
    delivery_min_to = models.IntegerField(default=0, db_index=True)
    price_range = EnumField(
        max_length=15,
        choices=PriceRangeEnum.CHOICES,
        null=True,
        blank=True,
        db_index=True
    )

    has_own_drivers = models.BooleanField(default=False)

    is_shipping_free = models.BooleanField(default=False)
    order_minimum_free_delivery = models.DecimalField(
        default=0,
        max_digits=settings.DEFAULT_MAX_DIGITS,
        decimal_places=settings.DEFAULT_DECIMAL_PLACES, )

    type = EnumField(
        max_length=20,
        choices=VendorTypes.choices,
        null=True,
        blank=False,
        db_index=True
    )

    total_orders_count = models.DecimalField(default=0, decimal_places=2, max_digits=10)

    objects = VendorsQueryManager()

    division = models.ForeignKey(
        "vendor.Division",
        null=True, blank=True,
        on_delete=models.SET_NULL
    )
    authority_code = models.CharField(max_length=255, null=True, blank=True)
    contact_email = models.EmailField(null=True, blank=True)
    operation_status = models.CharField(max_length=255, null=True, blank=True)
    onboarding_status = models.CharField(max_length=255, null=True, blank=True)
    cluster = models.CharField(max_length=255, null=True, blank=True)
    group = models.CharField(max_length=255, null=True, blank=True)
    virtual_group = models.CharField(max_length=255, null=True, blank=True)
    source = models.CharField(max_length=255, null=True, blank=True)
    org_id_nhic = models.CharField(max_length=255, null=True, blank=True)
    max_number_of_users = models.IntegerField(null=True, blank=True)

    is_integrated = models.BooleanField(default=False, db_index=True)
    enable_visit_details = models.BooleanField(default=False, db_index=True)
    is_live_booking_integrated = models.BooleanField(default=False, db_index=True)
    support_outpatient_journey = models.BooleanField(default=False, db_index=True,
                                                     null=False, blank=False)

    notify_by_email = models.BooleanField(default=False, db_index=True)

    delivery_price = models.IntegerField(default=0, null=False, blank=False)

    edit_like_enabled = models.BooleanField(default=True, db_index=True)

    has_own_payment_gateway = models.BooleanField(default=False, db_index=True)

    support_immediate_call = models.BooleanField(default=True, db_index=True)

    def __str__(self) -> str:
        return self.name

    def get_active_subscription(self):
        return self.subscriptions.filter(is_active=True).first()


class VendorRejectionReason(AuditModel):
    reason = models.CharField(max_length=500, blank=False, null=False)

    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE,
                               blank=False, null=False, related_name="rejections")

    class Meta:
        ordering = ("-created",)


class BranchManager(SoftDeletionManager):
    def accessible_branches(self):
        qs = self
        current_user = get_current_user()
        if not current_user.is_superuser and not current_user.is_staff:
            vendor_id = current_user.vendor_id
            if vendor_id:
                if current_user.is_vendor_admin:
                    qs = qs.filter(vendor_id=current_user.vendor_id)
                else:
                    qs = qs.filter(pk__in=current_user.branches.all())
        return qs


class Branch(SeoModel, AuditModel, SoftDeletionModel):
    name = models.CharField(max_length=255, unique=True, db_index=True)
    name_ar = models.CharField(max_length=255, unique=True, null=True, blank=True,
                               db_index=True)
    description = models.CharField(max_length=2000, blank=True)
    vendor = models.ForeignKey(
        Vendor, on_delete=models.CASCADE, related_name="branches", db_index=True)
    address = models.ForeignKey(
        "account.Address", null=True, on_delete=models.PROTECT,
        related_name="branches", blank=False)

    users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="branches",
        through="BranchUser",
        through_fields=('branch', 'user')
    )

    consumers = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="order_branches",
        through="BranchConsumer",
        through_fields=('branch', 'consumer')
    )

    is_active = models.BooleanField(default=True, db_index=True)

    contact_number = PossiblePhoneNumberField(blank=False, null=True, db_index=True)
    contact_email = models.EmailField(blank=True, null=True)

    type = EnumField(
        max_length=50,
        choices=VendorBranchTypes.choices,
        null=True,
        blank=False,
        db_index=True
    )

    health_license_number = models.CharField(
        max_length=255,
        unique=True,
        null=True,
        blank=True,
        db_index=True
    )
    health_license_start_date = models.DateField(null=True, blank=True)
    health_license_end_date = models.DateField(null=True, blank=True)
    is_trained = models.BooleanField(default=False)
    accepts_delivery = models.BooleanField(default=True, null=False, blank=False,
                                           db_index=True)
    accepts_pickup = models.BooleanField(default=True, null=False, blank=False,
                                         db_index=True)

    preferred_pharmacies = models.ManyToManyField(
        "self",
        blank=True,
        related_name="preferred_pharmacies",
        db_index=True
    )

    objects = BranchManager()

    def __str__(self) -> str:
        return self.name

    def is_open(self):
        now = timezone.localtime(timezone.now())
        day = DayOfWeekEnum.from_python_weekday(now.weekday())
        time = now.time()
        date = now.date()
        working_hours_override = self.working_hours_override.filter(date=date).all()
        if not working_hours_override:
            normal_working_hours = self.working_hours.filter(day=day,
                                                             open_time__lte=time,
                                                             close_time__gt=time).all()
            if normal_working_hours:
                return True
        else:
            for working_hour_override in working_hours_override:
                if working_hour_override.close_time > time >= working_hour_override.open_time:
                    return True

        return False


class BranchWorkingHour(models.Model):
    day = EnumField(
        max_length=3,
        choices=DayOfWeekEnum.CHOICES,
        null=False, blank=False
    )
    open_time = models.TimeField(blank=False, null=False)
    close_time = models.TimeField(blank=False, null=False)

    branch = models.ForeignKey(Branch, related_name="working_hours", null=False,
                               blank=False, on_delete=models.CASCADE)

    class Meta:
        unique_together = (("day", "branch", "open_time", "close_time"),)


class BranchWorkingHourOverride(models.Model):
    date = models.DateField(null=False, blank=False)
    open_time = models.TimeField(blank=False, null=False)
    close_time = models.TimeField(blank=False, null=False)

    branch = models.ForeignKey(Branch, related_name="working_hours_override",
                               null=False, blank=False, on_delete=models.CASCADE)

    class Meta:
        unique_together = (("date", "branch", "open_time", "close_time"),)


class VendorBankInfo(models.Model):
    bank_name = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=255, null=True, blank=True)
    iban = models.CharField(max_length=255, null=True, blank=True)
    account_name = models.CharField(max_length=255, null=True, blank=True)

    vendor = models.OneToOneField(Vendor, related_name="bank_info",
                                  on_delete=models.CASCADE)


class BranchUser(AuditModel):
    branch = models.ForeignKey(
        Branch, related_name="branchuser", on_delete=models.CASCADE)

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="branchuser", on_delete=models.CASCADE)

    class Meta:
        unique_together = (("branch", "user"),)


class CustomerFavoriteVendor(models.Model):
    vendor = models.ForeignKey(
        Vendor, related_name="customerfavoritevendor", on_delete=models.CASCADE
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="customerfavoritevendor",
        on_delete=models.CASCADE)

    class Meta:
        unique_together = (("vendor", "customer"),)


class VendorImage(SortableModel):
    vendor = models.ForeignKey(
        Vendor, related_name="images", on_delete=models.CASCADE
    )
    image = models.CharField(max_length=256, null=False, blank=False)

    alt = models.CharField(max_length=128, blank=True)

    class Meta:
        ordering = ("sort_order", "pk")

    def get_ordering_queryset(self):
        return self.vendor.images.all()


class BranchConsumer(AuditModel):
    branch = models.ForeignKey("vendor.Branch",
                               null=False, blank=False,
                               on_delete=models.CASCADE)

    consumer = models.ForeignKey(settings.AUTH_USER_MODEL,
                                 null=False, blank=False,
                                 on_delete=models.CASCADE)

    class Meta:
        unique_together = ("branch", "consumer")


class Division(AuditModel):
    name = models.CharField(max_length=255, unique=True)


class VendorManagersContactInfo(models.Model):
    general_manager_email = models.EmailField(null=True, blank=True)

    purchasing_manager_name = models.CharField(max_length=255, null=True, blank=True)
    purchasing_manager_mobile_number = PossiblePhoneNumberField(null=True, blank=True)
    purchasing_manager_email = models.EmailField(null=True, blank=True)

    financial_manager_name = models.CharField(max_length=255, null=True, blank=True)
    financial_manager_mobile_number = PossiblePhoneNumberField(null=True, blank=True)
    financial_manager_email = models.EmailField(null=True, blank=True)

    vendor = models.OneToOneField(Vendor, related_name="managers_contact_info",
                                  on_delete=models.CASCADE)


class VendorSpecializationPrice(AuditModel):
    vendor = models.ForeignKey(
        Vendor, related_name="vendor_specialization_price", on_delete=models.CASCADE
    )

    specialization_code = models.CharField(max_length=255, unique=True, blank=False,
                                           null=True)

    price = models.DecimalField(default=0, decimal_places=2, max_digits=10)

    class Meta:
        unique_together = ("vendor", "specialization_code")


# model to store vendor reviews and ratings
class VendorReview(AuditModel):
    vendor = models.ForeignKey(
        Vendor, related_name="reviews", on_delete=models.CASCADE
    )

    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        db_index=True
    )

    content = models.CharField(max_length=255, blank=True, null=True)
    published = models.BooleanField(default=False, db_index=True)

    class Meta:
        unique_together = ("vendor", "created_by")


# model for department
class Department(AuditModel, SoftDeletionModel):
    name = models.CharField(max_length=255, blank=False, null=False)
    code = models.CharField(max_length=255, blank=False, null=False, )
    description = models.TextField(blank=True, null=True)
    directions = models.TextField(blank=True, null=True)

    branch = models.ForeignKey(
        Branch, related_name="departments", on_delete=models.CASCADE
    )

    users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="departments",
        through="DepartmentUser",
        through_fields=('department', 'user')
    )

    class Meta:
        unique_together = ("branch", "name"), ("branch", "code")


class DepartmentUser(AuditModel):
    department = models.ForeignKey(
        Department, related_name="departmentuser", on_delete=models.CASCADE,
        db_index=True)

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="departmentuser",
        on_delete=models.CASCADE, db_index=True)

    class Meta:
        unique_together = (("department", "user"),)


# model for medical services that belongs to department
class DepartmentMedicalService(AuditModel):
    code = models.CharField(max_length=255)
    department = models.ForeignKey(
        Department, related_name="medical_services", on_delete=models.CASCADE
    )

    class Meta:
        unique_together = ("department", "code")


class VendorHealthPackagesView(models.Model):
    vendor_id = models.BigIntegerField()
    health_package_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'vendor_health_packages_view'
