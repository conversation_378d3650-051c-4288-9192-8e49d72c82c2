import graphene
from django.core.exceptions import ValidationError
from django.db import transaction
from ...core.mutations import ModelMutation, ModelDeleteMutation
from ...core.types.common import BlockError, LocationInput
from ....auth.permissions import BlockPermissions
from ....block import models


class BlockInput(graphene.InputObjectType):
    code = graphene.String(description="Block Code.")
    name = graphene.String(description="Block Name.")
    name_ar = graphene.String(description="Block Name arabic")
    coordinates = graphene.List(graphene.NonNull(LocationInput),
                                description="Block area coordinates")

    city = graphene.ID(description="block city")


class BlockCreate(ModelMutation):
    class Arguments:
        input = BlockInput(required=True,
                           description="Fields required to create a block.")

    class Meta:
        description = "Creates a new block."
        model = models.Block
        permissions = (BlockPermissions.MANAGE_BLOCKS,)
        error_type_class = BlockError
        error_type_field = "block_errors"

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):
        super().save(info, instance, cleaned_input)


class BlockUpdate(BlockCreate):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a block to update.")
        input = BlockInput(required=True,
                           description="Fields required to update a block.")

    class Meta:
        description = "Updates an existing block."
        model = models.Block
        permissions = (BlockPermissions.MANAGE_BLOCKS,)
        error_type_class = BlockError
        error_type_field = "block_errors"


class BlockDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of block to delete.")

    class Meta:
        description = "Delete an existing block."
        model = models.Block
        permissions = (BlockPermissions.MANAGE_BLOCKS,)
        error_type_class = BlockError
        error_type_field = "block_errors"
