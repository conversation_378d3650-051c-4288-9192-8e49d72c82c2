import graphene
from django.contrib.sites.models import Site
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import ProtectedError

from ...core.mutations import ModelMutation, ModelDeleteMutation
from ...core.scalars import Decimal
from ...core.types.common import BlockError
from ...vendor.enums import VendorBranchTypeEnum
from ....auth.permissions import BlockPermissions
from ....block import models


class RoundInput(graphene.InputObjectType):
    radius = Decimal(description="round radius in KM", required=True)
    max_number_of_pharmacies = graphene.Int(required=True)
    pharmacies_types = graphene.List(graphene.NonNull(VendorBranchTypeEnum), required=True)


class CityInput(graphene.InputObjectType):
    rounds = graphene.List(RoundInput)

    code = graphene.String(description="City Code.")

    name = graphene.String(description="City Name.")

    name_ar = graphene.String(description="City Name arabic")

    max_number_of_rounds = graphene.Int()

    max_number_of_tries = graphene.Int()

    time_out_period = graphene.Int(description="time out period in minutes")

    area = graphene.String(description="City Area")


class CityCreate(ModelMutation):
    class Arguments:
        input = CityInput(required=True,
                          description="Fields required to create a city.")

    class Meta:
        description = "Creates a new city."
        model = models.City
        permissions = (BlockPermissions.MANAGE_CITIES,)
        error_type_class = BlockError
        error_type_field = "city_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data)

        cls.clean_expansion_range_settings(instance, cleaned_data)

        return cleaned_data

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):
        super().save(info, instance, cleaned_input)

        rounds = cleaned_input.get("rounds", [])
        if rounds:
            instance.rounds.all().delete()
        for city_round in rounds:
            city_round.city = instance
            city_round.save()

    @classmethod
    def clean_expansion_range_settings(cls, instance, cleaned_input):
        site_settings = Site.objects.get_current().settings
        has_max_number_of_rounds = 'max_number_of_rounds' in cleaned_input
        cleaned_input['max_number_of_rounds'] = cleaned_input.pop(
            "max_number_of_rounds",
            instance.max_number_of_rounds or site_settings.range_expansion_max_number_of_rounds
        )
        cleaned_input['max_number_of_tries'] = cleaned_input.pop(
            "max_number_of_tries",
            instance.max_number_of_tries or site_settings.range_expansion_max_number_of_tries
        )
        cleaned_input['time_out_period'] = cleaned_input.pop(
            "time_out_period",
            instance.time_out_period or site_settings.range_expansion_time_out_period
        )
        rounds = cleaned_input.pop('rounds', None)
        cleaned_rounds = []
        if has_max_number_of_rounds:
            if not rounds:
                raise ValidationError({
                    "rounds": "this field is required"
                })
            if len(rounds) > cleaned_input['max_number_of_rounds']:
                raise ValidationError({
                    "rounds": "rounds count should not be greater than max_number_of_rounds"
                })

            for city_round in rounds:
                cleaned_rounds.append(
                    models.CityRound(
                        radius=city_round['radius'],
                        max_number_of_pharmacies=city_round[
                            'max_number_of_pharmacies'],
                        pharmacies_types=city_round['pharmacies_types']
                    )
                )

        elif not instance.max_number_of_rounds:
            for i in range(site_settings.range_expansion_max_number_of_rounds):
                cleaned_rounds.append(cls.get_default_round())

        if not cleaned_rounds:
            return

        cleaned_input['rounds'] = cleaned_rounds

        # ISSUE-2174: https://app.clickup.com/t/7542143/ISSUE-2174?comment=961070082
        round_0_pharmacies_count = cleaned_input['rounds'][0].max_number_of_pharmacies
        total_number_of_rounds = (
                cleaned_input['max_number_of_tries'] / round_0_pharmacies_count)

        if total_number_of_rounds < 1:
            raise ValidationError({
                "rounds": "(max_number_of_tries/round_0_pharmacies_count) should be "
                          "greater or equal to 1 "
            })

        if total_number_of_rounds > cleaned_input['max_number_of_rounds']:
            raise ValidationError({
                "rounds": "total number of rounds should not be greater than "
                          "maxNumberOfRounds "
            })

    @classmethod
    def get_default_round(cls):
        site_settings = Site.objects.get_current().settings
        return models.CityRound(
            radius=site_settings.range_expansion_round_radius,
            max_number_of_pharmacies=site_settings.range_expansion_round_max_number_of_pharmacies,
            pharmacies_types=site_settings.range_expansion_round_pharmacies_types
        )


class CityUpdate(CityCreate):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a city to update.")
        input = CityInput(required=True,
                          description="Fields required to update a city.")

    class Meta:
        description = "Updates an existing city."
        model = models.City
        permissions = (BlockPermissions.MANAGE_CITIES,)
        error_type_class = BlockError
        error_type_field = "city_errors"


class CityDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of city to delete.")

    class Meta:
        description = "Delete an existing city."
        model = models.City
        permissions = (BlockPermissions.MANAGE_CITIES,)
        error_type_class = BlockError
        error_type_field = "city_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        raise ValidationError("Mutation is deprecated do not call")

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        try:
            result = super().perform_mutation(_root, info, **data)
        except ProtectedError:
            raise ValidationError({
                "id": "City is in use, it cant be deleted"
            })
        return result
