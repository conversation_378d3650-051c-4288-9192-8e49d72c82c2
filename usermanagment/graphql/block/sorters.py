import graphene

from ..core.types import SortInputObjectType


class CityOrderField(graphene.Enum):
    NAME = ["name"]
    CREATED = ["created", "pk"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            CityOrderField.NAME.name: "name",
            CityOrderField.CREATED.name: "creation date",
        }
        if self.name in descriptions:
            return f"Sort cities by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.value)


class CityOrder(SortInputObjectType):
    field = graphene.Argument(
        CityOrderField, description="Sort cities by the selected field."
    )

    class Meta:
        sort_enum = CityOrderField


class BlockOrderField(graphene.Enum):
    NAME = ["name"]
    CREATED = ["created", "pk"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            BlockOrderField.NAME.name: "name",
            BlockOrderField.CREATED.name: "creation date",
        }
        if self.name in descriptions:
            return f"Sort blocks by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.value)


class BlockOrder(SortInputObjectType):
    field = graphene.Argument(
        BlockOrderField, description="Sort blocks by the selected field."
    )

    class Meta:
        sort_enum = BlockOrderField
