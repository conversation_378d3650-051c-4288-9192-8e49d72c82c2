import django_filters
from django.contrib.gis.db.models.functions import Intersection
from django.contrib.gis.geos import Point
from django.db.models import F

from ..core.filters import ObjectTypeFilter, filter_entities_by_name__icontains, \
    filter_entities_by_name
from ..core.types import FilterInputObjectType
from ..core.types.common import LocationInput
from ..utils import resolve_global_ids_to_primary_keys
from ...block.models import Block, City
from ...map.postgis.functions import IsEmpty


class CityFilter(django_filters.FilterSet):
    name__icontains = django_filters.CharFilter(
        method=filter_entities_by_name__icontains)
    name = django_filters.CharFilter(method=filter_entities_by_name)

    class Meta:
        model = City
        fields = {}


class CityFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = CityFilter


def filter_by_location(qs, _, value):
    if value:
        location = Point(x=value.lng, y=value.lat, srid=4326)
        qs = qs.annotate(
            is_empty_intersection=IsEmpty(Intersection(F('geo_coordinates'), location))
        ).filter(is_empty_intersection=False)

    return qs


def filter_by_city(qs, _, city_id):
    if city_id:
        try:
            _, pk = resolve_global_ids_to_primary_keys([city_id], "City")
            qs = qs.filter(city_id__in=pk)
        except Exception:
            # If global ID conversion fails, return empty queryset
            qs = qs.none()
    return qs


class BlockFilter(django_filters.FilterSet):
    location = ObjectTypeFilter(
        input_class=LocationInput,
        method=filter_by_location,
    )

    name__icontains = django_filters.CharFilter(
        method=filter_entities_by_name__icontains)
    name = django_filters.CharFilter(method=filter_entities_by_name)

    city = django_filters.CharFilter(
        method=filter_by_city
    )

    class Meta:
        model = Block
        fields = {}


class BlockFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = BlockFilter
