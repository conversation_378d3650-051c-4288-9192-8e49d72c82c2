import re

from django.core.exceptions import ValidationError

from ...graphql.core.enums import BlockErrorCode


def validate_coordinates(lng_field, lng, lat_field, lat):
    LONGITUDE = '^(\+|-)?(?:180(?:(?:\.0{1,6})?)|(?:[0-9]|[1-9][0-9]|1[0-7][0-9])(?:(?:\.[0-9]{1,6})?))$'
    LATITUDE = '^(\+|-)?(?:90(?:(?:\.0{1,6})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,6})?))$'

    if not re.compile(LONGITUDE).search(str(lng)):
        raise ValidationError(
            {
                lng_field: ValidationError(
                    f"Block field {lng_field} is invalid longitude",
                    code=BlockErrorCode.INVALID.value,
                )
            }
        )

    if not re.compile(LATITUDE).search(str(lat)):
        raise ValidationError(
            {
                lat_field: ValidationError(
                    f"Block field {lat_field} is invalid latitude",
                    code=BlockErrorCode.INVALID.value,
                )
            }
        )
