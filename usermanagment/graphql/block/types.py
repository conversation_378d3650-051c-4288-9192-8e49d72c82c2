import graphene
from graphene_federation import key
from ..core.connection import CountableDjangoObjectType
from ..core.types.common import LocationType
from ..vendor.enums import VendorBranchTypeEnum
from ...block import models


@key(fields="id")
class City(CountableDjangoObjectType):
    rounds = graphene.List(lambda: CityRound)

    class Meta:
        description = "Represents an individual city"
        interfaces = [graphene.relay.Node]
        model = models.City

    @staticmethod
    def resolve_rounds(root, _info):
        return root.rounds.all()

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, City)


class CityRound(CountableDjangoObjectType):
    pharmacies_types = graphene.List(VendorBranchTypeEnum)

    class Meta:
        model = models.CityRound
        interfaces = [graphene.relay.Node]


class Block(CountableDjangoObjectType):
    coordinates = graphene.List(LocationType,
                                description="Address location in lng/lat")

    class Meta:
        description = "Represents an individual block"
        interfaces = [graphene.relay.Node]
        model = models.Block
