import graphene

from .filters import BlockFilterInput, CityFilterInput
from .mutations.blocks import BlockCreate, BlockUpdate, BlockDelete
from .mutations.cities import CityCreate, CityUpdate, CityDelete
from .resolvers import resolve_blocks, resolve_cities
from .sorters import <PERSON><PERSON>rder, CityOrder
from .types import Block, City
from ..core.fields import FilterInputConnectionField


class BlockQueries(graphene.ObjectType):
    city = graphene.Field(
        City,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the city.",
            required=True),
        description="Look up a city by ID.",
    )

    cities = FilterInputConnectionField(
        City,
        filter=CityFilterInput(description="Filtering options for cities."),
        sort_by=CityOrder(description="Sort cities."),
        description="List of the site city.",
    )

    block = graphene.Field(
        Block,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the block.",
            required=True),
        description="Look up a block by ID.",
    )

    blocks = FilterInputConnectionField(
        Block,
        filter=BlockFilterInput(description="Filtering options for blocks."),
        sort_by=BlockOrder(description="Sort blocks."),
        description="List of the shop's blocks.",
    )

    def resolve_city(self, info, id, **_args):
        return graphene.Node.get_node_from_global_id(info, id, City)

    def resolve_cities(self, info, **kwargs):
        return resolve_cities(info, **kwargs)

    def resolve_block(self, info, id, **_args):
        return graphene.Node.get_node_from_global_id(info, id, Block)

    def resolve_blocks(self, info, **kwargs):
        return resolve_blocks(info, **kwargs)


class BlockMutations(graphene.ObjectType):
    city_create = CityCreate.Field()
    city_update = CityUpdate.Field()
    city_delete = CityDelete.Field()

    block_create = BlockCreate.Field()
    block_update = BlockUpdate.Field()
    block_delete = BlockDelete.Field()
