import os

import graphene
from graphene_federation import key

from ..core.connection import CountableDjangoObjectType
from ..federation.external_field import ExternalObject<PERSON>ield
from ...chat import models


class Message(CountableDjangoObjectType):
    attachments = graphene.List(lambda: Attachment,
                                description="list of message attachments")

    order = ExternalObjectField("usermanagment.graphql.federation.types.Order")

    class Meta:
        description = "Represents an individual chat message"
        interfaces = [graphene.relay.Node]
        model = models.Message

    @staticmethod
    def resolve_attachments(root, info, **kwargs):
        return root.attachments.all()


@key(fields="id")
class Attachment(CountableDjangoObjectType):
    sort_order = graphene.Int(description="attachment sort order")
    file = graphene.String(description="the attachment file url")

    class Meta:
        description = "Represents an attachment"
        interfaces = [graphene.relay.Node]
        model = models.Attachment

    @staticmethod
    def resolve_file(root, info, **kwargs):
        return root.file
