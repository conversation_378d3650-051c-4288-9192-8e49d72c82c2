import graphene
from django.core.exceptions import ValidationError
from django.utils import timezone

from .subscriptions import MessageSent
from .types import Attachment
from ..core.mutations import ModelMutation, BaseMutation
from ..core.types.common import ChatError
from ..utils.request_utils import get_current_user
from ...auth.exceptions import PermissionDenied
from ...chat import models
from usermanagment.kafka.topics import KafkaTopics
from graphene import Node
from ...workflow.rest_client import Workflow<PERSON><PERSON>
from django.db.models import Q


class AttachmentInput(graphene.InputObjectType):
    alt = graphene.String(description="Alt text for an image.")
    file = graphene.String(
        required=True,
        description="Represents  file",
    )
    content_type = graphene.String(
        required=True,
        description="content type of a file",
    )


class MessageAttachmentInput(graphene.InputObjectType):
    sort_order = graphene.Int(required=True,
                              description="display order of this attachment")
    attachment = graphene.ID(required=True, description="attachment id")


class MessageInput(graphene.InputObjectType):
    content = graphene.String(description="message text content")
    attachments = graphene.List(MessageAttachmentInput,
                                description="message image attachments")
    branch = graphene.ID(description="branch ID to chat with.")


class VendorMessageInput(MessageInput):
    recipient = graphene.ID(description="customer to send message to.")


class CustomerSendMessage(ModelMutation):
    class Arguments:
        input = MessageInput(
            required=True,
            description="Fields required for customer to send a message."
        )

    class Meta:
        description = "Creates a new customer message."
        model = models.Message
        error_type_class = ChatError
        error_type_field = "chat_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_authenticated:
            raise PermissionDenied()

        return {'sender': info.context.user}

    @classmethod
    def clean_attachments(cls, info, _instance, data):
        attachments_data = data.pop('attachments', [])
        attachments = []
        for attachment in attachments_data:
            attachment_instance = cls.get_node_or_error(info,
                                                        attachment.get('attachment'),
                                                        only_type=Attachment)
            message_attachment = models.MessageAttachment(
                sort_order=attachment['sort_order'],
                attachment=attachment_instance)
            attachments.append(message_attachment)
        return attachments

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        if result.message:
            attachments = result.message.attachments.all() if result.message.attachments.all() else []
            MessageSent.broadcast(
                topic= KafkaTopics.CHAT_TOPIC,
                payload={
                    'id': Node.to_global_id("Message",result.message.id),
                    'createdDate': str(result.message.created),
                    'content': result.message.content,
                    'branch':{
                        "id": Node.to_global_id("Branch",result.message.branch_id) if result.message.branch_id else None,
                    },
                    'vendor':{
                        "id": Node.to_global_id("Vendor",result.message.branch.vendor_id) if result.message.branch and result.message.branch.vendor else None,
                    },
                    'sender':{
                        "id": Node.to_global_id("User",result.message.sender_id),
                    },
                    'recipient':{
                        "id":  Node.to_global_id("User",result.message.recipient_id) if result.message.recipient_id else None,
                    },
                    'attachments': [{
                        "file": attachment.file,
                        "alt": attachment.alt,
                        "contentType": attachment.content_type,
                    } for attachment in attachments
                    ]
                })
        return result

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data)

        content = cleaned_data.get('content')
        attachments = cls.clean_attachments(info, instance, data)
        cleaned_data['attachments'] = attachments

        if not content and not attachments:
            raise ValidationError("you should provide 'content' or 'attachments'")

        return cleaned_data

    @classmethod
    def _save_m2m(cls, info, instance, cleaned_data):
        attachments = cleaned_data['attachments']
        for attachment in attachments:
            attachment.message = instance
            attachment.save()


class VendorSendMessage(CustomerSendMessage):
    class Arguments:
        input = VendorMessageInput(
            required=True,
            description="Fields required for customer to send a message."
        )

    class Meta:
        description = "Creates a new vendor message."
        model = models.Message
        error_type_class = ChatError
        error_type_field = "chat_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        branch = cls.get_node_or_error(info, data['input'].get('branch'), 'branch')
        current_user = info.context.user
        if current_user.vendor_id != branch.vendor_id or not (
                current_user.is_vendor_admin or branch in current_user.branches.all()):
            raise PermissionDenied()

        return {
            'sender': info.context.user,
        }

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data)

        recipient = cleaned_data.get('recipient')
        if not recipient:
            raise ValidationError({"recipient": "recipient is required"})

        return cleaned_data


class UploadAttachment(ModelMutation):
    class Arguments:
        input = AttachmentInput(required=True,
                                description="Fields required to create attachment.")

    class Meta:
        description = "Creates a new attachment."
        model = models.Attachment
        error_type_class = ChatError
        error_type_field = "chat_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls)
        if not cleaned_data.get("file"):
            raise ValidationError({
                "file": "you must provide a file path"
            })
        return cleaned_data


class CustomerMarkMessagesAsSeen(BaseMutation):
    updated_count = graphene.Int(description="number of messages marked as seen")

    class Arguments:
        branch_id = graphene.String(required=True, description="Branch id.")

    class Meta:
        description = "Mark Customer Received Messages as seen"
        error_type_class = ChatError
        error_type_field = "chat_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, root, info, **data):
        branch = cls.get_node_or_error(info, data['branch_id'],
                                       field='branch_id', only_type="Branch")

        if not branch:
            raise ValidationError({
                "branchId": "this field is required"
            })

        current_user = get_current_user()

        count = current_user.received_messages.filter(branch=branch,
                                                      seen_date__isnull=True) \
            .update(seen_date=timezone.now())

        return CustomerMarkMessagesAsSeen(updated_count=count)


class VendorMarkMessagesAsSeen(BaseMutation):
    updated_count = graphene.Int(description="number of messages marked as seen")

    class Arguments:
        branch_id = graphene.String(required=True, description="Branch id.")
        customer_id = graphene.String(required=True, description="Customer id.")

    class Meta:
        description = "Mark Branch Received Messages as seen"
        error_type_class = ChatError
        error_type_field = "chat_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        current_user = get_current_user()

        if not (current_user.is_authenticated
                and current_user.vendor_id == instance.vendor_id
                and (current_user.is_vendor_admin
                     or current_user in instance.users.all())):
            raise PermissionDenied()

        return {}

    @classmethod
    def perform_mutation(cls, root, info, **data):
        branch = cls.get_node_or_error(info, data['branch_id'],
                                       field='branch_id', only_type="Branch")

        customer = cls.get_node_or_error(info, data['customer_id'],
                                         field='customer_id', only_type="User")

        if not branch:
            raise ValidationError({
                "branchId": "this field is required"
            })

        if not customer:
            raise ValidationError({
                "customerId": "this field is required"
            })

        cls.check_authorization(root, info, branch, **data)

        count = branch.messages.filter(recipient__isnull=True,
                                       sender=customer,
                                       seen_date__isnull=True) \
            .update(seen_date=timezone.now())

        return VendorMarkMessagesAsSeen(updated_count=count)
