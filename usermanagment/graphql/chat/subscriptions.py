import graphene
from django.db.models import Q

from ..chat.types import Message
from ..core.subscriptions import BaseSubscription
from ...auth.enums import AppTypes, AppRoleTypes
from ...core.permissions import ChatPermissions


class MessageSent(BaseSubscription):
    message = graphene.Field(Message)

    @classmethod
    def check_authorization(cls, payload, info):
        current_user = info.context.user
        message = payload['message']

        if message.recipient and message.recipient == current_user:
            return True
        elif message.sender and message.sender == current_user:
            return True
        elif not message.recipient:
            branch_users = message.branch.vendor.users.filter(
                Q(app_type=AppTypes.VENDOR, app_role=AppRoleTypes.ADMIN) | Q(
                    branches=message.branch))

            if current_user in branch_users and \
                    current_user.has_perm(ChatPermissions.MANAGE_CHAT):
                return True

        return False

    @classmethod
    def perform_publish(cls, payload, info):
        return MessageSent(message=payload['message'])
