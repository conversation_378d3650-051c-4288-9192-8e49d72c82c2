import graphene

from .mutations import CustomerSendMessage, VendorSendMessage, UploadAttachment, \
    CustomerMarkMessagesAsSeen, VendorMarkMessagesAsSeen
from .subscriptions import MessageSent


class ChatQueries(graphene.ObjectType):
    pass


class ChatMutations(graphene.ObjectType):
    customer_send_message = CustomerSendMessage.Field()
    vendor_send_message = VendorSendMessage.Field()
    upload_attachment = UploadAttachment.Field()

    customer_mark_messages_as_seen = CustomerMarkMessagesAsSeen.Field()
    vendor_mark_messages_as_seen = VendorMarkMessagesAsSeen.Field()


class ChatSubscriptions(graphene.ObjectType):
    message_sent = MessageSent.Field()
