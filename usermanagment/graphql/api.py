from graphene_federation import build_schema

from .all_graphql_types import *  # noqa
from .account.schema import AccountMutations, AccountQueries
from .block.schema import BlockQueries, BlockMutations
from .branch.schema import BranchQueries, BranchMutations
from .chat.schema import ChatMutations, ChatSubscriptions
from .complaint.schema import ComplaintMutations, ComplaintQueries
from .core.schema import CoreQueries, CoreMutations
from .feature.schema import FeatureMutations, FeatureQueries
from .payer.schema import PayerQueries, PayerMutations
from .patient.schema import PatientQueries, PatientMutations
from .site.schema import UserManagementSiteSettingsQueries, \
    UserManagementSiteSettingsMutations
from .subscription.schema import PlanQueries, PlanMutations
from .vendor.schema import VendorQueries, VendorMutations
from .doctor.schema import DoctorQueries, DoctorMutations, DoctorSubscriptions


class Query(
    AccountQueries,
    CoreQueries,
    BlockQueries,
    VendorQueries,
    BranchQueries,
    PlanQueries,
    PatientQueries,
    DoctorQueries,
    PayerQueries,
    UserManagementSiteSettingsQueries,
    FeatureQueries,
    ComplaintQueries,
):
    pass


class Mutation(
    AccountMutations,
    CoreMutations,
    BlockMutations,
    VendorMutations,
    BranchMutations,
    PlanMutations,
    ChatMutations,
    PatientMutations,
    DoctorMutations,
    PayerMutations,
    UserManagementSiteSettingsMutations,
    FeatureMutations,
    ComplaintMutations,
):
    pass


class Subscription(
    ChatSubscriptions,
    DoctorSubscriptions
):
    pass


schema = build_schema(Query, mutation=Mutation, subscription=Subscription)
