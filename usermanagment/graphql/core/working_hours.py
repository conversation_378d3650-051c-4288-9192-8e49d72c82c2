from django.core.exceptions import ValidationError

from usermanagment.core.utils import datetime_utils


class WorkingHoursMixin:

    @classmethod
    def validate_working_hours(cls, info, data):
        working_hours = data.get("working_hours", None)
        if working_hours:
            for working_hour in working_hours:
                day = working_hour['day']
                open_time_ranges = working_hour['open_time_ranges']
                if not open_time_ranges:
                    raise ValidationError(
                        {"openTimeRanges":
                            "openTimeRanges shouldn't be empty for day {}".format(
                                day)})
                for time_range in open_time_ranges:
                    if time_range['open_time'] >= time_range['close_time']:
                        raise ValidationError(
                            {"workingHours":
                                 "workingHours openTime[{}] must be smaller than closeTime[{}] for day {}"
                                     .format(time_range['open_time'],
                                             time_range['close_time'], day)})

                if datetime_utils.time_ranges_has_intersection(open_time_ranges):
                    raise ValidationError(
                        {"workingHours":
                             "workingHours shouldn't have intersections for day {}"
                                 .format(day)})

    @classmethod
    def validate_working_hours_override(cls, info, data):
        working_hours_override = data.get("working_hours_override", None)
        if working_hours_override:
            for working_hour_override in working_hours_override:
                date = working_hour_override['date']
                open_time_ranges = working_hour_override['open_time_ranges']
                if not open_time_ranges:
                    raise ValidationError(
                        {"openTimeRanges":
                            "openTimeRanges shouldn't be empty for date {}".format(
                                date)})
                for time_range in open_time_ranges:
                    if time_range['open_time'] >= time_range['close_time']:
                        raise ValidationError(
                            {"workingHours":
                                 "workingHours openTime[{}] must be smaller than closeTime[{}] for day {}"
                                     .format(time_range['open_time'],
                                             time_range['close_time'], data)})

                if datetime_utils.time_ranges_has_intersection(open_time_ranges):
                    raise ValidationError(
                        {"workingHoursOverride":
                             "workingHoursOverride shouldn't have intersections for date {}"
                                 .format(date)})

    @classmethod
    def save_working_hours(cls, info, instance, cleaned_data, model_class,
                           relation_id_name):
        working_hours = cleaned_data.get("working_hours", None)
        if working_hours:
            instance.working_hours.all().delete()
            for working_hour in working_hours:
                day = working_hour['day']
                open_time_ranges = working_hour['open_time_ranges']
                for open_time_range in open_time_ranges:
                    data = {'day': day, relation_id_name: instance.id}
                    data.update(open_time_range)
                    model_class.objects.create(**data)

    @classmethod
    def save_working_hours_override(cls, info, instance, cleaned_data, model_class,
                                    relation_id_name):
        working_hours_override = cleaned_data.get("working_hours_override", None)
        if working_hours_override:
            instance.working_hours_override.all().delete()
            for working_hour_override in working_hours_override:
                date = working_hour_override['date']
                open_time_ranges = working_hour_override['open_time_ranges']
                for open_time_range in open_time_ranges:
                    data = {'day': date, relation_id_name: instance.id}
                    data.update(open_time_range)
                    model_class.objects.create(**data)
