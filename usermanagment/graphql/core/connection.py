import json
from functools import partial
from operator import attrgetter
from typing import Any, Dict, <PERSON>, Tuple, Union

import graphene
from django.core.paginator import Paginator
from django.db.models import Model as DjangoModel, Q, QuerySet
from graphene.relay.connection import Connection
from graphene_django import DjangoObjectType
from graphql.error import GraphQLError
from graphql_relay.connection.connectiontypes import Edge, PageInfo
from graphql_relay.utils import base64, unbase64

from .utils import to_snake_case
from ..core.enums import OrderDirection

ConnectionArguments = Dict[str, Any]


def to_global_cursor(values):
    return base64(str(values))


def from_global_cursor(cursor) -> List[str]:
    values = unbase64(cursor)
    return json.loads(values)


def get_field_value(instance: DjangoModel, field_name: str):
    """Get field value for given field in filter format 'field__foreign_key_field'."""
    field_path = field_name.split("__")
    attr = instance
    for elem in field_path:
        attr = getattr(attr, elem)

    if callable(attr):
        return "%s" % attr()
    return attr


def _prepare_filter_expression(
        field_name: str,
        index: int,
        cursor: List[str],
        sorting_fields: List[str],
        sorting_direction: str,
) -> Tuple[Q, Dict[str, Union[str, bool]]]:
    field_expression: Dict[str, Union[str, bool]] = {}
    extra_expression = Q()
    for cursor_id, cursor_value in enumerate(cursor[:index]):
        if cursor_value is not None:
            field_expression[sorting_fields[cursor_id]] = cursor_value

    if cursor[index] is not None:
        field_expression[f"{field_name}__{sorting_direction}"] = cursor[index]
    else:
        field_expression[f"{field_name}__isnull"] = False

    return extra_expression, field_expression


def _prepare_filter(
        cursor: List[str], sorting_fields: List[str], sorting_direction: str
) -> Q:
    """Create filter arguments based on sorting fields.

    :param cursor: list of values that are passed from page_info, used for filtering.
    :param sorting_fields: list of fields that were used for sorting.
    :param sorting_direction: keyword direction ('lt', gt').
    :return: Q() in following format
        (OR: ('first_field__gt', 'first_value_form_cursor'),
            (AND: ('second_field__gt', 'second_value_form_cursor'),
                ('first_field', 'first_value_form_cursor')),
            (AND: ('third_field__gt', 'third_value_form_cursor'),
                ('second_field', 'second_value_form_cursor'),
                ('first_field', 'first_value_form_cursor'))
        )
    """
    filter_kwargs = Q()
    for index, field_name in enumerate(sorting_fields):
        if cursor[index] is None:
            continue

        extra_expression, field_expression = _prepare_filter_expression(
            field_name, index, cursor, sorting_fields, sorting_direction
        )
        filter_kwargs |= Q(extra_expression, **field_expression)

    return filter_kwargs


def _validate_connection_args(args):
    first = args.get("first")
    last = args.get("last")

    if first and not (isinstance(first, int) and first > 0):
        raise GraphQLError("Argument `first` must be a non-negative integer.")
    if last and not (isinstance(last, int) and last > 0):
        raise GraphQLError("Argument `last` must be a non-negative integer.")
    if first and last:
        raise GraphQLError("Argument `last` cannot be combined with `first`.")
    if first and args.get("before"):
        raise GraphQLError("Argument `first` cannot be combined with `before`.")
    if last and args.get("after"):
        raise GraphQLError("Argument `last` cannot be combined with `after`.")


def _get_sorting_fields(sort_by):
    sorting_fields = sort_by.get("field")
    if sorting_fields and not isinstance(sorting_fields, list):
        return [sorting_fields]
    elif not sorting_fields:
        raise ValueError("Error while preparing cursor values.")
    return sorting_fields


def _get_sorting_direction(sort_by):
    direction = sort_by.get("direction", "")
    sorting_desc = direction == OrderDirection.DESC
    return "lt" if sorting_desc else "gt"


def _get_page_info(matching_records, cursor, first, last):
    page_info = {
        "has_previous_page": False,
        "has_next_page": False,
        "start_cursor": None,
        "end_cursor": None,
    }
    return page_info


def _get_edges_for_connection(edge_type, qs, args, sorting_fields):
    before = args.get("before")
    after = args.get("after")
    first = args.get("first")
    last = args.get("last")
    cursor = after or before
    page_no = get_page(cursor)
    page_no = page_no - 1 if before else page_no

    requested_count = first or last

    page = Paginator(qs, requested_count)

    if page_no is None:
        if first:
            page_no = 1
        if last:
            page_no = page.num_pages

    matching_records = []
    if not (page_no is not None and (page_no > page.num_pages or page_no < 1)):
        matching_records = list(page.page(page_no))

    page_info = _get_page_info(matching_records, cursor, first, last)

    edges = [
        edge_type(
            node=record,
            cursor=to_global_cursor(
                [get_field_value(record, field) for field in sorting_fields]
            ),
        )
        for record in matching_records
    ]
    if edges:
        page_info["start_cursor"] = to_global_cursor(page_no)
        page_info["end_cursor"] = to_global_cursor(page_no + 1)
        page_info["has_next_page"] = page.num_pages >= page_no + 1
        page_info["has_previous_page"] = page_no > 1
    return edges, page_info


def get_page(cursor):
    return from_global_cursor(cursor) if cursor else None


def connection_from_queryset_slice(
        qs: QuerySet,
        args: ConnectionArguments = None,
        connection_type: Any = Connection,
        edge_type: Any = Edge,
        pageinfo_type: Any = PageInfo,
) -> Connection:
    """Create a connection object from a QuerySet."""
    args = args or {}
    _validate_connection_args(args)

    sort_by = args.get("sort_by", {})
    sorting_fields = _get_sorting_fields(sort_by)
    edges, page_info = _get_edges_for_connection(edge_type, qs, args, sorting_fields)
    return connection_type(edges=edges, page_info=pageinfo_type(**page_info), )


class NonNullConnection(Connection):
    class Meta:
        abstract = True

    @classmethod
    def __init_subclass_with_meta__(cls, node=None, name=None, **options):
        super().__init_subclass_with_meta__(node=node, name=name, **options)

        # Override the original EdgeBase type to make to `node` field required.
        class EdgeBase:
            node = graphene.Field(
                cls._meta.node,
                description="The item at the end of the edge.",
                required=True,
            )
            cursor = graphene.String(
                required=True, description="A cursor for use in pagination."
            )

        # Create the edge type using the new EdgeBase.
        edge_name = cls.Edge._meta.name
        edge_bases = (EdgeBase, graphene.ObjectType)
        edge = type(edge_name, edge_bases, {})
        cls.Edge = edge

        # Override the `edges` field to make it non-null list
        # of non-null edges.
        cls._meta.fields["edges"] = graphene.Field(
            graphene.NonNull(graphene.List(graphene.NonNull(cls.Edge)))
        )


class CountableConnection(NonNullConnection):
    class Meta:
        abstract = True

    total_count = graphene.Int(description="A total count of items in the collection.")

    @staticmethod
    def resolve_total_count(root, *_args, **_kwargs):
        if isinstance(root.iterable, list):
            return len(root.iterable)
        return root.iterable.count()


class TranslatableField(graphene.Field):
    def __init__(self, required=False, *args, **kwargs):
        super(TranslatableField, self).__init__(graphene.String,
                                                required=required,
                                                *args, **kwargs)

    @staticmethod
    def field_resolver(root, info, **_args):
        return attrgetter(f"translated.{to_snake_case(info.field_name)}")(root)

    def get_resolver(self, parent_resolver):
        return partial(
            self.field_resolver,
        )


class TranslatableType(DjangoObjectType):
    class Meta:
        abstract = True

    @classmethod
    def __init_subclass_with_meta__(cls, _meta=None, **options):
        super(TranslatableType, cls).__init_subclass_with_meta__(_meta=_meta, **options)
        translatable_fields = options.pop('translatable_fields', [])
        for translatable_field in translatable_fields:
            cls._meta.fields[translatable_field] = TranslatableField()


class CountableDjangoObjectType(TranslatableType):
    class Meta:
        abstract = True

    @classmethod
    def __init_subclass_with_meta__(cls, *args, **kwargs):
        # exclude createdBy and modifiedBy fields from all fields
        if not (kwargs.get('only_fields') or kwargs.get('fields')):
            from ...core.models import AuditModel
            if issubclass(kwargs.get('model'), AuditModel):
                exclude = kwargs.get('exclude', kwargs.pop('exclude_fields', []))
                exclude.extend(['created_by', 'modified_by'])
                kwargs['exclude'] = exclude
        # Force it to use the countable connection
        countable_conn = CountableConnection.create_type(
            "{}CountableConnection".format(cls.__name__), node=cls
        )
        super().__init_subclass_with_meta__(*args, connection=countable_conn, **kwargs)
