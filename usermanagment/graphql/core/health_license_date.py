from django.core.exceptions import ValidationError
from datetime import date


class HealthLicenseDatesMixin:

    @classmethod
    def validate_health_licence_dates(cls, info, instance, data):
        # validate all fields in the list are either None or all are not None
        # True means more checking is needed. False means no more checking is needed.

        health_license_number = data.get('health_license_number',
                                         instance.health_license_number if instance else None)
        health_license_start_date = data.get('health_license_start_date',
                                             instance.health_license_start_date if instance else None)
        health_license_end_date = data.get('health_license_end_date',
                                           instance.health_license_end_date if instance else None)

        if health_license_number is not None and len(health_license_number) == 0:
            health_license_number = None

        data = [health_license_number, health_license_start_date,
                health_license_end_date]

        count = data.count(None)
        if count == len(data): # all fields are  None، fast return
            return
        if count == 0:
            if health_license_start_date >= health_license_end_date:
                raise ValidationError(
                    {
                        "health_license_start_date": "Start date must be less than end date"},
                    code="invalid")

            if health_license_end_date < date.today():
                raise ValidationError(
                    {
                        "health_license_end_date": "License End date is expired"},
                    code="invalid")
            return
        raise ValidationError({
            health_license_number: "All health license fields must be either None or all not None"},
            code="required")
