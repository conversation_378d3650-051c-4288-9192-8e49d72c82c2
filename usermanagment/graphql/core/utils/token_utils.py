import json
import logging
from datetime import date

import jwt
from phonenumber_field.phonenumber import PhoneNumber

from usermanagment.auth.enums import AppTypes
from usermanagment.auth.jwt_utils import encode_jwt_token, get_token_payload
from usermanagment.core.permissions import VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS, \
    PHARMACIST_USER_AUTH_GROUP_PERMISSIONS, DOCTOR_USER_AUTH_GROUP_PERMISSIONS, \
    NURSE_USER_AUTH_GROUP_PERMISSIONS, PAYER_ADMIN_AUTH_GROUP_PERMISSIONS, \
    PAYER_USER_AUTH_GROUP_PERMISSIONS, RECEPTIONIST_USER_AUTH_GROUP_PERMISSIONS
from usermanagment.graphql.account.utils import get_user_permissions
from usermanagment.keycloak.keycloak_client import KeycloakAPI
from usermanagment.workflow.rest_client import WorkflowApi

logger = logging.getLogger(__name__)

user_attrs_to_be_added_to_token = ['app_type', 'app_role', 'national_id',
                                   'date_of_birth', 'meeting_platform_id', 'mobile',
                                   'two_factor_auth_enabled',
                                   'two_factor_auth_verification_method']


def generate_new_token_with_extra_claims(token, user):
    try:
        branches = []
        user_info = get_token_payload(token)
        user_info['user_id'] = user.pk
        user_info["patient_id"] = None
        user_info["vendor_id"] = None
        user_info["doctor_id"] = None

        prepare_user_attributes(user, user_info)
        branches = prepare_user_branches(branches, user, user_info)
        prepare_user_permissions(user, user_info)

        headers = jwt.get_unverified_header(token)
        return encode_jwt_token(user_info, headers), branches
    except Exception as e:
        logger.error("Error while add extra claims to user token", exc_info=e)
        return token, branches


def prepare_user_permissions(user, user_info):
    if not (user.is_superuser or user.is_consumer or user.is_aggregator):
        if user.is_doctor:
            user_permissions = DOCTOR_USER_AUTH_GROUP_PERMISSIONS
        elif user.is_nurse:
            user_permissions = NURSE_USER_AUTH_GROUP_PERMISSIONS
        elif user.is_receptionist:
            user_permissions = RECEPTIONIST_USER_AUTH_GROUP_PERMISSIONS
        elif user.is_pharmacist:
            user_permissions = PHARMACIST_USER_AUTH_GROUP_PERMISSIONS
        elif user.is_payer_staff:
            user_permissions = PAYER_USER_AUTH_GROUP_PERMISSIONS
        else:
            user_permissions = get_user_permissions(user)

        if user.is_vendor_admin:
            list(set(user_permissions.extend(VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS)))

        elif user.is_payer_admin:
            list(set(user_permissions.extend(PAYER_ADMIN_AUTH_GROUP_PERMISSIONS)))

        if user_permissions:
            user_info['permissions'] = [permission.codename.lower() for
                                        permission
                                        in user_permissions]


def prepare_user_branches(branches, user, user_info):
    if user.is_vendor_staff:
        branches = user.branches.all()

        if branches:
            user_info['branches'] = [branch.id for branch in branches]

    return branches


def prepare_user_attributes(user, user_info):
    user_attrs_to_be_added_to_token_extended = user_attrs_to_be_added_to_token[:]
    if user.is_consumer:
        user_attrs_to_be_added_to_token_extended.append("patient_id")
        if user.parent_user:
            user_attrs_to_be_added_to_token_extended.append("parent_user_id")
    elif user.is_vendor:
        user_attrs_to_be_added_to_token_extended.extend(
            ["vendor_id", "doctor_id", "vendor_user_type",
             "pharmacist_id", "nurse_id", "receptionist_id", "manager_id",
             "dental_hygienist_id",
             "diabetes_educator_id", "fitness_coach_id", "nutritionist_id",
             "optometrist_id", "podiatric_medical_assistant_id", "psychologist_id",
             "social_worker_id"])
    elif user.is_payer_staff:
        user_attrs_to_be_added_to_token_extended.extend(["payer_id", "payer_license"])
    for user_attr in user_attrs_to_be_added_to_token_extended:
        user_attr_value = getattr(user, user_attr, None)
        if user_attr == "payer_license":
            user_attr_value = user.payer.license_number
        if user_attr_value is not None:
            if isinstance(user_attr_value, (PhoneNumber, date)):
                user_info[user_attr] = str(user_attr_value)
                continue
            user_info[user_attr] = user_attr_value

    user_id = getattr(user, 'pk', None)
    if user_id:
        user_info['user_id'] = user.pk


def generate_meeting_platform_id(user, username, password):
    pass
    # if not user.meeting_platform_id and user.app_type in [AppTypes.CUSTOMER,
    #                                                       AppTypes.VENDOR]:
    #     result = KeycloakAPI().login({
    #         "username": username,
    #         "password": password,
    #     })
    #     if result.get("error") is not None:
    #         logger.error(f"Error while login to keycloak => {result}")
    #         return None
    #
    #     token = result['access_token']
    #     request_result = WorkflowApi().create_meeting_platform_id(token)
    #     try:
    #         logger.debug(f"request_result => {request_result}")
    #         result_data = json.loads(request_result[0].data)
    #         logger.debug(f"result_data => {result_data}")
    #         meeting_platform_id = result_data.get('messengerId', None)
    #         logger.debug(f"meeting_platform_id => {meeting_platform_id}")
    #         return meeting_platform_id
    #     except Exception as e:
    #         logger.error("Error while creating meeting platform id", exc_info=e)
