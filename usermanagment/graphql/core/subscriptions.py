import logging

import channels_graphql_ws
from usermanagment.kafka.kafka_messagin_service import KafkaMessageService

logger = logging.getLogger(__name__)


class BaseSubscription(channels_graphql_ws.Subscription):

    @classmethod
    def check_authorization(cls, payload, info):
        pass

    @classmethod
    def perform_publish(cls, payload, info):
        pass

    @classmethod
    def publish(cls, payload, info, **kwargs):
        """Called to notify the client."""

        # Here `payload` contains the `payload` from the `broadcast()`
        # invocation (see below). You can return `MySubscription.SKIP`
        # if you wish to suppress the notification to a particular
        # client. For example, this allows to avoid notifications for
        # the actions made by this particular client.

        if not cls.check_authorization(payload, info):
            return cls.SKIP

        user_email = getattr(info.context.user, "email", None)
        user_type = getattr(info.context.user, "app_type", "Unknown user type")

        status = None
        try:
            data = list(payload.values())[0]
            status = getattr(data, "status", getattr(data, "dispense_status", None))
        except Exception:
            pass

        logger.debug(
            "\n"
            + "=" * 100
            + f"\n{cls.__name__} sending websocket message to {user_type} {user_email}"
            + (f" => status: {status}\n" if status else "\n")
            + "=" * 100
        )

        return cls.perform_publish(payload, info)

    @classmethod
    def broadcast(cls,topic, *, group=None, payload=None):
        try:
            KafkaMessageService().send_message(
                topic,
                payload
            )
        except Exception as e:
            logger.error(f"error while broadcasting {cls.__name__}", exc_info=e)
