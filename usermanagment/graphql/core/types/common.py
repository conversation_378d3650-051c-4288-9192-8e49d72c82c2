import graphene
from graphene import <PERSON>ala<PERSON>, Date, relay

from ..enums import (
    AccountErrorCode,
    PermissionGroupErrorCode,
    BlockErrorCode,
    ConsumerViewPreferenceErrorCode,
    VendorErrorCode,
    SubscriptionErrorCode,
    ChatErrorCode,
    PatientMedicalHistoryErrorCode,
    PatientErrorCode,
    LanguageCodeEnum,
    DoctorErrorCode,
    SpecializationErrorCode,
    QualificationErrorCode,
    PayerErrorCode,
    HealthSymptomErrorCode,
    SiteSettingsErrorCode
)
from ..scalars import Decimal
from ...vendor.enums import DayOfWeekEnum
from ....auth.graphql.enums import PermissionEnum


class CountryDisplay(graphene.ObjectType):
    code = graphene.String(description="Country code.", required=True)
    country = graphene.String(description="Country name.", required=True)


class LanguageDisplay(graphene.ObjectType):
    code = LanguageCodeEnum(
        description="ISO 639 representation of the language name.", required=True
    )
    language = graphene.String(description="Full name of the language.", required=True)


class Permission(graphene.ObjectType):
    code = PermissionEnum(description="Internal code for permission.", required=False)
    name = graphene.String(
        description="Describe action(s) allowed to do by permission.", required=True
    )
    key_cloak_permission = graphene.Field(
        "usermanagment.graphql.account.mutations.keycloak_permission.KeyCloakPermission")

    class Meta:
        description = "Represents a permission object in a friendly form."
        interfaces = [relay.Node]
        model = "django.contrib.auth.models.Permission"
        fields = ["code", "name", "key_cloak_permission"]

    @staticmethod
    def resolve_code(root, _info, **_kwargs):
        if hasattr(root, "code"):
            return root.code
        return None

    @staticmethod
    def resolve_key_cloak_permission(root, _info, **_kwargs):
        if hasattr(root, "key_cloak_permission"):
            return root.key_cloak_permission
        return None


class Error(graphene.ObjectType):
    field = graphene.String(
        description=(
            "Name of a field that caused the error. A value of `null` indicates that "
            "the error isn't associated with a particular field."
        ),
        required=False,
    )
    message = graphene.String(description="The error message.")

    class Meta:
        description = "Represents an error in the input of a mutation."


class AccountError(Error):
    code = AccountErrorCode(description="The error code.", required=True)


class PermissionGroupError(Error):
    code = PermissionGroupErrorCode(description="The error code.", required=True)
    message = graphene.String(description="The error message.")
    permissions = graphene.List(
        graphene.NonNull(PermissionEnum),
        description="List of permissions which causes the error.",
        required=False,
    )
    users = graphene.List(
        graphene.NonNull(graphene.ID),
        description="List of user IDs which causes the error.",
        required=False,
    )


class DoctorError(Error):
    code = DoctorErrorCode(description="The error code.", required=True)


class SpecializationError(Error):
    code = SpecializationErrorCode(description="The error code.", required=True)


class QualificationError(Error):
    code = QualificationErrorCode(description="The error code.", required=True)


class ConsumerViewPreferenceError(Error):
    code = ConsumerViewPreferenceErrorCode(description="The error code.", required=True)


class BlockError(Error):
    code = BlockErrorCode(description="The error code.", required=True)


class VendorError(Error):
    code = VendorErrorCode(description="The error code.", required=True)


class VendorDepartmentError(Error):
    code = VendorErrorCode(description="The error code.", required=True)


class PayerError(Error):
    code = PayerErrorCode(description="The error code.", required=True)


class BranchError(Error):
    code = VendorErrorCode(description="The error code.", required=True)


class SubscriptionError(Error):
    code = SubscriptionErrorCode(description="The error code.", required=True)


class ChatError(Error):
    code = ChatErrorCode(description="The error code.", required=True)


class PatientError(Error):
    code = PatientErrorCode(description="The error code.", required=True)


class PatientMedicalHistoryError(Error):
    code = PatientMedicalHistoryErrorCode(description="The error code.", required=True)


class HealthSymptomError(Error):
    code = HealthSymptomErrorCode(description="The error code.", required=True)


class UserManagementSiteSettingsError(Error):
    code = SiteSettingsErrorCode(description="The error code.",
                                 required=True)


class Weight(graphene.ObjectType):
    unit = graphene.String(description="Weight unit.", required=True)
    value = graphene.Float(description="Weight value.", required=True)

    class Meta:
        description = "Represents weight value in a specific weight unit."


class Image(graphene.ObjectType):
    url = graphene.String(required=True, description="The URL of the image.")
    alt = graphene.String(description="Alt text for an image.")

    class Meta:
        description = "Represents an image."


class PriceRangeInput(graphene.InputObjectType):
    gte = graphene.Float(description="Price greater than or equal to.", required=False)
    lte = graphene.Float(description="Price less than or equal to.", required=False)


class DateRangeInput(graphene.InputObjectType):
    gte = graphene.Date(description="Start date.", required=False)
    lte = graphene.Date(description="End date.", required=False)


class DateTimeRangeInput(graphene.InputObjectType):
    gte = graphene.DateTime(description="Start date.", required=False)
    lte = graphene.DateTime(description="End date.", required=False)


class IntRangeInput(graphene.InputObjectType):
    gte = graphene.Int(description="Value greater than or equal to.", required=False)
    lte = graphene.Int(description="Value less than or equal to.", required=False)


class DecimalRangeInput(graphene.InputObjectType):
    gte = Decimal(description="Value greater than or equal to.", required=False)
    lte = Decimal(description="Value less than or equal to.", required=False)


class TaxType(graphene.ObjectType):
    """Representation of tax types fetched from tax gateway."""

    description = graphene.String(description="Description of the tax type.")
    tax_code = graphene.String(
        description="External tax code used to identify given tax group."
    )


class LocationInput(graphene.InputObjectType):
    lng = graphene.Float(description="Longitude Value .", required=True)
    lat = graphene.Float(description="Latitude Value.", required=True)


class LocationType(graphene.ObjectType):
    lng = graphene.Float(description="Longitude Value .")
    lat = graphene.Float(description="Latitude Value.")


class DictType(Scalar):
    @staticmethod
    def serialize(dt):
        return dt

    @staticmethod
    def parse_literal(node):
        return node

    @staticmethod
    def parse_value(value):
        return value


class TimeRangeFilterInput(graphene.InputObjectType):
    start = graphene.Time(description="start time", required=True)
    end = graphene.Time(description="end time", required=True)


class WorkingHourFilterInput(graphene.InputObjectType):
    day = DayOfWeekEnum(description="day of week", required=True)
    time = TimeRangeFilterInput(required=True)


class DateTimeFilterInput(graphene.InputObjectType):
    date = Date(description="date", required=True)
    time = TimeRangeFilterInput(required=True)


class TimeRangeInput(graphene.InputObjectType):
    open_time = graphene.Time(description="open time", required=True)
    close_time = graphene.Time(description="close time", required=True)


class WorkingHourInput(graphene.InputObjectType):
    day = DayOfWeekEnum(description="day of week", required=True)
    open_time_ranges = graphene.List(TimeRangeInput, description="open time ranges",
                                     required=True)


class WorkingHourOverrideInput(graphene.InputObjectType):
    date = graphene.Date(description="date to override", required=True)
    open_time_ranges = graphene.List(TimeRangeInput, description="open time ranges",
                                     required=True)


class TimeRange(graphene.ObjectType):
    open_time = graphene.Time(description="open time")
    close_time = graphene.Time(description="close time")


class WorkingHour(graphene.ObjectType):
    day = DayOfWeekEnum(description="day of week")
    open_time_ranges = graphene.List(TimeRange, description="open time ranges")


class WorkingHourOverride(graphene.ObjectType):
    date = graphene.Date(description="specific date to override working hours for")
    open_time_ranges = graphene.List(TimeRange, description="open time ranges")


class DistanceFilterInput(graphene.InputObjectType):
    distance = Decimal(description="Distance in meters", required=False)
    coordinates = LocationInput(required=True)
