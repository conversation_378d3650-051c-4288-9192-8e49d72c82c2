import graphene

from usermanagment import settings
from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.graphql.core.enums import LanguageCodeEnum
from usermanagment.graphql.core.types.common import LanguageDisplay
from usermanagment.graphql.core.utils import str_to_enum

BASIC_TRANSLATABLE_FIELDS = ["id", "name"]
EXTENDED_TRANSLATABLE_FIELDS = [
    "id",
    "name",
    "description",
    "description_json",
    "seo_title",
    "seo_description",
]


class BaseTranslationType(CountableDjangoObjectType):
    language = graphene.Field(
        LanguageDisplay, description="Translation language.", required=True
    )

    class Meta:
        abstract = True

    @staticmethod
    def resolve_language(root, *_args):
        try:
            language = next(
                language[1]
                for language in settings.LANGUAGES
                if language[0] == root.language_code
            )
        except StopIteration:
            return None
        return LanguageDisplay(
            code=LanguageCodeEnum[str_to_enum(root.language_code)], language=language
        )


class ObjectWithBasicTranslatableFields(graphene.Interface):
    name = graphene.String()

    @staticmethod
    def resolve_name(root, _info):
        return root.translated.name


class ObjectWithExtendedTranslatableFields(ObjectWithBasicTranslatableFields):
    description = graphene.String()
    description_json = graphene.String()
    seo_title = graphene.String()
    seo_description = graphene.String()

    @staticmethod
    def resolve_description(root, _info):
        return root.translated.description

    @staticmethod
    def resolve_description_json(root, _info):
        return root.translated.description_json

    @staticmethod
    def resolve_seo_title(root, _info):
        return root.translated.seo_title

    @staticmethod
    def resolve_seo_description(root, _info):
        return root.translated.seo_description


class TranslationMixin:

    @classmethod
    def clean_translations(cls, info, instance, data, translatable_fields):
        #TODO : re implement this if needed
        # This code assumes all date are sent in the database
        # if translation is not in the data it will add empty translation, other than
        # performance issue this will cause problem in the future
        translations = data.pop('translations', [])
        # default_translation = {k: v for k, v in data.items() if k in translatable_fields}
        #
        # en_exist = any('en' in tr.values() for tr in translations)
        # if not en_exist:
        #     default_translation['language_code'] = 'en'
        #     translations.append(default_translation)

        data['translations'] = translations

    @classmethod
    def save_translations(cls, info, instance, cleaned_input):
        translations = cleaned_input.pop('translations', [])
        for translation in translations:
            instance.translations.update_or_create(
                language_code=translation.pop('language_code'), defaults=translation
            )
