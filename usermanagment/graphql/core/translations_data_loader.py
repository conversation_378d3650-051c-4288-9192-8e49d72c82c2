from collections import defaultdict

from django.db.models import Q

from ..core.dataloaders import DataLoader


class TranslationsByObjectIdLoader(DataLoader):

    def __new__(cls, context, translation_model, object_id_name):
        cls.context_key = f'{translation_model.__name__}_loader'
        return super().__new__(cls, context)

    def __init__(self, context, translation_model, object_id_name):
        super().__init__(context)

        self.context_key = f'{translation_model.__name__}_loader'
        self.model = translation_model
        self.object_id_name = object_id_name

    def batch_load(self, keys):
        translations = self.model.objects.filter(
            Q(**{f"{self.object_id_name}__in": keys}))
        translation_map = defaultdict(list)
        for translation in translations:
            translation_map[getattr(translation, self.object_id_name)].append(
                translation)
        return [translation_map[key] for key in keys]
