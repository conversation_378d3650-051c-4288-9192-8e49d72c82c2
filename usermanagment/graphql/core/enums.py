import graphene

from .utils import str_to_enum
from ... import settings
from ...account import error_codes as account_error_codes
from ...block import error_codes as block_error_codes
from ...chat import error_codes as chat_error_codes
from ...patient import error_codes as patient_error_codes
from ...subscription import error_codes as subscription_error_codes
from ...vendor import error_codes as vendor_error_codes
from ...doctor import error_codes as doctor_error_codes
from ...payer import error_codes as payer_error_codes
from ...site import error_codes as site_error_codes

# FIXME CoreTaxRateType should be removed after we will drop old api fields dedicated
#  to taxes


class OrderDirection(graphene.Enum):
    ASC = ""
    DESC = "-"

    @property
    def description(self):
        # Disable all the no-member violations in this function
        # pylint: disable=no-member
        if self == OrderDirection.ASC:
            return "Specifies an ascending sort order."
        if self == OrderDirection.DESC:
            return "Specifies a descending sort order."
        raise ValueError("Unsupported enum value: %s" % self.value)


class ReportingPeriod(graphene.Enum):
    TODAY = "TODAY"
    THIS_MONTH = "THIS_MONTH"
    LAST_7_DAYS = "LAST_7_DAYS"


def to_enum(enum_cls, *, type_name=None, **options) -> graphene.Enum:
    """Create a Graphene enum from a class containing a set of options.

    :param enum_cls:
        The class to build the enum from.
    :param type_name:
        The name of the type. Default is the class name + 'Enum'.
    :param options:
        - description:
            Contains the type description (default is the class's docstring)
        - deprecation_reason:
            Contains the deprecation reason.
            The default is enum_cls.__deprecation_reason__ or None.
    :return:
    """

    # note this won't work until
    # https://github.com/graphql-python/graphene/issues/956 is fixed
    deprecation_reason = getattr(enum_cls, "__deprecation_reason__", None)
    if deprecation_reason:
        options.setdefault("deprecation_reason", deprecation_reason)

    type_name = type_name or (enum_cls.__name__ + "Enum")
    choices = getattr(enum_cls, 'CHOICES', getattr(enum_cls, 'choices', None))
    enum_data = [(str_to_enum(code.upper()), code) for code, name in choices]
    return graphene.Enum(type_name, enum_data, **options)


AccountErrorCode = graphene.Enum.from_enum(account_error_codes.AccountErrorCode)
PermissionGroupErrorCode = graphene.Enum.from_enum(
    account_error_codes.PermissionGroupErrorCode
)
BlockErrorCode = graphene.Enum.from_enum(block_error_codes.BlockErrorCode)
VendorErrorCode = graphene.Enum.from_enum(vendor_error_codes.VendorErrorCode)
PayerErrorCode = graphene.Enum.from_enum(payer_error_codes.PayerErrorCode)
SubscriptionErrorCode = graphene.Enum.from_enum(
    subscription_error_codes.SubscriptionErrorCode
)

ChatErrorCode = graphene.Enum.from_enum(chat_error_codes.ChatErrorCode)
PatientErrorCode = graphene.Enum.from_enum(patient_error_codes.PatientErrorCode)
PatientMedicalHistoryErrorCode = graphene.Enum.from_enum(
    patient_error_codes.PatientHistoryErrorCode)
ConsumerViewPreferenceErrorCode = graphene.Enum.from_enum(
    account_error_codes.ConsumerViewPreferenceErrorCode
)
LanguageCodeEnum = graphene.Enum(
    "LanguageCodeEnum",
    [(lang[0].replace("-", "_").upper(), lang[0]) for lang in settings.LANGUAGES],
)
DoctorErrorCode = graphene.Enum.from_enum(doctor_error_codes.DoctorErrorCode)
SpecializationErrorCode = graphene.Enum.from_enum(
    doctor_error_codes.SpecializationErrorCode
)
QualificationErrorCode = graphene.Enum.from_enum(
    doctor_error_codes.QualificationErrorCode
)

HealthSymptomErrorCode = graphene.Enum.from_enum(
    patient_error_codes.HealthSymptomErrorCode
)

SiteSettingsErrorCode = graphene.Enum.from_enum(
    site_error_codes.SiteErrorCode
)
