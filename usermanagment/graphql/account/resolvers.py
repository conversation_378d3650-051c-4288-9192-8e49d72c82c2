import graphene
from django.contrib.auth import models as auth_models
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.db.models.aggregates import Count
from django.db.models.fields import DateField
from django.db.models.functions import Cast

from .filters import filter_date_joined, filter_date_joined_period
from .utils import (
    get_user_permissions,
)
from ..utils import format_permissions_for_display, get_database_id
from ..utils.request_utils import get_current_user
from ...account import models
from ...auth.enums import AppTypes, AppRoleTypes
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import AccountPermissions, TerminologyPermissions


def resolve_customers(branch_id=None, has_chat_with=False):
    qs = models.User.objects.customers(branch_id=branch_id, has_chat_with=has_chat_with)
    return qs.distinct()


def resolve_customers_dashboard(info, query, **_kwargs):
    qs = models.User.objects.customers()

    filters = {
        'date_joined': _kwargs.get('filter', {}).get('date_joined', {}),
        'joined_period': _kwargs.get('filter', {}).get('joined_period'),
    }

    qs = filter_date_joined(qs, None, filters['date_joined'])
    qs = filter_date_joined_period(qs, None, filters['joined_period']) if filters[
        'joined_period'] else qs

    aggregated_data = qs. \
        annotate(key=Cast('date_joined', DateField())). \
        values('key'). \
        annotate(**{'value': Count('id')})

    return [
        {'key': str(_item['key'].strftime('%Y-%m-%d')), 'value': _item['value']}
        for _item in aggregated_data
    ]


def resolve_permission_groups(_info, user: models.User, app_type, vendor_id, **_kwargs):

    if vendor_id is not None:
        vendor_id = get_database_id(vendor_id, "Vendor")
    if user.is_vendor:
        vendor_id = user.vendor_id

    if app_type == AppTypes.VENDOR:
        return auth_models.Group.objects.filter(
            Q(group_configuration__group_type=app_type,
              group_configuration__keycloak_group_id__isnull=False,
              group_configuration__vendor_id=vendor_id) |
            Q(group_configuration__group_type=app_type,
              group_configuration__keycloak_group_id__isnull=False,
              group_configuration__is_global=True)
        ).all()
    elif app_type == AppTypes.ADMIN:
        return auth_models.Group.objects.filter(
            Q(group_configuration__group_type=app_type)).all()

    raise ValidationError(f"appType: {app_type} is not supported")


def resolve_users(info):
    current_user = get_current_user()
    qs = models.User.objects
    if current_user.is_staff:
        qs = qs.filter(~Q(app_type=AppTypes.CUSTOMER) &
                       ~Q(app_role=AppRoleTypes.ADMIN, app_type=AppTypes.ADMIN))
    elif current_user.is_vendor_admin:
        qs = qs.filter(vendor_id=current_user.vendor_id)
    elif current_user.vendor_id:
        qs = qs.filter(branches__in=current_user.branches.all())
    elif current_user.is_consumer:
        qs = qs.filter(app_type=AppTypes.VENDOR)
    elif current_user.is_payer_admin:
        qs = qs.filter(payer_id=current_user.payer_id)
    elif current_user.payer_id:
        qs = qs.filter(branches__in=current_user.branches.all())
    return qs.distinct()


def resolve_group_candidate_users(info, user, app_type, vendor_id):

    if vendor_id is not None:
        vendor_id = get_database_id(vendor_id, "Vendor")

    if user.is_vendor:
        vendor_id = user.vendor_id
        if app_type == AppTypes.ADMIN:
            raise PermissionDenied

    if app_type in [AppTypes.CUSTOMER, AppTypes.AGGREGATOR]:
        raise ValidationError(f"AppType : {app_type} is not supported")

    if app_type == AppTypes.ADMIN:
        vendor_id = None

    qs = models.User.objects

    qs = qs.filter(~Q(app_role=AppRoleTypes.ADMIN, app_type=AppTypes.ADMIN) &
                       Q(app_type=app_type))
    if vendor_id:
        qs = qs.filter(vendor_id=vendor_id)

    return qs.distinct()


# return only users that have the manage code system lists.
def resolve_medlist_users(info):
    qs = models.User.objects
    qs = qs.filter(
        Q(groups__permissions__codename=
          TerminologyPermissions.MANAGE_CODE_SYSTEM_LISTS.codename) |
        Q(user_permissions__codename=
          TerminologyPermissions.MANAGE_CODE_SYSTEM_LISTS.codename)
    )
    return qs.distinct()


def resolve_user(info, id):
    requester = get_current_user()
    if requester and requester.is_authenticated:
        _model, user_pk = graphene.Node.from_global_id(id)
        if requester.has_perms(
                [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS]
        ):
            return models.User.objects.filter(pk=user_pk).first()
        if requester.has_perm(AccountPermissions.MANAGE_STAFF):
            return models.User.objects.staff().filter(pk=user_pk).first()
        if requester.has_perm(AccountPermissions.MANAGE_USERS) or requester.has_perm(
                AccountPermissions.VIEW_USERS):
            if requester.is_vendor:
                return models.User.objects.filter(pk=user_pk,
                                                  vendor_id=requester.vendor_id).first()
            return models.User.objects.filter(pk=user_pk).first()
    return PermissionDenied()


def resolve_address(info, id):
    user = get_current_user()
    _model, address_pk = graphene.Node.from_global_id(id)
    if user.is_superuser:
        return models.Address.objects.filter(pk=address_pk).first()
    if user and not user.is_anonymous:
        return user.addresses.filter(id=address_pk).first()
    return PermissionDenied()


def resolve_permissions(root: models.User):
    if root.is_consumer:
        return []
    permissions = get_user_permissions(root)
    # permissions = permissions.order_by("codename")
    return format_permissions_for_display(permissions)


def resolve_customer(info, national_id):
    return models.User.objects.customers().filter(national_id=national_id).first()


def resolve_consumer_view_preference(info, user, view):
    return models.ConsumerViewPreference.objects.filter(user=user, view=view).first()
