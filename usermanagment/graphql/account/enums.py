import enum

import graphene
from django_countries import countries

from ..core.utils import str_to_enum
from ...account import CustomerEvents, AddressType, PersonGender, PasswordResetMethod, \
    TwoAuthVerificationMethod, OTPOperation, SendBulkMessagesMethod
from ...auth.enums import BiometricLoginType
from ...graphql.core.enums import to_enum

AddressTypeEnum = to_enum(AddressType, type_name="AddressTypeEnum")
CustomerEventsEnum = to_enum(CustomerEvents)

CountryCodeEnum = graphene.Enum(
    "CountryCode", [(str_to_enum(country[0]), country[0]) for country in countries]
)

TwoAuthVerificationMethodEnum = to_enum(TwoAuthVerificationMethod)

class UserStatus(graphene.Enum):
    ACTIVE = "active"
    DEACTIVATED = "deactivated"


PasswordResetMethodEnum = graphene.Enum(
    "PasswordResetMethodEnum",
    [(str_to_enum(status[0]), status[0]) for status in PasswordResetMethod.choices],

)

PersonGenderEnum = graphene.Enum(
    "PersonGenderEnum",
    [(str_to_enum(status[0]), status[0]) for status in PersonGender.choices],
)

OTPOperationEnum = graphene.Enum(
    "OTPOperationEnum",
    [(str_to_enum(status[0]), status[0]) for status in OTPOperation.choices],
)

BiometricLoginTypeEnum = graphene.Enum(
    "BiometricLoginTypeEnum",
    [(str_to_enum(status[0]), status[0]) for status in BiometricLoginType.choices],
)

SendBulkMessagesMethodEnum = graphene.Enum(
    "SendBulkMessagesMethodEnum",
    [(str_to_enum(status[0]), status[0]) for status in SendBulkMessagesMethod.choices],
)
