import django_filters
from graphene_django.filter import GlobalIDMultipleChoiceFilter

from .enums import UserStatus, BiometricLoginTypeEnum
from ..core.enums import ReportingPeriod
from ..core.filters import <PERSON><PERSON><PERSON>ilter, ObjectTypeFilter, ListObjectTypeFilter
from ..core.types.common import DateRangeInput
from ..utils import get_database_id, resolve_global_ids_to_primary_keys
from ..utils.filters import filter_by_query_param, filter_range_field, filter_by_period
from ...account.models import User
from ...auth.enums import AppRoleTypes
from ...auth.graphql.enums import AppTypeEnum, AppRoleTypeEnum, VendorUserTypeEnum
from ...vendor.models import Branch
from django.db.models import Q


def filter_date_joined(qs, _, value):
    return filter_range_field(qs, "date_joined__date", value)


def filter_date_joined_period(qs, _, value):
    return filter_by_period(qs, value, "date_joined")


def filter_status(qs, _, value):
    if value == UserStatus.ACTIVE:
        qs = qs.filter(is_active=True)
    elif value == UserStatus.DEACTIVATED:
        qs = qs.filter(is_active=False)
    return qs


def filter_app_type(qs, _, value):
    if value:
        qs = qs.filter(app_type__in=value)
    return qs


def filter_app_role_type(qs, _, value):
    if value:
        qs = qs.filter(app_role=value)
    return qs


def filter_by_vendor_name(qs, _, value):
    if value:
        qs = qs.filter(vendor__name=value)
    return qs


def filter_by_vendor_id(qs, _, value):
    if value:
        id = get_database_id(value, "Vendor")
        qs = qs.filter(vendor__id=id)
    return qs


def filter_by_branch_name(qs, _, value):
    if value:
        qs = qs.filter(branches__name=value)
    return qs


def filter_by_branch_id(qs, _, value):
    if value:
        id = get_database_id(value, "Branch")
        branch = Branch.objects.filter(id=id).first()
        if branch:
            return qs.filter(
                Q(Q(branches__id=branch.id) & Q(app_role=AppRoleTypes.USER)) |
                Q(Q(vendor__id=branch.vendor.id) & Q(app_role=AppRoleTypes.ADMIN)))
        else:
            return qs.none()


def filter_by_group_name(qs, _, value):
    if value:
        qs = qs.filter(groups__name=value)
    return qs


def filter_by_vendor_user_type(qs, _, value):
    if value:
        qs = qs.filter(vendor_user_type__in=value)
    return qs


def filter_user_search(qs, _, value):
    search_fields = (
        "email",
        "first_name",
        "last_name",
        "first_name_ar",
        "last_name_ar",
        "national_id",
        "default_shipping_address__name",
        "default_shipping_address__city__name",
    )
    if value:
        qs = filter_by_query_param(qs, value, search_fields)
    return qs


def filter_search(qs, _, value):
    search_fields = ("name",)
    if value:
        qs = filter_by_query_param(qs, value, search_fields)
    return qs


def filter_display(qs, _, value):
    search_fields = ("display", "display_ar")
    if value:
        qs = filter_by_query_param(qs, value, search_fields)
    return qs

def filter_code(qs, _, value):
    if value:
        qs = qs.filter(code=value)
    return qs

def filter_national_id(qs, _, value):
    if value:
        qs = qs.filter(national_id=value)
    return qs


def filter_by_ids(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "User")
        qs = qs.filter(id__in=pks)
    return qs


def filter_device_name(qs, _, value):
    if value:
        qs = qs.filter(device_name__icontains=value)
    return qs


def filter_device_id(qs, _, value):
    if value:
        qs = qs.filter(device_id=value)
    return qs


def filter_device_type(qs, _, value):
    if value:
        qs = qs.filter(type__in=value)
    return qs


def filter_by_created_date(qs, _, value):
    return filter_range_field(qs, "created", value)


class BaseUserFilter(django_filters.FilterSet):
    date_joined = ObjectTypeFilter(
        input_class=DateRangeInput, method=filter_date_joined
    )
    joined_period = EnumFilter(
        input_class=ReportingPeriod, method=filter_date_joined_period
    )
    search = django_filters.CharFilter(method=filter_user_search)

    class Meta:
        model = User
        fields = [
            "date_joined",
            "search",
        ]


class CustomerFilter(BaseUserFilter):
    national_id = django_filters.CharFilter(method=filter_user_search)


class CustomerDashboardFilter(django_filters.FilterSet):
    date_joined = ObjectTypeFilter(
        input_class=DateRangeInput, method=filter_date_joined
    )
    joined_period = EnumFilter(
        input_class=ReportingPeriod, method=filter_date_joined_period
    )


def filter_by_is_global(qs, _, value):
    qs = qs.filter(group_configuration__is_global=value)
    return qs


def filter_by_is_editable(qs, _, value):
    qs = qs.filter(group_configuration__is_editable=value)
    return qs


class PermissionGroupFilter(django_filters.FilterSet):
    search = django_filters.CharFilter(method=filter_search)
    is_global = django_filters.BooleanFilter(method=filter_by_is_global)
    is_editable = django_filters.BooleanFilter(method=filter_by_is_editable)


class UserFilter(BaseUserFilter):
    ids = GlobalIDMultipleChoiceFilter(method=filter_by_ids)
    status = EnumFilter(input_class=UserStatus, method=filter_status)
    app_type = ListObjectTypeFilter(
        input_class=AppTypeEnum, method=filter_app_type
    )
    app_role = EnumFilter(input_class=AppRoleTypeEnum, method=filter_app_role_type)
    vendor_id = django_filters.CharFilter(method=filter_by_vendor_id)
    branch_id = django_filters.CharFilter(method=filter_by_branch_id)
    group_name = django_filters.CharFilter(method=filter_by_group_name)
    vendor_user_type = ListObjectTypeFilter(
        input_class=VendorUserTypeEnum, method=filter_by_vendor_user_type
    )
    department_ids = GlobalIDMultipleChoiceFilter(field_name="departments")

class LanguageFilter(django_filters.FilterSet):
    display = django_filters.CharFilter(method=filter_display)
    code = django_filters.CharFilter(method=filter_code)


class BiometricLoginDeviceFilter(django_filters.FilterSet):
    device_name = django_filters.CharFilter(method=filter_device_name)
    device_id = django_filters.CharFilter(method=filter_device_id)
    type = ListObjectTypeFilter(input_class=BiometricLoginTypeEnum,
                                method=filter_device_type)
    created_date = ObjectTypeFilter(
        input_class=DateRangeInput, method=filter_by_created_date
    )
