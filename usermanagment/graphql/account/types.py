import graphene
from django.contrib.auth import get_user_model, models as auth_models
from django.db.models import Q
from graphene import relay
from graphene_federation import key

from .dataloaders.messages import MessagesByCustomerIdAndMessageIdLoader
from .enums import CustomerEventsEnum
from .utils import can_user_manage_group, get_groups_which_user_can_manage
from ..core.connection import CountableDjangoObjectType
from ..core.enums import LanguageCodeEnum
from ..core.fields import FilterInputConnectionField
from ..core.types.common import LocationType, LocationInput, Permission
from ..core.types.translations import BaseTranslationType
from ..core.utils import from_global_id_strict_type
from ..manager.types import Manager
from ..nurse.types import Nurse
from ..patient.types import UnverifiedNationals
from ..pharmacist.types import Pharmacist
from ..receptionist.types import Receptionist
from ..dental_hygienist.types import DentalHygienist
from ..diabetes_educator.types import DiabetesEducator
from ..fitness_coach.types import  Fitness<PERSON>oach
from ..nutritionist.types import Nutritionist
from ..optometrist.types import Optometrist
from ..podiatric_medical_assistant.types import PodiatricMedicalAssistant
from ..psychologist.types import Psychologist
from ..social_worker.types import SocialWorker
from ..utils import format_permissions_for_display
from ..vendor.types import Vendor
from ...account import models, ADDRESS_TRANSLATABLE_FIELDS
from ...auth.enums import VendorUserTypes
from ...doctor import models as doctor_models
from ...auth.decorators import one_of_permissions_required, permission_required
from ...auth.exceptions import PermissionDenied
from ...auth.graphql.enums import AppTypeEnum, AppRoleTypeEnum
from ...auth.permissions import AccountPermissions
from ...chat import models as chat_models
from ...doctor.models import Doctor


class AddressTranslatableFieldsInput(graphene.InputObjectType):
    name = graphene.String(description="Address Name.")
    name_ar = graphene.String(description="Address Name (Arabic).")
    street_address_1 = graphene.String(description="street Address 1.")
    street_address_1_ar = graphene.String(description="street Address 1 (Arabic).")
    street_address_2 = graphene.String(description="street Address 2.")
    street_address_2_ar = graphene.String(description="street Address 2 (Arabic).")
    area = graphene.String(description="Area.")
    area_ar = graphene.String(description="Area (Arabic).")
    district = graphene.String(description="District.")
    district_ar = graphene.String(description="District (Arabic).")
    region = graphene.String(description="Region")
    region_ar = graphene.String(description="Region (Arabic)")
    building_name = graphene.String(description="Building Name")
    building_name_ar = graphene.String(description="Building Name (Arabic)")


class AddressTranslationInput(AddressTranslatableFieldsInput):
    language_code = graphene.Argument(
        LanguageCodeEnum, required=True, description="Translation language code."
    )


class AddressInput(AddressTranslatableFieldsInput):
    postal_code = graphene.String(description="Postal code.")
    phone = graphene.String(description="Phone number.")
    building_number = graphene.String(description="Building number")
    unit_number = graphene.String(description="Apartment number")
    coordinates = LocationInput(required=True)
    translations = graphene.List(AddressTranslationInput)
    block = graphene.ID(description="Block.")
    city = graphene.ID(description="City.")


@key("id")
class Address(CountableDjangoObjectType):
    is_default_shipping_address = graphene.Boolean(
        required=False, description="Address is user's default shipping address."
    )
    is_default_billing_address = graphene.Boolean(
        required=False, description="Address is user's default billing address."
    )

    coordinates = graphene.Field(LocationType,
                                 description="Address location in lng/lat")

    class Meta:
        description = "Represents user address data."
        interfaces = [relay.Node]
        model = models.Address
        fields = [
            "name",
            "name_ar",
            "city",
            "block",
            "area",
            "area_ar",
            "id",
            "phone",
            "postal_code",
            "street_address_1",
            "street_address_1_ar",
            "street_address_2",
            "street_address_2_ar",
            "building_number",
            "building_name",
            "building_name_ar",
            "unit_number",
            "region",
            "region_ar",
            "district",
            "district_ar",
        ]

    @staticmethod
    def resolve_is_default_shipping_address(root: models.Address, _info):
        """Look if the address is the default shipping address of the user.

        This field is added through annotation when using the
        `resolve_addresses` resolver. It's invalid for
        `resolve_default_shipping_address` and
        `resolve_default_billing_address`
        """
        if not hasattr(root, "user_default_shipping_address_pk"):
            return None

        user_default_shipping_address_pk = getattr(
            root, "user_default_shipping_address_pk"
        )
        if user_default_shipping_address_pk == root.pk:
            return True
        return False

    @staticmethod
    def resolve_is_default_billing_address(root: models.Address, _info):
        """Look if the address is the default billing address of the user.

        This field is added through annotation when using the
        `resolve_addresses` resolver. It's invalid for
        `resolve_default_shipping_address` and
        `resolve_default_billing_address`
        """
        if not hasattr(root, "user_default_billing_address_pk"):
            return None

        user_default_billing_address_pk = getattr(
            root, "user_default_billing_address_pk"
        )
        if user_default_billing_address_pk == root.pk:
            return True
        return False

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Address)

class AddressTranslation(BaseTranslationType):
    class Meta:
        model = models.AddressTranslation
        interfaces = [graphene.relay.Node]
        fields = ADDRESS_TRANSLATABLE_FIELDS


class CustomerEvent(CountableDjangoObjectType):
    date = graphene.types.datetime.DateTime(
        description="Date when event happened at in ISO 8601 format."
    )
    type = CustomerEventsEnum(description="Customer event type.")
    user = graphene.Field(lambda: User, description="User who performed the action.")
    message = graphene.String(description="Content of the event.")
    count = graphene.Int(description="Number of objects concerned by the event.")

    class Meta:
        description = "History log of the customer."
        model = models.CustomerEvent
        interfaces = [relay.Node]
        fields = ["id"]

    @staticmethod
    def resolve_user(root: models.CustomerEvent, info):
        user = info.context.user
        if (
                user == root.user
                or user.has_perm(AccountPermissions.MANAGE_USERS)
                or user.has_perm(AccountPermissions.MANAGE_STAFF)
        ):
            return root.user
        raise PermissionDenied()

    @staticmethod
    def resolve_message(root: models.CustomerEvent, _info):
        return root.parameters.get("message", None)

    @staticmethod
    def resolve_count(root: models.CustomerEvent, _info):
        return root.parameters.get("count", None)


class UserPermission(Permission):
    source_permission_groups = graphene.List(
        graphene.NonNull("usermanagment.graphql.account.types.Group"),
        description="List of user permission groups which contains this permission.",
        user_id=graphene.Argument(
            graphene.ID,
            description="ID of user whose groups should be returned.",
            required=True,
        ),
        required=False,
    )

    def resolve_source_permission_groups(root: Permission, _info, user_id, **_kwargs):
        user_id = from_global_id_strict_type(user_id, only_type="User", field="pk")
        groups = auth_models.Group.objects.filter(
            user__pk=user_id, permissions__name=root.name
        )
        return groups

@key("code")
class DependentRelation(graphene.ObjectType):
    code =graphene.String()


@key("id")
class User(CountableDjangoObjectType):
    addresses = graphene.List(Address,
                              description="List of all user's addresses.")
    note = graphene.String(description="A note about the customer.")

    user_permissions = graphene.List(
        UserPermission, description="List of user's permissions."
    )
    permission_groups = graphene.List(
        "usermanagment.graphql.account.types.Group",
        description="List of user's permission groups.",
    )
    editable_groups = graphene.List(
        "usermanagment.graphql.account.types.Group",
        description="List of user's permission groups which user can manage.",
    )
    avatar = graphene.String(description="User avatar image.", required=False)
    events = graphene.List(
        CustomerEvent, description="List of events associated with the user."
    )

    app_type = AppTypeEnum()
    app_role = AppRoleTypeEnum()

    is_superuser = graphene.Boolean()
    is_staff = graphene.Boolean()
    is_vendor_admin = graphene.Boolean()

    branches = graphene.List("usermanagment.graphql.vendor.types.Branch")

    vendor = graphene.Field(Vendor)
    messages = FilterInputConnectionField(
        "usermanagment.graphql.chat.types.Message",
        branch_id=graphene.Argument(
            graphene.ID,
            description="messages with specific vendor-branch",
            required=True
        )
    )

    last_message = graphene.Field("usermanagment.graphql.chat.types.Message")

    meeting_platform_id = graphene.String(
        description="meeting platform id")

    date_of_birth = graphene.Date()

    mobile = graphene.String()

    national_id = graphene.String()
    nurse = graphene.Field(Nurse)
    pharmacist = graphene.Field(Pharmacist)
    receptionist = graphene.Field(Receptionist)
    manager = graphene.Field(Manager)
    dental_hygienist = graphene.Field(DentalHygienist)
    diabetes_educator = graphene.Field(DiabetesEducator)
    fitness_coach = graphene.Field(FitnessCoach)
    nutritionist = graphene.Field(Nutritionist)
    optometrist = graphene.Field(Optometrist)
    podiatric_medical_assistant = graphene.Field(PodiatricMedicalAssistant)
    psychologist = graphene.Field(Psychologist)
    social_worker = graphene.Field(SocialWorker)


    unverified_nationals = graphene.List(UnverifiedNationals)

    dependents = graphene.List("usermanagment.graphql.account.types.User")

    departments = graphene.List("usermanagment.graphql.vendor.types.Department")

    health_license_number = graphene.String(required=False)
    health_license_start_date = graphene.Date(required=False)
    health_license_end_date = graphene.Date(required=False)

    relation_type = graphene.String(required=False)

    relation = graphene.Field(DependentRelation, description="Dependent relation of the user.")

    ask_to_enable_bio_login = graphene.Boolean()

    class Meta:
        description = "Represents user data."
        interfaces = [relay.Node]
        model = get_user_model()
        fields = [
            "date_joined",
            "default_billing_address",
            "default_shipping_address",
            "email",
            "first_name",
            "second_name",
            "third_name",
            "last_name",
            "full_name",
            "first_name_ar",
            "second_name_ar",
            "third_name_ar",
            "last_name_ar",
            "full_name_ar",
            "id",
            "is_active",
            "last_login",
            "note",
            "mobile_verified",
            "email_verified",
            "meeting_platform_id",
            "app_type",
            "app_role",
            "vendor",
            "patient",
            "doctor",
            "gender",
            "photo",
            "vendor_user_type",
            "parent_user",
            "preferred_language",
            "terms_and_conditions_accepted_version",
            "default_branch",
            "two_factor_auth_enabled",
            "marketing_consent",
            "two_factor_auth_verification_method"
        ]

    @staticmethod
    def resolve_ask_to_enable_bio_login(root: models.User, _info, **_kwargs):
        return (not models.BiometricLoginDevice.objects.filter(user=root).exists()
                and root.ask_to_enable_bio_login)

    @staticmethod
    def resolve_relation_type(root: models.User, _info, **_kwargs):
        return root.relation_type

    @staticmethod
    def resolve_relation(root: models.User, _info, **_kwargs):
        if root.relation_type:
            return {"code": root.relation_type}

    @staticmethod
    def resolve_health_license_number(root: models.User, _info, **_kwargs):
        if not root.is_vendor:
            return None
        if root.vendor_user_type == VendorUserTypes.MANAGER:
            return None
        elif root.vendor_user_type == VendorUserTypes.PHARMACIST:
            return root.pharmacist.health_license_number
        elif root.vendor_user_type == VendorUserTypes.NURSE:
            return root.nurse.health_license_number
        elif root.vendor_user_type == VendorUserTypes.DOCTOR:
            return root.doctor.health_license_number
        elif root.vendor_user_type == VendorUserTypes.RECEPTIONIST:
            return None
        elif root.vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST:
            return root.dental_hygienist.health_license_number
        elif root.vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR:
            return root.diabetes_educator.health_license_number
        elif root.vendor_user_type == VendorUserTypes.FITNESS_COACH:
            return root.fitness_coach.health_license_number
        elif root.vendor_user_type == VendorUserTypes.NUTRITIONIST:
            return root.nutritionist.health_license_number
        elif root.vendor_user_type == VendorUserTypes.OPTOMETRIST:
            return root.optometrist.health_license_number
        elif root.vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT:
            return root.podiatric_medical_assistant.health_license_number
        elif root.vendor_user_type == VendorUserTypes.PSYCHOLOGIST:
            return root.psychologist.health_license_number
        elif root.vendor_user_type == VendorUserTypes.SOCIAL_WORKER:
            return root.social_worker.health_license_number

    @staticmethod
    def resolve_health_license_start_date(root: models.User, _info, **_kwargs):
        if not root.is_vendor:
            return None
        if root.vendor_user_type == VendorUserTypes.MANAGER:
            return None
        elif root.vendor_user_type == VendorUserTypes.PHARMACIST:
            return root.pharmacist.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.NURSE:
            return root.nurse.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.DOCTOR:
            return root.doctor.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.RECEPTIONIST:
            return None
        elif root.vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST:
            return root.dental_hygienist.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR:
            return root.diabetes_educator.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.FITNESS_COACH:
            return root.fitness_coach.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.NUTRITIONIST:
            return root.nutritionist.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.OPTOMETRIST:
            return root.optometrist.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT:
            return root.podiatric_medical_assistant.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.PSYCHOLOGIST:
            return root.psychologist.health_license_start_date
        elif root.vendor_user_type == VendorUserTypes.SOCIAL_WORKER:
            return root.social_worker.health_license_start_date

    @staticmethod
    def resolve_health_license_end_date(root: models.User, _info, **_kwargs):
        if not root.is_vendor:
            return None
        if root.vendor_user_type == VendorUserTypes.MANAGER:
            return None
        elif root.vendor_user_type == VendorUserTypes.PHARMACIST:
            return root.pharmacist.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.NURSE:
            return root.nurse.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.DOCTOR:
            return root.doctor.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.RECEPTIONIST:
            return None
        elif root.vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST:
            return root.dental_hygienist.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR:
            return root.diabetes_educator.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.FITNESS_COACH:
            return root.fitness_coach.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.NUTRITIONIST:
            return root.nutritionist.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.OPTOMETRIST:
            return root.optometrist.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT:
            return root.podiatric_medical_assistant.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.PSYCHOLOGIST:
            return root.psychologist.health_license_end_date
        elif root.vendor_user_type == VendorUserTypes.SOCIAL_WORKER:
            return root.social_worker.health_license_end_date

    @staticmethod
    def resolve_addresses(root: models.User, _info, **_kwargs):
        if root.is_doctor:
            if root.is_doctor:
                if doctor_models.Doctor.objects.is_accessible_property(root,
                                                                root.doctor.is_address_public):
                    return root.addresses.annotate_default(root).all()
                return None
        return root.addresses.annotate_default(root).all()

    @staticmethod
    def resolve_permissions(root: models.User, _info, **_kwargs):
        # deprecated, to remove in #5389
        from .resolvers import resolve_permissions

        return resolve_permissions(root)

    @staticmethod
    def resolve_user_permissions(root: models.User, _info, **_kwargs):
        from .resolvers import resolve_permissions

        return resolve_permissions(root)

    @staticmethod
    def resolve_permission_groups(root: models.User, _info, **_kwargs):
        return root.groups.all()

    @staticmethod
    def resolve_editable_groups(root: models.User, _info, **_kwargs):
        return get_groups_which_user_can_manage(root)

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_note(root: models.User, info):
        return root.note

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_events(root: models.User, _info):
        return root.events.all()

    @staticmethod
    def resolve_avatar(root: models.User, _info, **_kwargs):
        return root.avatar

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, User)

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_is_staff(root: models.User, info):
        return root.is_staff

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_is_active(root: models.User, info):
        return root.is_active

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_is_vendor_admin(root: models.User, info):
        return root.is_vendor_admin

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_is_superuser(root: models.User, info):
        return root.is_superuser

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.MANAGE_STAFF,
         AccountPermissions.VIEW_USERS]
    )
    def resolve_branches(root: models.User, info):
        if root.is_superuser or root.is_staff or root.is_consumer:
            return []
        if root.is_vendor_admin:
            return root.vendor.branches.all()
        return root.branches.filter(is_active=True)

    @staticmethod
    def resolve_departments(root: models.User, info):
        if root.is_superuser or root.is_staff or root.is_consumer:
            return []
        return root.departments.all()

    @staticmethod
    def resolve_vendor(root: models.User, info):
        if root.is_superuser or root.is_staff or root.is_consumer:
            return None
        return root.vendor

    @staticmethod
    def resolve_patient(root: models.User, info, **_kwargs):
        if root.is_superuser or root.is_staff or root.is_vendor:
            return None
        return getattr(root, 'patient', None)

    @staticmethod
    def resolve_last_message(root, info, branch=None, **kwargs):
        return MessagesByCustomerIdAndMessageIdLoader(info.context) \
            .load(f'{root.id}__{root.last_message_id}')

    @staticmethod
    def resolve_messages(root: models.User, info, branch_id, **kwargs):
        # imported here to avoid circular import
        from ..branch.types import Branch
        branch = graphene.Node.get_node_from_global_id(info, branch_id,
                                                       only_type=Branch)
        current_user = info.context.user
        if current_user != root \
                and branch.vendor_id != current_user.vendor_id:
            raise PermissionDenied()

        return chat_models.Message.objects.filter(
            Q(branch_id=branch.id) & (Q(sender_id=root.id) | Q(recipient_id=root.id))) \
            .select_related("branch", "branch__vendor", "sender", "recipient") \
            .prefetch_related("attachments")

    @staticmethod
    def resolve_doctor(root: models.User, info, **_kwargs):
        return getattr(root, 'doctor', None)

    @staticmethod
    def resolve_date_of_birth(root: models.User, info, **kwargs):
        if root.is_doctor:
            if Doctor.objects.is_accessible_property(root,
                                                     root.doctor.is_date_of_birth_public):
                return root.date_of_birth
            return None

        return root.date_of_birth

    @staticmethod
    def resolve_mobile(root: models.User, info, **kwargs):
        if root.is_doctor:
            if doctor_models.Doctor.objects.is_accessible_property(root,
                                                            root.doctor.is_mobile_number_public):
                return root.mobile
            return None

        return root.mobile

    @staticmethod
    def resolve_national_id(root: models.User, info, **kwargs):
        if root.is_doctor:
            if doctor_models.Doctor.objects.is_accessible_property(root,
                                                            root.doctor.is_national_id_public):
                return root.national_id

        return root.national_id

    @staticmethod
    def resolve_unverified_nationals(root: models.User, info, **kwargs):
        return root.unverifiednationals_set.all()

    @staticmethod
    def resolve_dependents(root: models.User, info, **kwargs):
        return root.dependents.all()


class ChoiceValue(graphene.ObjectType):
    raw = graphene.String()
    verbose = graphene.String()


class GroupConfiguration(graphene.ObjectType):
    keycloak_group_id = graphene.String()
    group_type = AppTypeEnum()
    is_editable = graphene.Boolean()
    is_global = graphene.Boolean()
    vendor = graphene.Field(Vendor)
    created = graphene.DateTime()


class Group(CountableDjangoObjectType):
    users = graphene.List(User, description="List of group users")
    permissions = graphene.List(Permission, description="List of group permissions")
    user_can_manage = graphene.Boolean(
        required=True,
        description=(
            "True, if the currently authenticated user has rights to manage a group."
        ),
    )
    group_configuration = graphene.Field(GroupConfiguration)
    created = graphene.DateTime()
    created_by = graphene.Field(User)
    modified = graphene.DateTime()
    modified_by = graphene.Field(User)

    class Meta:
        description = "Represents permission group data."
        interfaces = [relay.Node]
        model = auth_models.Group
        fields = ["name", "permissions", "id", "group_configuration"]

    @staticmethod
    @permission_required(AccountPermissions.MANAGE_USERS)
    def resolve_users(root: auth_models.Group, _info):
        return root.user_set.all()

    @staticmethod
    def resolve_permissions(root: auth_models.Group, _info):
        permissions = root.permissions.order_by(
            "codename"
        )
        return format_permissions_for_display(permissions)

    @staticmethod
    def resolve_user_can_manage(root: auth_models.Group, info):
        user = info.context.user
        return can_user_manage_group(user, root)

    @staticmethod
    def resolve_group_configuration(root: auth_models.Group, info):
        return root.group_configuration

    @staticmethod
    def resolve_created_by(root: auth_models.Group, _info):
        if hasattr(root, 'audit') and root.audit:
            return root.audit.created_by

    @staticmethod
    def resolve_modified_by(root: auth_models.Group, _info):
        if hasattr(root, 'audit') and root.audit:
            return root.audit.modified_by

    @staticmethod
    def resolve_created(root: auth_models.Group, _info):
        if hasattr(root, 'audit') and root.audit:
            return root.audit.created

    @staticmethod
    def resolve_modified(root: auth_models.Group, _info):
        if hasattr(root, 'audit') and root.audit:
            return root.audit.modified


class ConsumerViewPreference(CountableDjangoObjectType):
    class Meta:
        description = "Consumer View Preference"
        interfaces = [relay.Node]
        model = models.ConsumerViewPreference


class Language(CountableDjangoObjectType):
    class Meta:
        description = "Language"
        interfaces = [relay.Node]
        model = models.Language
        fields = [
            "id",
            "code",
            "display",
            "display_ar",
        ]


class BiometricLoginDevice(CountableDjangoObjectType):
    class Meta:
        description = "Biometric Login Device"
        interfaces = [relay.Node]
        model = models.BiometricLoginDevice
        fields = [
            "id",
            "device_id",
            "device_name",
            "type"
        ]
