import graphene
from django_countries import countries

from .filters import (
    Customer<PERSON>ilter,
    PermissionGroupFilter,
    CustomerDashboardFilter,
    UserFilter, LanguageFilter, BiometricLoginDeviceFilter,
)
from .mutations.account import (
    AccountAddressCreate,
    AccountAddressDelete,
    AccountAddressUpdate,
    AccountSetDefaultAddress,
    AccountAvatarDelete,
    AccountAvatarUpdate,
    TwoFactorAuthEnable,
    VerifyAuthenticatorCodeForFirstTime,
    VerifyTwoFactorAuth, VerifyOTPForLogin,
    ConsentMarketing,
)
from .mutations.base import (
    ConfirmAccountMobile,
    PasswordChange,
    RequestPasswordReset,
    SetPassword,
    ConfirmAccountEmail,
    ConfirmPasswordRestOTP, RequestOTP, ConfirmVerification,
    UpdateUserEmailMobile, LanguageCreate, LanguageUpdate, RequestOTPForDeleteAccount,
    VerifyCredentials, EnableBiometricLogin, BiometricLogin, BiometricLoginDeviceDelete,
    SendBulkMessages, SendBulkMessagesBySearchCriteria,
)
from .mutations.consumer_view_preference import ConsumerViewPreferenceSave
from .mutations.permission_group import (
    PermissionGroupCreate,
    PermissionGroupDelete,
    PermissionGroupUpdate,
    KeycloakPermissionConfigurationUpdate,
)
from .mutations.users import UserCreate, UserUpdate, UserDelete, \
    MeetingPlatformIdUpdate, UserProfileUpdate, UnbindParent, UnbindDependent, \
    ApproveTermsAndConditions, DeleteCurrentCustomerUser, UserActiveStatusUpdate, \
    UpdateAskToEnableBioLogin
from .mutations.vendor_users import (
    VendorUserCreate,
    VendorUserUpdate,
    VendorUserDelete
)
from .resolvers import (
    resolve_address,
    resolve_customers,
    resolve_permission_groups,
    resolve_user,
    resolve_customers_dashboard,
    resolve_users,
    resolve_customer,
    resolve_consumer_view_preference,
    resolve_medlist_users, resolve_group_candidate_users
)
from .sorters import PermissionGroupSortingInput, UserSortingInput, \
    BiometricLoginDeviceSortingInput
from .types import Address, Group, User, ConsumerViewPreference, Language, \
    BiometricLoginDevice
from ..core.fields import FilterInputConnectionField
from ..core.types import FilterInputObjectType
from ..core.types.aggregation import Aggregation
from ..core.types.common import CountryDisplay, Permission
from ..utils import format_permissions_for_display, get_database_id
from ..utils.request_utils import get_current_user
from ...account import models
from ...auth.decorators import permission_required, one_of_permissions_required
from ...auth.enums import AppTypes
from ...auth.exceptions import PermissionDenied
from ...auth.graphql.enums import AppTypeEnum
from ...auth.permissions import AccountPermissions, TerminologyPermissions
from ...core.permissions import get_permissions


class CustomerFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = CustomerFilter


class CustomerDashboardFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = CustomerDashboardFilter


class PermissionGroupFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = PermissionGroupFilter


class UserFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = UserFilter


class LanguageFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = LanguageFilter


class BiometricLoginDeviceFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = BiometricLoginDeviceFilter


class AccountQueries(graphene.ObjectType):
    address = graphene.Field(
        Address,
        id=graphene.Argument(
            graphene.ID, description="ID of an address.", required=True
        ),
        description="Look up an address by ID.",
    )
    customers = FilterInputConnectionField(
        User,
        branch=graphene.Argument(graphene.ID, required=False),
        has_chat_with=graphene.Argument(graphene.Boolean, required=False),
        filter=CustomerFilterInput(description="Filtering options for customers."),
        sort_by=UserSortingInput(description="Sort customers."),
        description="List of the shop's customers.",
    )

    customers_dashboard = graphene.List(
        Aggregation,
        filter=CustomerDashboardFilterInput(
            description="Filtering options for customers."),
        description="Aggregated Customers",
    )

    permissions = graphene.List(
        Permission,
        description="List of available permissions.", required=True
    )

    key_cloak_permissions = graphene.List(
        Permission,
        app_type=AppTypeEnum(required=True),
        description="List of permission for target User",
    )

    permission_groups = FilterInputConnectionField(
        Group,
        app_type=AppTypeEnum(required=True),
        vendor_id=graphene.Argument(graphene.ID, description="ID of the vendor user.", required=False),
        filter=PermissionGroupFilterInput(
            description="Filtering options for permission groups."
        ),
        sort_by=PermissionGroupSortingInput(description="Sort permission groups."),
        description="List of permission groups.",
    )
    permission_group = graphene.Field(
        Group,
        id=graphene.Argument(
            graphene.ID, description="ID of the group.", required=True
        ),
        description="Look up permission group by ID.",
    )
    me = graphene.Field(User, description="Return the currently authenticated user.")

    user = graphene.Field(
        User,
        id=graphene.Argument(graphene.ID, description="ID of the user.", required=True),
        description="Look up a user by ID.",
    )

    countries = graphene.List(CountryDisplay, description="Countries")

    users = FilterInputConnectionField(
        User,
        filter=UserFilterInput(description="Filtering options for users."),
        sort_by=UserSortingInput(description="Sort users."),
        description="List of the shop's users.",
    )

    group_candidate_users = FilterInputConnectionField(
        User,
        app_type=AppTypeEnum(required=True),
        vendor_id=graphene.Argument(graphene.ID, description="ID of the vendor user.",
                                    required=False),
        filter=UserFilterInput(description="Filtering options for users."),
        sort_by=UserSortingInput(description="Sort users."),
        description="List of the shop's users.",
    )

    medlist_users = FilterInputConnectionField(
        User,
        filter=UserFilterInput(description="Filtering options for users. allowed to "
                                           "manage medical lists"),
        sort_by=UserSortingInput(description="Sort users."),
        description="List of users allowed to modify the medical lists",
    )
    customer = graphene.Field(
        User,
        national_id=graphene.Argument(graphene.String,
                                      description="national ID of the customer.",
                                      required=True),
        description="Look up a customer by National ID.",
    )

    consumer_view_preference = graphene.Field(
        ConsumerViewPreference,
        view=graphene.Argument(
            graphene.String,
            description="View name",
            required=True,
        ),
        description="Look up a preference by view name",
    )

    language = graphene.Field(
        Language,
        id=graphene.Argument(graphene.ID, description="ID of the language.",
                             required=True),
        description="Look up a language by ID.",
    )

    languages = FilterInputConnectionField(
        Language,
        description="List of languages.",
        filter=LanguageFilterInput(description="Filtering options for users."),
    )

    biometric_login_devices = FilterInputConnectionField(
        BiometricLoginDevice,
        filter=BiometricLoginDeviceFilterInput(
            description="Filtering options for biometric login devices."),
        sort_by=BiometricLoginDeviceSortingInput(
            description="Sort biometric login devices."),
        description="List of biometric login devices.",
    )


    @permission_required(AccountPermissions.VIEW_CUSTOMERS)
    def resolve_customers(self, info, branch=None, has_chat_with=False, **kwargs):
        branch_id = None
        if branch:
            branch_id = get_database_id(branch, "Branch")
        return resolve_customers(branch_id=branch_id, has_chat_with=has_chat_with)

    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.VIEW_USERS])
    def resolve_customers_dashboard(self, info, query=None, **kwargs):
        return resolve_customers_dashboard(info, query=query, **kwargs)

    @one_of_permissions_required(
        [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS,
         AccountPermissions.VIEW_STAFF, AccountPermissions.VIEW_USERS,
         AccountPermissions.MANAGE_PERMISSION_GROUP])
    def resolve_permission_groups(self, info, app_type, vendor_id=None, query=None,
                                  **kwargs):
        user = get_current_user()
        if (user.is_vendor and app_type != AppTypes.VENDOR) or (
                user.is_consumer and app_type != AppTypes.CUSTOMER) \
                or (user.is_aggregator and app_type != AppTypes.AGGREGATOR):
            raise PermissionDenied()

        return resolve_permission_groups(info, user, app_type, vendor_id,
                                         query=query, **kwargs)

    @one_of_permissions_required(
        [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS,
         AccountPermissions.VIEW_STAFF, AccountPermissions.VIEW_USERS,
         AccountPermissions.MANAGE_PERMISSION_GROUP])
    def resolve_permission_group(self, info, id):
        return graphene.Node.get_node_from_global_id(info, id, Group)

    @staticmethod
    def resolve_permissions(_, _info):
        current_user = get_current_user()
        if not current_user.is_authenticated or not current_user.is_admin:
            return []
        permissions = get_permissions()
        return format_permissions_for_display(permissions)

    @one_of_permissions_required(
        [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS,
         AccountPermissions.VIEW_STAFF, AccountPermissions.VIEW_USERS]
    )
    def resolve_key_cloak_permissions(self, info, app_type, **kwargs):
        user = get_current_user()
        if (user.is_vendor and app_type != AppTypes.VENDOR) or (
                user.is_consumer and app_type != AppTypes.CUSTOMER) \
                or (user.is_aggregator and app_type != AppTypes.AGGREGATOR):
            raise PermissionDenied()

        permissions = get_permissions(app_type=app_type)
        return format_permissions_for_display(permissions)

    def resolve_me(self, info):
        user = info.context.user
        return user if user.is_authenticated else None

    @one_of_permissions_required(
        [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS,
         AccountPermissions.VIEW_STAFF, AccountPermissions.VIEW_USERS]
    )
    def resolve_user(self, info, id):
        return resolve_user(info, id)

    def resolve_address(self, info, id):
        return resolve_address(info, id)

    def resolve_countries(self, info, **kwargs):
        return [CountryDisplay(code=country[0], country=country[1])
                for country in countries]

    @staticmethod
    def resolve_biometric_login_devices(_root, info, **kwargs):
        current_user = get_current_user()
        return models.BiometricLoginDevice.objects.filter(user_id=current_user.id).all()


    @one_of_permissions_required(
        [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS,
         AccountPermissions.VIEW_STAFF, AccountPermissions.VIEW_USERS]
    )
    def resolve_users(self, info, *_args, **_kwargs):
        return resolve_users(info)

    @one_of_permissions_required(
        [AccountPermissions.MANAGE_STAFF, AccountPermissions.MANAGE_USERS,
         AccountPermissions.VIEW_STAFF, AccountPermissions.VIEW_USERS]
    )
    def resolve_group_candidate_users(self, info, app_type, vendor_id=None, *_args,
                                      **_kwargs):
        user = get_current_user()
        if (user.is_vendor and app_type != AppTypes.VENDOR) or (
                user.is_consumer and app_type != AppTypes.CUSTOMER) \
                or (user.is_aggregator and app_type != AppTypes.AGGREGATOR):
            raise PermissionDenied()

        return resolve_group_candidate_users(info, user, app_type, vendor_id)

    @one_of_permissions_required([TerminologyPermissions.MANAGE_CODE_SYSTEM_LISTS,
                                  TerminologyPermissions.VIEW_CODE_SYSTEM_LISTS])
    def resolve_medlist_users(self, info, *_args, **_kwargs):
        return resolve_medlist_users(info)

    @permission_required(AccountPermissions.VIEW_CUSTOMERS)
    def resolve_customer(self, info, national_id):
        return resolve_customer(info, national_id)

    @staticmethod
    def resolve_consumer_view_preference(_root, info, view, **_args):
        current_user = get_current_user()
        if not current_user.is_authenticated:
            return None
        return resolve_consumer_view_preference(info, get_current_user(), view)

    @staticmethod
    def resolve_language(_root, info, id, **_args):
        return graphene.Node.get_node_from_global_id(info, id, Language)


class AccountMutations(graphene.ObjectType):
    # Base mutations
    request_password_reset = RequestPasswordReset.Field()
    confirm_account_mobile = ConfirmAccountMobile.Field()
    confirm_account_email = ConfirmAccountEmail.Field()
    confirm_password_rest_otp = ConfirmPasswordRestOTP.Field()
    set_password = SetPassword.Field()
    password_change = PasswordChange.Field()

    # Account mutations
    account_address_create = AccountAddressCreate.Field()
    account_address_update = AccountAddressUpdate.Field()
    account_address_delete = AccountAddressDelete.Field()
    account_set_default_address = AccountSetDefaultAddress.Field()

    request_OTP = RequestOTP.Field()
    verify_credentials = VerifyCredentials.Field()
    request_otp_for_delete_account = RequestOTPForDeleteAccount.Field()
    confirm_verification = ConfirmVerification.Field()

    enable_biometric_login = EnableBiometricLogin.Field()
    biometric_login = BiometricLogin.Field()
    biometric_login_device_delete = BiometricLoginDeviceDelete.Field()

    user_avatar_update = AccountAvatarUpdate.Field()
    user_avatar_delete = AccountAvatarDelete.Field()

    two_factor_auth_enable = TwoFactorAuthEnable.Field()
    verify_authenticator_code_for_first_time = VerifyAuthenticatorCodeForFirstTime.Field()
    verify_two_factor_auth = VerifyTwoFactorAuth.Field()
    verify_otp_for_login = VerifyOTPForLogin.Field()
    consent_marketing = ConsentMarketing.Field()

    # Permission group mutations
    keycloak_permission_configuration_update = KeycloakPermissionConfigurationUpdate.Field()
    permission_group_create = PermissionGroupCreate.Field()
    permission_group_update = PermissionGroupUpdate.Field()
    permission_group_delete = PermissionGroupDelete.Field()

    user_create = UserCreate.Field()
    user_update = UserUpdate.Field()
    user_delete = UserDelete.Field()
    user_active_status_update = UserActiveStatusUpdate.Field()

    vendor_user_create = VendorUserCreate.Field()
    vendor_user_update = VendorUserUpdate.Field()
    vendor_user_delete = VendorUserDelete.Field()

    meeting_platform_update = MeetingPlatformIdUpdate.Field()

    # Consumer view mutations
    consumer_view_preference_save = ConsumerViewPreferenceSave.Field()

    user_profile_update = UserProfileUpdate.Field()
    update_user_email_mobile = UpdateUserEmailMobile.Field()

    unbind_parent = UnbindParent.Field()
    unbind_dependent = UnbindDependent.Field()

    approve_terms_and_conditions = ApproveTermsAndConditions.Field()
    delete_current_customer_user = DeleteCurrentCustomerUser.Field()

    language_create = LanguageCreate.Field()
    language_update = LanguageUpdate.Field()

    update_ask_to_enable_bio_login = UpdateAskToEnableBioLogin.Field()

    send_bulk_messages = SendBulkMessages.Field()
    send_bulk_messages_by_search_criteria = SendBulkMessagesBySearchCriteria.Field()
