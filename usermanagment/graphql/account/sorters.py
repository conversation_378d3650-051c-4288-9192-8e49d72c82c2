import datetime

import graphene
from django.core.exceptions import ValidationError
from django.db.models import QuerySet, Case, F, When, Value
from django.db.models.aggregates import Max
from django.db.models.functions import Coalesce
from ..core.types import SortInputObjectType


class PatientSortField (graphene.Enum):
    CREATED_DATE = ["created", "pk"]

    @property
    def description(self):
        if self.name in PatientSortField.__enum__._member_names_:
            sort_name = self.name.lower().replace("_", " ")
            return f"Sort patients by {sort_name}."
        raise ValueError("Unsupported enum value: %s" % self.value)

class UserSortField(graphene.Enum):
    FIRST_NAME = ["first_name", "last_name", "pk"]
    LAST_NAME = ["last_name", "first_name", "pk"]
    EMAIL = ["email", "pk"]
    ORDER_COUNT = ["total_orders_count", "pk"]
    LAST_MESSAGE_SENT = ['last_message_created', "pk"]
    DATE_JOINED = ["date_joined", "pk"]

    @property
    def description(self):
        if self.name in UserSortField.__enum__._member_names_:
            sort_name = self.name.lower().replace("_", " ")
            return f"Sort users by {sort_name}."
        raise ValueError("Unsupported enum value: %s" % self.value)

    @staticmethod
    def qs_with_last_message_sent(queryset: QuerySet, args: dict) -> QuerySet:

        has_chat_with = args.get('has_chat_with', False)
        if not has_chat_with:
            raise ValidationError({
                "hasChatWith": "this field is required to sort by LAST_MESSAGE_SENT"
            })
        return queryset.annotate(
            last_sent_message_created=Coalesce(Value(Max(F('sentmessages__created'))),
                                               Value(datetime.date.min)),
            last_sent_message_id=Coalesce(Max('sentmessages__id'), -1),
            last_received_message_created=Coalesce(Value(Max(F('receivedmessages__created'))),
                                                   Value(datetime.date.min)),
            last_received_message_id=Coalesce(Max('receivedmessages__id'), -1)) \
            .annotate(last_message_created=Case(
            When(last_sent_message_created__gt=F('last_received_message_created'),
                 then="last_sent_message_created"),
            default='last_received_message_created')) \
            .annotate(last_message_id=Case(
            When(last_sent_message_id__gt=F('last_received_message_id'),
                 then="last_sent_message_id"),
            default='last_received_message_id'))


class UserSortingInput(SortInputObjectType):
    class Meta:
        sort_enum = UserSortField
        type_name = "users"


class BiometricLoginDeviceSortField(graphene.Enum):
    CREATED_DATE = ["created"]

    @property
    def description(self):
        # pylint: disable=no-member
        if self in [BiometricLoginDeviceSortField.CREATED_DATE]:
            sort_name = self.name.lower().replace("_", " ")
            return f"Sort biometric login devices by {sort_name}."
        raise ValueError("Unsupported enum value: %s" % self.value)


class BiometricLoginDeviceSortingInput(SortInputObjectType):
    class Meta:
        sort_enum = BiometricLoginDeviceSortField
        type_name = "biometric login device"


class PermissionGroupSortField(graphene.Enum):
    NAME = ["name"]
    CREATED_DATE = ["group_configuration__created"]

    @property
    def description(self):
        # pylint: disable=no-member
        if self in [PermissionGroupSortField.NAME,PermissionGroupSortField.CREATED_DATE]:
            sort_name = self.name.lower().replace("_", " ")
            return f"Sort permission group accounts by {sort_name}."
        raise ValueError("Unsupported enum value: %s" % self.value)


class PermissionGroupSortingInput(SortInputObjectType):
    class Meta:
        sort_enum = PermissionGroupSortField
        type_name = "permission group"


class PatientSortingInput(SortInputObjectType):
    class Meta:
        sort_enum = PatientSortField
        type_name = "patients"
