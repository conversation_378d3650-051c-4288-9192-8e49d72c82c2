import graphene
import pyotp
from django.contrib.auth import password_validation
from django.core.exceptions import ValidationError
from django.db import transaction
from usermanagment.graphql_client.backend_client import BackendClient

from .base import (
    BaseAddressDelete,
    can_edit_address, check_email_verification, check_contact_number_verification,
    check_and_confirm_verification,
)
from ..i18n import AddressCreate
from ...account.enums import AddressTypeEnum, TwoAuthVerificationMethodEnum
from ...account.types import Address, AddressInput, User
from ...core.mutations import BaseMutation, ModelMutation
from ...core.types.common import AccountError
from ...core.utils import from_global_id_strict_type
from ...core.utils.token_utils import prepare_user_attributes, prepare_user_branches
from ...utils.request_utils import get_current_user
from ....account import events as account_events, models, utils, \
    AddressType, TwoAuthVerificationMethod
from ....account.error_codes import AccountErrorCode
from ....account.validators import validate_possible_number
from ....auth.exceptions import PermissionDenied
from ....auth.features import Feature
from ....block.models import Block
from ....core.utils.encryotion_utils import EncryptionUtils
from ....keycloak.keycloak_client import KeycloakAPI
from ....patient import models as patient_models
from ....settings import get_bool_from_env


class AccountRegisterInput(graphene.InputObjectType):
    first_name = graphene.String(required=False)
    email = graphene.String(required=False)
    last_name = graphene.String(required=False)
    national_id = graphene.String(description="The national id the consumer.",
                                  required=True)
    mobile = graphene.String(description="consumer mobile number.", required=True)
    password = graphene.String(description="Password.", required=True)


class VerifyOTPForLoginInput(graphene.InputObjectType):
    code = graphene.String(required=True)
    session_token = graphene.String(required=False)
    email = graphene.String(required=False)
    mobile = graphene.String(required=False)

class VerifyTwoFactorAuthInput(graphene.InputObjectType):
    code = graphene.String(required=True)
    session_token = graphene.String(required=False)
    user_id = graphene.ID(required=True)

class VerifyAuthenticatorCodeForFirstTimeInput(graphene.InputObjectType):
    code = graphene.String(required=True)

class AccountRegister(ModelMutation):
    class Arguments:
        input = AccountRegisterInput(
            description="Fields required to create a user.", required=True
        )

    session_token = graphene.String(
        description="sessionToken to resent with each request"
    )

    class Meta:
        description = "Register a new user."
        exclude = ["password"]
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def mutate(cls, root, info, **data):
        response = super().mutate(root, info, **data)
        return response

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        password = data.get("password")
        if password:
            try:
                password_validation.validate_password(password, instance)
            except ValidationError as error:
                raise ValidationError({"password": error})

        mobile = data.get("mobile")
        if mobile:
            try:
                validate_possible_number(mobile)
            except ValidationError as error:
                raise ValidationError({"mobile": error})

        cleaned_data = super().clean_input(info, instance, data, input_cls=None)

        return cleaned_data

    @classmethod
    def save(cls, info, user, cleaned_input):
        user.is_active = True
        user.set_password(user.password)
        user.save()
        account_events.customer_account_created_event(user=user)

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        patient_registration = data.get("patient_registration", False)
        if not patient_registration:
            patient_models.Patient.objects.update_or_create(
                national_id_number=result.user.national_id,
                defaults={
                    "contact_number": str(result.user.mobile),
                    "user": result.user
                }
            )

        user = result.user
        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(user, keycloak_user_data["attributes"])
        prepare_user_branches([], user, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(user.sso_id, keycloak_user_data)
        return result


class AccountInput(graphene.InputObjectType):
    first_name = graphene.String(description="Given name.")
    last_name = graphene.String(description="Family name.")
    default_billing_address = AddressInput(
        description="Billing address of the customer."
    )
    default_shipping_address = AddressInput(
        description="Shipping address of the customer."
    )


class AccountUpdate(ModelMutation):
    class Arguments:
        input = AccountInput(
            description="Fields required to update the account of the logged-in user.",
            required=True,
        )

    class Meta:
        description = "Updates the account of the logged-in user."
        exclude = ["password"]
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, root, info, **data):
        user = info.context.user
        data["id"] = data.get("id", graphene.Node.to_global_id("User", user.id))
        return super().perform_mutation(root, info, **data)


class AccountAddressCreate(AddressCreate):
    user = graphene.Field(
        User, description="A user instance for which the address was created."
    )

    class Arguments:
        input = AddressInput(
            description="Fields required to create address.", required=True
        )
        type = AddressTypeEnum(
            required=False,
            description=(
                "A type of address. If provided, the new address will be "
                "automatically assigned as the customer's default address "
                "of that type."
            ),
        )

    class Meta:
        description = "Create a new address for the customer."
        model = models.Address
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls)
        block_id = data.get("block", instance.block_id)
        city_id = data.get("city", instance.city_id)

        # Validate that the block is linked to the provided city
        if block_id and city_id:
            city_id = from_global_id_strict_type(city_id, "City")
            block_id = from_global_id_strict_type(block_id, "Block")
            if not Block.objects.filter(id=block_id, city_id=city_id).exists():
                raise ValidationError({
                    "block": "The provided block is not linked to the specified city."
                })

        return cleaned_data

    @classmethod
    def perform_mutation(cls, root, info, **data):
        address_type = data.get("type", None)
        user = info.context.user
        result = super().perform_mutation(root, info, **data)
        if address_type:
            utils.change_user_default_address(user, result.address, address_type)
        return AccountAddressCreate(user=user, address=result.address)

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):
        super().save(info, instance, cleaned_input)
        user = info.context.user
        instance.user_addresses.add(user)


class AccountAddressUpdate(AccountAddressCreate):
    class Arguments:
        id = graphene.ID(required=True, description="ID of the address to updated.")
        input = AddressInput(
            description="Fields required to create address.", required=True
        )

    class Meta:
        description = "Updates an address of the logged-in user."
        model = models.Address
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not can_edit_address(info.context.user, instance):
            raise PermissionDenied()

        return {}


class AccountAddressDelete(BaseAddressDelete):
    class Meta:
        description = "Delete an address of the logged-in user."
        model = models.Address
        error_type_class = AccountError
        error_type_field = "account_errors"


class AccountSetDefaultAddress(BaseMutation):
    user = graphene.Field(User, description="An updated user instance.")

    class Arguments:
        id = graphene.ID(
            required=True, description="ID of the address to set as default."
        )
        type = AddressTypeEnum(required=True, description="The type of address.")

    class Meta:
        description = "Sets a default address for the authenticated user."
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        address = cls.get_node_or_error(info, data.get("id"), Address)
        user = info.context.user

        if not user.addresses.filter(pk=address.pk).exists():
            raise ValidationError(
                {
                    "id": ValidationError(
                        "The address doesn't belong to that user.",
                        code=AccountErrorCode.INVALID,
                    )
                }
            )

        if data.get("type") == AddressTypeEnum.BILLING.value:
            address_type = AddressType.BILLING
        else:
            address_type = AddressType.SHIPPING

        utils.change_user_default_address(user, address, address_type)
        return cls(user=user)


class SSOAccountRegister(AccountRegister):
    class Arguments:
        input = AccountRegisterInput(
            description="Fields required to create a sso user.", required=True
        )

    class Meta:
        description = "Register a new sso user."
        exclude = ["password"]
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data, input_cls)
        cleaned_input['username'] = cleaned_input['national_id']
        return cleaned_input

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):

        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(instance, keycloak_user_data["attributes"])
        prepare_user_branches([], instance, keycloak_user_data["attributes"])

        response_data = KeycloakAPI().create_user({
            'username': cleaned_input['national_id'],
            'email': cleaned_input['email'],
            'credentials': [{
                'value': cleaned_input.pop('password'),
                'type': "password",
                'temporary': False
            }],
            'firstName': cleaned_input.get('first_name'),
            'lastName': cleaned_input.get('last_name'),
            'enabled': True,
            'attributes': keycloak_user_data["attributes"]
        })

        if not response_data:
            raise ValidationError({
                "sso": "user creation failed on sso server please contact super admin"
            })

        instance.sso_id = response_data
        try:
            with transaction.atomic():
                super().save(info, instance, cleaned_input)
        except Exception as e:
            KeycloakAPI().delete_user(instance.sso_id)
            raise e

    @classmethod
    def mutate(cls, root, info, **data):
        raise ValidationError("This mutation is deprecated, use 'customerCreate' "
                              "mutation instead")


class SSOAccountUpdate(AccountUpdate):
    class Meta:
        description = "Updates the account of the logged-in user."
        exclude = ["password"]
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):
        data = {
            'firstName': instance.first_name,
            'lastName': instance.last_name,
        }

        KeycloakAPI().update_account(data)

        super().save(info, instance, cleaned_input)

    @classmethod
    def mutate(cls, root, info, **data):
        raise ValidationError("This mutation is deprecated, use 'customerUpdate' "
                              "mutation instead")


class AccountAvatarUpdate(BaseMutation):
    user = graphene.Field(User, description="An updated user instance.")

    class Arguments:
        image = graphene.String(
            required=True,
            description="Represents an image file",
        )

    class Meta:
        description = (
            "Create a user avatar. This mutation must be sent "
            "as a `multipart` request. More detailed specs of the upload format can be "
            "found here: https://github.com/jaydenseric/graphql-multipart-request-spec"
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, _root, info, image):
        user = info.context.user
        # TODO IMAGE UPDATE/DELETE
        user.avatar = image
        user.save()
        return AccountAvatarUpdate(user=user)


class AccountAvatarDelete(BaseMutation):
    user = graphene.Field(User, description="An updated user instance.")

    class Meta:
        description = "Deletes a user avatar."
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, _root, info):
        user = info.context.user
        # TODO IMAGE UPDATE/DELETE
        user.avatar = None
        user.save()
        return AccountAvatarDelete(user=user)


class TwoFactorAuthEnable(BaseMutation):
    two_factor_auth_secret = graphene.String(
        description="Two factor authentication secret key"
    )

    class Arguments:
        enable = graphene.Boolean(required=True)
        method = TwoAuthVerificationMethodEnum(required=True)

    class Meta:
        description = "Enable two factor authentication"
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.TWO_FACTOR_AUTHENTICATION

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        enable = data.get("enable")
        method = data.get("method")
        user = get_current_user()
        if user.two_factor_auth_enabled == enable:
            raise ValidationError(
                {
                    "enable": "Two factor authentication is already enabled" if enable else "Two factor authentication is already disabled"})

        if enable and not method:
            raise ValidationError(
                {
                    "method": "Please select a method to enable two factor authentication"
                }
            )

        if enable and method==TwoAuthVerificationMethod.EMAIL and not user.email:
            raise ValidationError(
                {
                    "enable": "Please add email address to enable two factor authentication"
                }
            )
        if enable and method==TwoAuthVerificationMethod.MOBILE and not user.mobile:
            raise ValidationError(
                {
                    "enable": "Please add mobile number to enable two factor authentication"
                }
            )
        encryption_utils = EncryptionUtils()

        if enable and method==TwoAuthVerificationMethod.AUTHENTICATOR:
            user.two_factor_auth_secret = encryption_utils.encrypt(pyotp.random_base32())
            user.two_factor_auth_enabled = False
            user.two_factor_auth_verification_method = method
        elif enable:
            user.two_factor_auth_enabled = enable
            user.two_factor_auth_verification_method = method
        else:
            user.two_factor_auth_enabled = enable
            user.two_factor_auth_secret = None

        admin_two_fa_email_only_feature_enabled = cls.is_admin_two_feature_enabled()

        if (enable and method != TwoAuthVerificationMethod.EMAIL and
                admin_two_fa_email_only_feature_enabled and user.is_admin):
            raise ValidationError(
                {
                    "method": "Two factor authentication can only be enabled with email method for admin users"
                }
            )

        user.save(update_fields=["two_factor_auth_enabled", "two_factor_auth_secret", "two_factor_auth_verification_method"])

        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(user, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(user.sso_id, keycloak_user_data)

        return TwoFactorAuthEnable(two_factor_auth_secret=
                                   encryption_utils.decrypt(user.two_factor_auth_secret))

    @classmethod
    def is_admin_two_feature_enabled(cls):
      site_settings = BackendClient().get_site_settings()
      return site_settings.admin_two_fa_email_only


class VerifyAuthenticatorCodeForFirstTime(BaseMutation):
    success = graphene.Boolean(
        description="Returns True, if two factor authentication code is valid"
    )

    class Arguments:
        input = VerifyAuthenticatorCodeForFirstTimeInput(required=True)

    class Meta:
        description = "Verify two factor authentication code"
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.TWO_FACTOR_AUTHENTICATION

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_consumer():
            raise PermissionDenied()

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        code = data.get('input').get("code")
        user = get_current_user()
        encryption_utils = EncryptionUtils()
        if user.two_factor_auth_verification_method != TwoAuthVerificationMethod.AUTHENTICATOR:
            raise ValidationError(
                {
                    "method": "This allowed for authenticator method only"
                }
            )
        if user.two_factor_auth_enabled:
            raise ValidationError(
                {
                    "method": "Authenticator method is already verified"
                }
            )
        totp = pyotp.TOTP(encryption_utils.decrypt(user.two_factor_auth_secret))
        if not totp.verify(code):
            return VerifyTwoFactorAuth(success=False)

        user.two_factor_auth_enabled = True
        user.save(update_fields=["two_factor_auth_enabled"])

        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(user, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(user.sso_id, keycloak_user_data)

        return VerifyTwoFactorAuth(success=True)


class VerifyOTPForLogin(BaseMutation):
    success = graphene.Boolean(
        description="Returns True, if two factor authentication code is valid"
    )

    class Arguments:
        input = VerifyOTPForLoginInput(required=True)

    class Meta:
        description = "Verify two factor authentication code"
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        input_data = data.get("input")
        code = input_data.get("code")
        session_token = input_data.get("session_token")
        email = input_data.get("email")
        mobile = input_data.get("mobile")
        if (not email and not mobile) or (email and mobile):
            raise ValidationError(
                {
                    "email": "Email or mobile number is required"
                }
            )

        try:
            check_and_confirm_verification(email=email, phone_number=mobile,
                                           session_token=session_token,
                                           verification_code=code, is_token_used=True)
        except:
            return VerifyTwoFactorAuth(success=False)

        return VerifyTwoFactorAuth(success=True)


class VerifyTwoFactorAuth(BaseMutation):
    success = graphene.Boolean(
        description="Returns True, if two factor authentication code is valid"
    )

    class Arguments:
        input = VerifyTwoFactorAuthInput(required=True)

    class Meta:
        description = "Verify two factor authentication code"
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_admin():
            raise PermissionDenied()

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        input = data.get("input")
        code = input.get("code")
        session_token = input.get("session_token")
        user_id= input.get("user_id")

        user = models.User.objects.get(id=graphene.Node.from_global_id(user_id)[1])
        if not user.two_factor_auth_enabled:
            return VerifyTwoFactorAuth(success=True)
        if user.two_factor_auth_verification_method == TwoAuthVerificationMethod.EMAIL:
            try:
                check_and_confirm_verification(email=user.email,phone_number=None,session_token=session_token,
                                               verification_code=code,is_token_used=True)
            except:
                return VerifyTwoFactorAuth(success=False)
        elif user.two_factor_auth_verification_method == TwoAuthVerificationMethod.MOBILE:
            try:
                check_and_confirm_verification(email=None,phone_number=user.mobile,session_token=session_token,
                                               verification_code=code,is_token_used=True)
            except:
                return VerifyTwoFactorAuth(success=False)
        else:
            encryption_utils = EncryptionUtils()
            totp = pyotp.TOTP(encryption_utils.decrypt(user.two_factor_auth_secret))
            if not totp.verify(code):
                return VerifyTwoFactorAuth(success=False)

        return VerifyTwoFactorAuth(success=True)


class ConsentMarketing(BaseMutation):
    user = graphene.Field(User, description="An updated user instance.")

    class Arguments:
        consent = graphene.Boolean(
            required=True,
            description="Represents a boolean value to mark the user for marketing consent",
        )

    class Meta:
        description = "Consent marketing."
        error_type_class  = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        user = info.context.user
        consent = data.get("consent")
        if user.is_consumer:
            user.marketing_consent = consent
            user.save()
            return ConsentMarketing(user=user)
        else:
            raise ValidationError(
                {
                    "user": "This mutation is only allowed for consumers"
                }
            )
