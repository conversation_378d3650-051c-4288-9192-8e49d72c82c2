from collections import defaultdict
from typing import TYPE_CHECKING, Dict, List, Optional, Tuple

import graphene
from django.contrib.auth import models as auth_models
from django.core.exceptions import ValidationError
from django.db import transaction

from .keycloak_permission import KeyCloakPermission
from ..types import Group
from ...account.utils import (
    can_user_manage_group,
    get_not_manageable_permissions_after_group_deleting,
    get_not_manageable_permissions_after_removing_perms_from_group,
    get_not_manageable_permissions_after_removing_users_from_group,
    get_out_of_scope_users,
    get_out_of_scope_permissions_by_type,
)
from ...core.mutations import ModelDeleteMutation, ModelMutation
from ...core.types.common import PermissionGroupError
from ...core.utils import get_duplicates_ids
from .... import settings
from ....account.error_codes import PermissionGroupErrorCode
from ....auth.enums import AppTypes
from ....auth.exceptions import PermissionDenied
from ....auth.graphql.enums import PermissionEnum, AppTypeEnum
from ....auth.permissions import AccountPermissions
from ....core.permissions import get_permissions, NOT_EDITABLE_GROUPS
from ....keycloak.keycloak_client import KeycloakAPI
from ....keycloak_permissions import models as keycloak_model

if TYPE_CHECKING:
    from ....account.models import User


class PermissionGroupInput(graphene.InputObjectType):
    add_permissions = graphene.List(
        graphene.NonNull(PermissionEnum),
        description="List of permission code names to assign to this group.",
        required=False,
    )
    add_users = graphene.List(
        graphene.NonNull(graphene.ID),
        description="List of users to assign to this group.",
        required=False,
    )


class GroupConfigurationInput(graphene.InputObjectType):
    vendor = graphene.ID(description="Vendor id")
    group_type = AppTypeEnum(required=True)
    is_editable = graphene.Boolean(default=True, requried=False)
    is_global = graphene.Boolean(default=False, required=False)


class PermissionGroupCreateInput(PermissionGroupInput):
    group_configuration = GroupConfigurationInput(
        description="group configuration",
        required=True)
    name = graphene.String(description="Group name.", required=True)


class PermissionGroupCreate(ModelMutation):
    group = graphene.Field(Group, description="The newly created group.")

    class Arguments:
        input = PermissionGroupCreateInput(
            description="Input fields to create permission group.", required=True
        )

    class Meta:
        description = "Create new permission group."
        model = auth_models.Group
        permissions = (AccountPermissions.MANAGE_PERMISSION_GROUP,)
        error_type_class = PermissionGroupError
        error_type_field = "permission_group_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        for permission in cls._meta.permissions:
            if context.user.has_perm(permission):
                return True
        raise PermissionDenied()

    @classmethod
    @transaction.atomic
    def _save_m2m(cls, info, instance: auth_models.Group, cleaned_data):
        group_configuration_instance = cleaned_data.get("group_configuration")
        is_create = False if group_configuration_instance.group else True
        keycloak_group_id = None
        if not is_create:
            keycloak_group_id = group_configuration_instance.keycloak_group_id

        add_permissions = cleaned_data.get("add_permissions", [])
        if add_permissions:
            instance.permissions.add(*add_permissions)

        added_users = cleaned_data.get("add_users", [])
        if added_users:
            instance.user_set.add(*added_users)

        client_name = settings.KEYCLOAK_CONFIG['CLIENT_ID']
        client_id = KeycloakAPI().get_client_id(client_name)
        if not client_id:
            if is_create:
                instance.delete()
            return None

        added_roles = cls.prepare_keycloak_roles(add_permissions)

        try:
            keycloak_group_id = KeycloakAPI().create_update_group(name=instance.name,
                                                                  client_id=client_id,
                                                                  added_roles=added_roles,
                                                                  added_users=added_users,
                                                                  removed_roles=[],
                                                                  removed_users=[],
                                                                  is_create=is_create,
                                                                  group_id=keycloak_group_id)
        except ValidationError as e:
            if is_create:
                instance.delete()
            raise e

        group_configuration_instance.group = instance
        group_configuration_instance.keycloak_group_id = keycloak_group_id
        group_configuration_instance.save()

        return client_id, keycloak_group_id

    @classmethod
    def prepare_keycloak_roles(cls, add_permissions):
        roles = [
            {
                "clientRole": True,
                "composite": False,
                "id": permission.key_cloak_permission.keycloak_role_id,
                "name": permission.codename
            }
            for permission in add_permissions
        ]
        return roles

    @classmethod
    def clean_input(cls, info, instance, data, ):

        cleaned_input = super().clean_input(info, instance, data)
        requestor = info.context.user
        group_name = cleaned_input.get("name", instance.name).strip().lower()

        if group_name in ["admins", "users"]:
            raise ValidationError("Duplicate Group name")

        if group_name == "":
            raise ValidationError("Empty group name")

        cleaned_input["name"] = group_name

        cls.clean_group_configuration(info, requestor, cleaned_input, instance)

        errors = defaultdict(list)

        cls.clean_permissions(requestor, instance, errors, cleaned_input)
        cls.clean_users(requestor, errors, cleaned_input, instance)

        if errors:
            raise ValidationError(errors)

        return cleaned_input

    @classmethod
    def clean_group_configuration(cls, info, requestor, cleaned_input, group_instance):
        group_configuration = cleaned_input.pop("group_configuration", {})

        group_configuration_instance = group_instance.group_configuration if \
            hasattr(group_instance, "group_configuration") else \
            keycloak_model.GroupConfiguration()

        cleaned_group_configuration = super().clean_input(info,
                                                          group_configuration_instance,
                                                          group_configuration,
                                                          input_cls=GroupConfigurationInput)

        group_configuration_instance = cls.construct_instance(
            group_configuration_instance,
            cleaned_group_configuration)

        group_type = cleaned_group_configuration.get("group_type",
                                                     group_configuration_instance.group_type)
        is_global = cleaned_group_configuration.get("is_global",
                                                    group_configuration_instance.is_global)
        is_editable = cleaned_group_configuration.get("is_editable",
                                                      group_configuration_instance.is_editable)

        if requestor.is_vendor and group_type != AppTypes.VENDOR:
            raise ValidationError({
                "group_type": "Vendors can create only groups of type Vendor"
            })

        if not (requestor.is_admin or requestor.is_vendor):
            raise PermissionDenied()

        vendor = cleaned_group_configuration.get("vendor", None)
        if requestor.is_vendor:
            vendor = requestor.vendor
            is_global = False
            is_editable = True

        if group_type == AppTypes.ADMIN and vendor:
            raise ValidationError({
                "vendor": "Admin group type should not be combined with a vendor"
            })
        group_configuration_instance.created_by = requestor
        group_configuration_instance.is_global = is_global
        group_configuration_instance.is_editable = is_editable

        cleaned_input["group_configuration"] = group_configuration_instance

        return cleaned_input

    @classmethod
    def clean_permissions(
            cls,
            requestor: "User",
            group: auth_models.Group,
            errors: Dict[Optional[str], List[ValidationError]],
            cleaned_input: dict,
    ):
        field = "add_permissions"
        permission_items = cleaned_input.get(field)
        group_type = cleaned_input.get("group_configuration").group_type

        if permission_items:
            cleaned_input[field] = get_permissions(permission_items, group_type)
            permission_items = get_permissions(permission_items)

            cls.ensure_can_manage_permissions(
                errors, field, permission_items, group_type
            )

    @classmethod
    def ensure_can_manage_permissions(
            cls,
            errors: Dict[Optional[str], List[ValidationError]],
            field: str,
            permission_items: List[str],
            group_type
    ):
        """Check if requestor can manage permissions from input.
        """
        missing_permissions = get_out_of_scope_permissions_by_type(group_type,
                                                                   permission_items)
        missing_permissions = ["permissions." + per.codename for per in
                               missing_permissions]
        if missing_permissions:
            # add error
            error_msg = "You can't add/remove permissions out of your scop"
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_PERMISSION.value
            params = {"permissions": missing_permissions}
            cls.update_errors(errors, error_msg, field, code, params)

    @classmethod
    def clean_users(
            cls,
            requestor,
            errors: dict,
            cleaned_input: dict,
            group
    ):
        user_items = cleaned_input.get("add_users")
        if user_items:
            cls.ensure_users_can_be_added(errors, "add_users", cleaned_input)

    @classmethod
    def ensure_users_can_be_added(
            cls,
            errors: Dict[Optional[str], List[ValidationError]],
            field: str,
            cleaned_input: dict,
    ):

        users = cleaned_input[field]
        group_type = cleaned_input.get("group_configuration").group_type
        vendor = cleaned_input.get("group_configuration").vendor
        is_global = cleaned_input.get("group_configuration").is_global
        not_allowed_users = [user.pk for user in users if (
                user.app_type != group_type or (
                not is_global and user.vendor != vendor))]
        if not_allowed_users:
            # add error
            ids = [graphene.Node.to_global_id("User", pk) for pk in not_allowed_users]
            error_msg = "users are not from within same department"
            code = PermissionGroupErrorCode.ASSIGN_NON_STAFF_MEMBER.value
            params = {"users": ids}
            cls.update_errors(errors, error_msg, field, code, params)

    @classmethod
    def update_errors(
            cls,
            errors: Dict[Optional[str], List[ValidationError]],
            msg: str,
            field: Optional[str],
            code: str,
            params: dict,
    ):
        """Create ValidationError and add it to error list."""
        error = ValidationError(message=msg, code=code, params=params)
        errors[field].append(error)


class GroupConfigurationUpdateInput(graphene.InputObjectType):
    is_editable = graphene.Boolean(default=True, requried=False)


class PermissionGroupUpdateInput(PermissionGroupInput):
    group_configuration = GroupConfigurationUpdateInput(
        description="group configuration",
        required=True)
    remove_permissions = graphene.List(
        graphene.NonNull(PermissionEnum),
        description="List of permission code names to unassign from this group.",
        required=False,
    )

    remove_users = graphene.List(
        graphene.NonNull(graphene.ID),
        description="List of users to unassign from this group.",
        required=False,
    )


class PermissionGroupUpdate(PermissionGroupCreate):
    group = graphene.Field(Group, description="Group which was edited.")

    class Arguments:
        id = graphene.ID(description="ID of the group to update.", required=True)
        input = PermissionGroupUpdateInput(
            description="Input fields to create permission group.", required=True
        )

    class Meta:
        description = "Update permission group."
        model = auth_models.Group
        permissions = (AccountPermissions.MANAGE_PERMISSION_GROUP,)
        error_type_class = PermissionGroupError
        error_type_field = "permission_group_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        node_id = data.get("id")
        model_type = cls.get_type_for_model()
        instance = cls.get_node_or_error(info, node_id, only_type=model_type)
        if not instance.group_configuration.is_editable:
            raise ValidationError(
                {"id": ValidationError(message="You cant update this group")}
            )
        result = super().perform_mutation(_root, info, **data)
        return result

    @classmethod
    @transaction.atomic
    def _save_m2m(cls, info, instance, cleaned_data):
        client_id, keycloak_group_id = super()._save_m2m(info, instance, cleaned_data)

        remove_users = cleaned_data.get("remove_users", [])
        if remove_users:
            instance.user_set.remove(*remove_users)

        remove_permissions = cleaned_data.get("remove_permissions", [])
        if remove_permissions:
            instance.permissions.remove(*remove_permissions)

        remove_permissions = cls.prepare_keycloak_roles(remove_permissions)
        try:
            KeycloakAPI().create_update_group(name=instance.name,
                                              client_id=client_id,
                                              added_roles=[],
                                              added_users=[],
                                              removed_roles=remove_permissions,
                                              removed_users=remove_users,
                                              is_create=False,
                                              group_id=keycloak_group_id)
        except ValidationError as e:
            raise e

    @classmethod
    def clean_input(
            cls, info, instance, data,
    ):
        requestor = info.context.user
        cls.ensure_requestor_can_manage_group(requestor, instance, data)

        errors = defaultdict(list)
        permission_fields = ("add_permissions", "remove_permissions", "permissions")
        user_fields = ("add_users", "remove_users", "users")

        cls.check_for_duplicates(errors, data, permission_fields)
        cls.check_for_duplicates(errors, data, user_fields)

        if errors:
            raise ValidationError(errors)

        cleaned_input = super().clean_input(info, instance, data)

        return cleaned_input

    @classmethod
    def ensure_requestor_can_manage_group(
            cls, requestor: "User", group: auth_models.Group, data
    ):
        """Check if requestor can manage group.

        Requestor cannot manage group with wider scope of permissions.
        """
        if not (requestor.is_admin or requestor.is_vendor):
            raise PermissionDenied()

        if (requestor.is_vendor and
                (
                        group.group_configuration.vendor_id and requestor.vendor_id != group.group_configuration.vendor_id)):
            error_msg = "You can't manage groups which is not users"
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_PERMISSION.value
            raise ValidationError(error_msg, code)

        is_editable = data.get("group_configuration", {}).get("is_editable",
                                                              group.group_configuration.is_editable)
        if (requestor.is_vendor and
                (
                        not group.group_configuration.is_editable and group.group_configuration.is_editable != is_editable)):
            error_msg = "Vendors cant change group editable attribute"
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_PERMISSION.value
            raise ValidationError(error_msg, code)

        add_permissions = data.get("add_permissions", [])
        remove_permissions = data.get("remove_permissions", [])
        if (requestor.is_vendor and
                (
                        not group.group_configuration.is_editable or group.group_configuration.is_global) and
                (add_permissions or remove_permissions)
        ):
            error_msg = "Vendors cant add/remove roles to none editable or global group"
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_PERMISSION.value
            raise ValidationError(error_msg, code)

    @classmethod
    def clean_permissions(
            cls,
            requestor: "User",
            group: auth_models.Group,
            errors: Dict[Optional[str], List[ValidationError]],
            cleaned_input: dict,
    ):
        super().clean_permissions(requestor, group, errors, cleaned_input)
        field = "remove_permissions"
        permission_items = cleaned_input.get(field)
        if permission_items:
            cleaned_input[field] = get_permissions(permission_items,
                                                   group.group_configuration.group_type)
            permission_items = get_permissions(permission_items)
            cls.ensure_can_manage_permissions(
                errors, field, permission_items,
                group_type=group.group_configuration.group_type
            )
            # Basically the idea of ensure permissions can be removed is not to leave staff without permission
            # currently we have super admin whom always will be able to manage everyone and for vendors, they cant remove global groups
            # and leave themselves out of permissions
            # cls.ensure_permissions_can_be_removed(errors, group, permission_items)

    @classmethod
    def ensure_permissions_can_be_removed(
            cls, errors: dict, group: auth_models.Group, permissions: List["str"],
    ):
        missing_perms = get_not_manageable_permissions_after_removing_perms_from_group(
            group, permissions
        )
        if missing_perms:
            # add error
            permission_codes = [PermissionEnum.get(code) for code in permissions]
            msg = (
                "Permissions cannot be removed, "
                "some of permissions will not be manageable."
            )
            code = PermissionGroupErrorCode.LEFT_NOT_MANAGEABLE_PERMISSION.value
            params = {"permissions": permission_codes}
            cls.update_errors(errors, msg, "remove_permissions", code, params)

    @classmethod
    def clean_users(
            cls,
            requestor,
            errors: dict,
            cleaned_input: dict,
            group
    ):
        super().clean_users(requestor, errors, cleaned_input, group)
        remove_users = cleaned_input.get("remove_users")
        if remove_users:
            # cls.ensure_can_manage_users(
            #     requestor, errors, "remove_users", cleaned_input
            # )
            cls.clean_remove_users(requestor, errors, cleaned_input, group)

    @classmethod
    def ensure_can_manage_users(
            cls,
            requestor: "User",
            errors: Dict[Optional[str], List[ValidationError]],
            field: str,
            cleaned_input: dict,
    ):
        """Check if requestor can manage users from input.

        Requestor cannot manage users with wider scope of permissions.
        """
        if requestor.is_superuser:
            return
        users = cleaned_input[field]
        out_of_scope_users = get_out_of_scope_users(requestor, users)
        if out_of_scope_users:
            # add error
            ids = [
                graphene.Node.to_global_id("User", user_instance.pk)
                for user_instance in out_of_scope_users
            ]
            error_msg = "You can't manage these users."
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_USER.value
            params = {"users": ids}
            cls.update_errors(errors, error_msg, field, code, params)

    @classmethod
    def clean_remove_users(
            cls,
            requestor: "User",
            errors: dict,
            cleaned_input: dict,
            group: auth_models.Group,
    ):
        cls.check_if_removing_user_last_group(requestor, errors, cleaned_input)
        # cls.check_if_users_can_be_removed(requestor, errors, cleaned_input, group)

    @classmethod
    def check_if_removing_user_last_group(
            cls, requestor: "User", errors: dict, cleaned_input: dict
    ):
        """Ensure user doesn't remove user's last group."""
        remove_users = cleaned_input["remove_users"]
        if requestor in remove_users and requestor.groups.count() == 1:
            # add error
            error_msg = "You cannot remove yourself from your last group."
            code = PermissionGroupErrorCode.CANNOT_REMOVE_FROM_LAST_GROUP.value
            params = {"users": [graphene.Node.to_global_id("User", requestor.pk)]}
            cls.update_errors(errors, error_msg, "remove_users", code, params)

    @classmethod
    def check_if_users_can_be_removed(
            cls,
            requestor: "User",
            errors: dict,
            cleaned_input: dict,
            group: auth_models.Group,
    ):
        """Check if after removing users from group all permissions will be manageable.

        After removing users from group, for each permission, there should be
        at least one staff member who can manage it (has both “manage staff”
        and this permission).
        """
        if requestor.is_superuser:
            return

        remove_users = cleaned_input["remove_users"]
        add_users = cleaned_input.get("add_users")
        manage_staff_permission = AccountPermissions.MANAGE_STAFF.value

        # check if user with manage staff will be added to the group
        if add_users:
            if any([user.has_perm(manage_staff_permission) for user in add_users]):
                return True

        permissions = get_not_manageable_permissions_after_removing_users_from_group(
            group, remove_users
        )
        if permissions:
            # add error
            permission_codes = [PermissionEnum.get(code) for code in permissions]
            msg = "Users cannot be removed, some of permissions will not be manageable."
            code = PermissionGroupErrorCode.LEFT_NOT_MANAGEABLE_PERMISSION.value
            params = {"permissions": permission_codes}
            cls.update_errors(errors, msg, "remove_users", code, params)

    @classmethod
    def check_for_duplicates(
            cls, errors: dict, input_data: dict, fields: Tuple[str, str, str],
    ):
        """Check if any items are on both input field.

        Raise error if some of items are duplicated.
        """
        add_field, remove_field, error_class_field = fields
        duplicated_ids = get_duplicates_ids(
            input_data.get(add_field), input_data.get(remove_field)
        )
        if duplicated_ids:
            # add error
            error_msg = (
                "The same object cannot be in both list"
                "for adding and removing items."
            )
            code = PermissionGroupErrorCode.DUPLICATED_INPUT_ITEM.value
            params = {error_class_field: list(duplicated_ids)}
            cls.update_errors(errors, error_msg, None, code, params)


class PermissionGroupDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(description="ID of the group to delete.", required=True)

    class Meta:
        description = "Delete permission group."
        model = auth_models.Group
        permissions = (AccountPermissions.MANAGE_PERMISSION_GROUP,)
        error_type_class = PermissionGroupError
        error_type_field = "permission_group_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        for permission in cls._meta.permissions:
            if context.user.has_perm(permission):
                return True
        raise PermissionDenied()

    @classmethod
    def clean_instance(cls, info, instance: auth_models.Group):
        requestor = info.context.user
        if requestor.is_vendor and (
                instance.group_configuration.vendor_id != requestor.vendor_id or
                instance.group_configuration.is_global or
                not instance.group_configuration.is_editable):
            error_msg = "You can't manage group with permissions out of your scope."
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_PERMISSION.value
            raise ValidationError(error_msg, code)

        if instance.name in NOT_EDITABLE_GROUPS:
            error_msg = "You cant delete this group"
            code = PermissionGroupErrorCode.OUT_OF_SCOPE_PERMISSION.value
            raise ValidationError(error_msg, code)
        cls.check_if_group_can_be_removed(requestor, instance)

    @classmethod
    def check_if_group_can_be_removed(cls, requestor, group):
        # cls.ensure_deleting_not_left_not_manageable_permissions(group)
        cls.ensure_not_removing_requestor_last_group(group, requestor)

    @classmethod
    def ensure_deleting_not_left_not_manageable_permissions(cls, group):
        """Return true if management of all permissions is provided by other groups.

        After removing group, for each permission, there should be at least one staff
        member who can manage it (has both “manage staff” and this permission).
        """
        permissions = get_not_manageable_permissions_after_group_deleting(group)
        if permissions:
            permission_codes = [PermissionEnum.get(code) for code in permissions]
            msg = "Group cannot be removed, some of permissions will not be manageable."
            code = PermissionGroupErrorCode.LEFT_NOT_MANAGEABLE_PERMISSION.value
            params = {"permissions": permission_codes}
            raise ValidationError(
                {"id": ValidationError(message=msg, code=code, params=params)}
            )

    @classmethod
    def ensure_not_removing_requestor_last_group(cls, group, requestor):
        """Ensure user doesn't remove user's last group."""
        if requestor in group.user_set.all() and requestor.groups.count() == 1:
            msg = "You cannot delete your last group."
            code = PermissionGroupErrorCode.CANNOT_REMOVE_FROM_LAST_GROUP.value
            raise ValidationError({"id": ValidationError(message=msg, code=code)})

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root: auth_models.Group, info, **data):

        if not cls.check_permissions(info.context):
            raise PermissionDenied()

        node_id = data.get("id")
        model_type = cls.get_type_for_model()
        instance = cls.get_node_or_error(info, node_id, only_type=model_type)
        if not instance.group_configuration.is_editable:
            raise ValidationError(
                {"id": ValidationError(message="You cant delete this group")}
            )

        cls.check_authorization(_root, info, instance, **data)

        cls.perform_validations(_root, info, instance, data)

        if instance:
            cls.clean_instance(info, instance)

        keycloak_group_id = instance.group_configuration.keycloak_group_id
        db_id = instance.id
        instance.delete()

        # After the instance is deleted, set its ID to the original database's
        # ID so that the success response contains ID of the deleted object.
        instance.id = db_id

        try:
            KeycloakAPI().get_group_id_by_name(
                instance.name)  # make sure group exist on keycloak
        except ValidationError as e:
            return cls.success_response(instance)

        KeycloakAPI().delete_group(keycloak_group_id, node_id)

        return cls.success_response(instance)


class KeycloakPermissionConfigurationInput(graphene.InputObjectType):
    is_staff = graphene.Boolean(required=False, description="Is Allowed for Admin")

    is_vendor = graphene.Boolean(required=False, description="Is Allowed for Vendor")

    is_client = graphene.Boolean(required=False, description="Is Allowed for Customer")

    is_aggregator = graphene.Boolean(required=False,
                                     description="Is Allowed for Aggregator")

    keycloak_role_id = graphene.String(required=False,
                                       description="Key cloak role Id")


class KeycloakPermissionConfigurationUpdate(ModelMutation):
    key_cloak_permission = graphene.Field(KeyCloakPermission)

    class Arguments:
        id = graphene.ID(description="ID of the keycloak permission",
                         required=True)

        input = KeycloakPermissionConfigurationInput(
            description="Input fields to update permission configure keycloak permissions.",
            required=True
        )

    class Meta:
        description = "Update keycloak permission group"
        model = keycloak_model.KeyCloakPermission
        permissions = (AccountPermissions.MANAGE_PERMISSION_GROUP,)
        error_type_class = PermissionGroupError
        error_type_field = "permission_group_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data)
        for key, value in cleaned_data.items():
            if isinstance(value, bool):
                if not value:
                    raise ValidationError("False values not not supported yet")
        return cleaned_data

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        return result
