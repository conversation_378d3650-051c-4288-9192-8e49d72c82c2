import graphene

from usermanagment.graphql.utils.request_utils import get_current_user
from ...core.mutations import ModelMutation
from ...core.types.common import ConsumerViewPreferenceError
from ....account.models import ConsumerViewPreference
from django.core.exceptions import ValidationError
from ....account.error_codes import ConsumerViewPreferenceErrorCode


class ConsumerViewPreferenceInput(graphene.InputObjectType):
    view = graphene.String(description="view name")
    data = graphene.JSONString(description="consumer view preference data")


class ConsumerViewPreferenceSave(ModelMutation):
    class Arguments:
        input = ConsumerViewPreferenceInput(required=True)

    class Meta:
        description = "Save Consumer View Preference"
        model = ConsumerViewPreference
        error_type_class = ConsumerViewPreferenceError
        error_type_field = "consumer_view_preference_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        return {'user': get_current_user()}

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        instance = cls.get_instance(info, **data)
        if data.get('input').get('data') is None:
            if instance.pk:
                instance.data = None
                instance.delete()
                return cls.success_response(instance)
            else:
                raise ValidationError(
                    {
                        "view": ValidationError(
                            "Consumer View Preference not found.",
                            code=ConsumerViewPreferenceErrorCode.NOT_FOUND,
                        )
                    }
                )
        else:
            instance.user = get_current_user()
            instance.view = data['input']['view']
            instance.data = data['input']['data']
            instance.save()
            return cls.success_response(instance)

    @classmethod
    def get_instance(cls, info, **data):
        try:
            return ConsumerViewPreference.objects.get(user=get_current_user(),
                                                      view=data['input']['view'])
        except ConsumerViewPreference.DoesNotExist:
            return cls._meta.model()
