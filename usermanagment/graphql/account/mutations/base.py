import base64
import random
from datetime import timed<PERSON><PERSON>
from textwrap import wrap

import graphene
import jwt
import requests
from Cryptodome.Hash import SHA256
from Cryptodome.PublicKey import RSA
from Cryptodome.Signature import pkcs1_15

from django.conf import settings
from django.contrib.auth import password_validation
from django.contrib.sites.models import Site
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import transaction
from django.utils import timezone
from django.utils.crypto import get_random_string
from graphene import Node
from ..enums import PasswordResetMethod, PasswordResetMethodEnum, OTPOperationEnum, \
    BiometricLoginTypeEnum, SendBulkMessagesMethodEnum, PersonGenderEnum
from ..utils import verify_otp, verify_email_otp, UserDeleteMixin
from ...account.types import Address, User
from ...core.enums import LanguageCodeEnum
from ...core.mutations import (
    BaseMutation,
    ModelDeleteMutation,
    validation_error_to_error_type, ModelMutation,
)
from ...core.types.common import AccountError
from ...core.utils import from_global_id_strict_type
from ...core.utils.token_utils import prepare_user_attributes, prepare_user_branches
from ...utils.request_utils import get_current_user
from ....account import events as account_events, models, OTPOperation, \
    SendBulkMessagesMethod
from ....account.email import send_email
from ....account.error_codes import AccountErrorCode
from ....account.models import EmailPhoneVerification, BiometricLoginDevice, \
    BulkMessageRequest
from ....account.sms import send_sms
from ....account.validators import validate_possible_number, is_email_valid
from ....auth.enums import AppTypes
from ....auth.exceptions import PermissionDenied
from ....auth.graphql.enums import AppTypeEnum
from ....auth.permissions import AccountPermissions, HealthProgramPermissions
from ....keycloak.keycloak_client import KeycloakAPI
from ....core.utils.encryotion_utils import EncryptionUtils
from ....auth.features import Feature
from ....patient.models import PatientChronicDiseaseView

BILLING_ADDRESS_FIELD = "default_billing_address"
SHIPPING_ADDRESS_FIELD = "default_shipping_address"
INVALID_TOKEN = "Invalid or expired token."


def can_edit_address(user, address):
    return not address.user_addresses.exclude(pk=user.pk).exists()


def get_user_by_email_or_mobile(email, mobile, app_type, vendor):
    user = None
    try:
        if email:
            user = models.User.objects.get(email=email, app_type=app_type)

        if mobile:
            if app_type == AppTypes.VENDOR:
                if not vendor:
                    raise ValidationError({
                        "vendor": "this field is required with vendor users"
                    })

                vendor_id = from_global_id_strict_type(vendor, "Vendor")
                user = models.User.objects.get(mobile=mobile, app_type=app_type,
                                               vendor=vendor_id)
            else:
                user = models.User.objects.get(mobile=mobile, app_type=app_type)

    except ObjectDoesNotExist:
        raise ValidationError({"username": "user does not exist"})

    if not user.is_active and user.last_login:
        raise ValidationError(
            {
                "username": "your account is disabled. Please contact super admin.",
            }
        )

    return user


class SetPassword(BaseMutation):
    account_errors = graphene.List(
        graphene.NonNull(AccountError),
        description="List of errors that occurred executing the mutation.",
        required=False,
    )
    success = graphene.Boolean(default_value=False)

    class Arguments:
        username = graphene.String(required=True)
        password = graphene.String(required=True)
        token = graphene.String(
            description="A one-time token required to set the password.", required=True
        )
        app_type = graphene.Argument(AppTypeEnum,
                                     description="app from which the user is trying to reset password",
                                     required=True,
                                     )

        vendor = graphene.ID()

        password_reset_method = PasswordResetMethodEnum(required=True)

    class Meta:
        error_type_class = AccountError
        error_type_field = "account_errors"
        description = (
            "Sets the user's password from the token sent to mobile "
            "using the RequestPasswordReset mutation."
        )

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, root, info, **data):

        username = data.get("username")
        app_type = data.get("app_type")
        password_reset_method = data.get("password_reset_method")
        password = data.get("password")
        token = data.get("token")
        vendor = data.get("vendor")

        email = username if password_reset_method == PasswordResetMethod.EMAIL else None
        if email:
            check_email_verification(email, token)

        mobile = username if password_reset_method == PasswordResetMethod.MOBILE else None
        if mobile:
            check_contact_number_verification(mobile, token)

        user = get_user_by_email_or_mobile(email, mobile, app_type, vendor)

        cls._set_password_for_user(user, password, token)
        return cls(success=True, account_errors=[])

    @classmethod
    def _set_password_for_user(cls, user, password, token):
        try:
            password_validation.validate_password(password, user)
        except ValidationError as error:
            raise ValidationError({"password": error})

        KeycloakAPI().set_user_password(user.sso_id, password)

        KeycloakAPI().update_user(user.sso_id, {
            "enabled": True
        })

        user.is_active = True
        user.last_password_reset_date = timezone.now()
        user.save(update_fields=["is_active", "last_password_reset_date"])

        account_events.customer_password_reset_event(user=user)


class RequestPasswordReset(BaseMutation):
    session_token = graphene.String(
        description="sessionToken to resent with each request"
    )

    class Arguments:
        username = graphene.String(
            required=True,
            description="Destination of the OTP (email or mobile).",
        )

        app_type = graphene.Argument(AppTypeEnum,
                                     description="app from which the user is trying to reset password",
                                     required=True,
                                     )

        vendor = graphene.ID()

        password_reset_method = PasswordResetMethodEnum(required=True)
        recaptcha_token = graphene.String(description="reCaptcha token")

    class Meta:
        description = "Sends an sms with the account password modification."
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):

        if settings.RECAPTCHA_ENABLED:
            recaptcha_token = data.get("recaptcha_token")
            if not recaptcha_token:
                raise ValidationError(
                    {
                        "recaptcha_token": ValidationError(
                            "This field is required.", code=AccountErrorCode.REQUIRED
                        )
                    }
                )

            validate_recaptcha_token(recaptcha_token)

        password_reset_method = data.get("password_reset_method")
        app_type = data.get("app_type")
        username = data.get("username")
        vendor = data.get("vendor")

        if not username:
            raise ValidationError(
                {
                    "username": ValidationError(
                        "This field is required.", code=AccountErrorCode.REQUIRED
                    )
                }
            )

        email = username if password_reset_method == PasswordResetMethod.EMAIL.value else None
        mobile = username if password_reset_method == PasswordResetMethod.MOBILE.value else None

        user = get_user_by_email_or_mobile(email, mobile, app_type, vendor)

        site_settings = Site.objects.get_current().settings

        # get the language of the user
        user_language = info.context.LANGUAGE_CODE.upper()
        if user_language == LanguageCodeEnum.AR.name:
            if email:
                message = site_settings.password_reset_email_message_ar
            else:
                message = site_settings.password_reset_sms_message_ar
        else:
            if email:
                message = site_settings.password_reset_email_message
            else:
                message = site_settings.password_reset_sms_message

        message = message.format(patient_name=user.full_name,
                                 project_name=settings.PROJECT_NAME if "{project_name}" in message else "",
                                 project_name_ar=settings.PROJECT_NAME_AR if "{project_name_ar}" in message else "")

        try:
            session_token = generate_and_send_mobile_email_otp(email, mobile, message,
                                                               app_type, vendor)

            return RequestPasswordReset(session_token=session_token)
        except Exception as e:
            raise ValidationError(str(e))


class ConfirmAccountMobile(BaseMutation):
    success = graphene.Boolean(default_value=False)

    class Arguments:
        token = graphene.String(
            description="A one-time token required to confirm the account.",
            required=True,
        )
        session_token = graphene.String(
            description="A one-time token required to confirm the account.",
            required=True,
        )
        mobile = graphene.String(
            required=True,
            description="Mobile of the user that will be used for password recovery.",
        )
        username = graphene.String(
            description="username of the user performing account confirmation.",
            required=True,
        )

    class Meta:
        description = (
            "Confirm user account with token sent by mobile during registration."
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        try:
            user = models.User.objects.get_by_natural_key(data["username"])
        except ObjectDoesNotExist:
            raise ValidationError(
                {
                    "mobile": ValidationError(
                        "User with this username doesn't exist",
                        code=AccountErrorCode.NOT_FOUND,
                    )
                }
            )

        if not user.mobile or user.mobile != data['mobile']:
            raise ValidationError(
                {
                    "mobile": "mismatch mobile number.",
                }
            )

        if not user.is_active and user.last_login:
            raise ValidationError(
                {
                    "username": "your account is disabled. Please contact super admin.",
                }
            )

        verify_otp(user.mobile, data["session_token"], data["token"])

        user.is_active = True
        user.mobile_verified = True
        user.save(update_fields=["is_active", "mobile_verified"])

        KeycloakAPI().update_user(user.sso_id, {
            "enabled": True
        })

        return ConfirmAccountMobile(success=True)


class ConfirmAccountEmail(BaseMutation):
    success = graphene.Boolean(default_value=False)

    class Arguments:
        token = graphene.String(
            description="A one-time token required to confirm the account.",
            required=True,
        )
        session_token = graphene.String(
            description="A one-time token required to confirm the account.",
            required=True,
        )
        username = graphene.String(
            description="username of the user performing account confirmation.",
            required=True,
        )

    class Meta:
        description = (
            "Confirm user account with token sent by email during registration."
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        try:
            user = models.User.objects.get_by_natural_key(data["username"])
        except ObjectDoesNotExist:
            raise ValidationError(
                {
                    "email": ValidationError(
                        "User with this email doesn't exist",
                        code=AccountErrorCode.NOT_FOUND,
                    )
                }
            )

        if not user.is_active and user.last_login:
            raise ValidationError(
                {
                    "username": "your account is disabled. Please contact super admin.",
                }
            )

        if not user.email:
            raise ValidationError(
                {
                    "email": "not defined.",
                }
            )
        verify_email_otp(user.email, data["session_token"], data["token"])

        user.is_active = True
        user.email_verified = True
        user.save(update_fields=["is_active", "email_verified"])

        KeycloakAPI().update_user(user.sso_id, {
            "enabled": True
        })

        return ConfirmAccountEmail(success=True)


class PasswordChange(BaseMutation):
    user = graphene.Field(User, description="A user instance with a new password.")

    class Arguments:
        old_password = graphene.String(
            required=True, description="Current user password."
        )
        new_password = graphene.String(required=True, description="New user password.")

    class Meta:
        description = "Change the password of the logged in user."
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        user = info.context.user
        old_password = data["old_password"]
        new_password = data["new_password"]

        if not user.check_password(old_password):
            raise ValidationError(
                {
                    "old_password": ValidationError(
                        "Old password isn't valid.",
                        code=AccountErrorCode.INVALID_CREDENTIALS,
                    )
                }
            )
        try:
            password_validation.validate_password(new_password, user)
        except ValidationError as error:
            raise ValidationError({"new_password": error})

        KeycloakAPI().set_user_password(user.sso_id, new_password)

        user.set_password(new_password)
        user.save(update_fields=["password"])
        account_events.customer_password_changed_event(user=user)
        return PasswordChange(user=user)


class BaseAddressDelete(ModelDeleteMutation):
    """Base mutation for address delete used by staff and customers."""

    user = graphene.Field(
        User, description="A user instance for which the address was deleted."
    )

    class Arguments:
        id = graphene.ID(required=True, description="ID of the address to delete.")

    class Meta:
        abstract = True

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def clean_instance(cls, info, instance):
        # Method check_permissions cannot be used for permission check, because
        # it doesn't have the address instance.
        if not can_edit_address(info.context.user, instance):
            raise PermissionDenied()
        return super().clean_instance(info, instance)

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        if not cls.check_permissions(info.context):
            raise PermissionDenied()

        node_id = data.get("id")
        instance = cls.get_node_or_error(info, node_id, Address)
        if instance:
            cls.clean_instance(info, instance)

        db_id = instance.id

        # Return the first user that the address is assigned to. There is M2M
        # relation between users and addresses, but in most cases address is
        # related to only one user.
        user = instance.user_addresses.first()

        instance.delete()
        instance.id = db_id

        # Refresh the user instance to clear the default addresses. If the
        # deleted address was used as default, it would stay cached in the
        # user instance and the invalid ID returned in the response might cause
        # an error.
        user.refresh_from_db()

        response = cls.success_response(instance)

        response.user = user
        return response


class ConfirmPasswordRestOTP(BaseMutation):
    success = graphene.Boolean(default_value=False)

    class Arguments:
        verification_code = graphene.String(
            description="A one-time token required to confirm the account.",
            required=True,
        )
        session_token = graphene.String(
            description="A one-time token required to confirm the account.",
            required=True,
        )

        password_reset_method = PasswordResetMethodEnum(required=True)

        username = graphene.String(
            description="username of the user performing password reset.",
            required=True,
        )

        app_type = graphene.Argument(AppTypeEnum,
                                     description="app from which the user is trying to reset password",
                                     required=True,
                                     )

        vendor = graphene.ID()

    class Meta:
        description = (
            "Confirm user otp sent by mobile or email during password reset."
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        password_reset_method = data.get("password_reset_method")
        username = data.get("username")
        verification_code = data.get("verification_code")
        session_token = data.get("session_token")
        app_type = data.get("app_type")
        vendor = data.get("vendor")

        email = username if password_reset_method == PasswordResetMethod.EMAIL.value else None
        mobile = username if password_reset_method == PasswordResetMethod.MOBILE.value else None

        if app_type == AppTypes.VENDOR and not vendor:
            raise ValidationError({"vendor": "vendor is required with vendor users"})

        check_and_confirm_verification(email, mobile, session_token, verification_code,
                                       app_type=app_type, vendor=vendor)

        return ConfirmPasswordRestOTP(success=True)


class BaseUserDelete(UserDeleteMixin, ModelDeleteMutation):
    class Meta:
        abstract = True

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)

        if result.user and result.user.sso_id:
            sso_id = result.user.sso_id.replace(f'_deleted_{result.user.pk}', '')
            KeycloakAPI().delete_user(sso_id)

        return result


class RequestOTPForDeleteAccount(BaseMutation):
    session_token = graphene.String()

    class Arguments:
        email = graphene.String()
        phone_number = graphene.String()
        recaptcha_token = graphene.String(description="reCaptcha token")

    class Meta:
        description = (
            "Request user verification by sending verification code to email"
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        if settings.RECAPTCHA_ENABLED:
            recaptcha_token = data.get("recaptcha_token")
            if not recaptcha_token:
                raise ValidationError(
                    {
                        "recaptcha_token": ValidationError(
                            "This field is required.", code=AccountErrorCode.REQUIRED
                        )
                    }
                )

            validate_recaptcha_token(recaptcha_token)
        email = data.get("email")
        phone_number = data.get("phone_number")

        if email:
            user = models.User.objects.filter(email=email).first()
        if phone_number:
            user = models.User.objects.filter(mobile=phone_number).first()

        if not (user and user.is_consumer):
            token_data = None
            if email:
                token_data = {"email": email, "random": random.random()}
            if phone_number:
                token_data = {"mobile": phone_number, "random": random.random()}
            session_token = jwt.encode(token_data, settings.SECRET_KEY, algorithm="HS512")
            return RequestOTPForDeleteAccount(session_token=session_token)

        if email is not None:
            if not is_email_valid(email):
                raise ValidationError({"email": "email is not valid"})

        if phone_number is not None:
            if phone_number == '':
                raise ValidationError(
                    {"phone_number": "The phone number entered is not valid."})
            validate_possible_number(phone_number)

        if not (bool(email) ^ bool(phone_number)):
            raise ValidationError("email or phone number must be passed (not both)")

        site_settings = Site.objects.get_current().settings

        # get the language of the user
        user_language = info.context.LANGUAGE_CODE.upper()
        if user_language == LanguageCodeEnum.AR.name:
            if email:
                message = site_settings.delete_account_email_message_ar
            else:
                message = site_settings.delete_account_sms_message_ar
        else:
            if email:
                message = site_settings.delete_account_email_message
            else:
                message = site_settings.delete_account_sms_message

        session_token = generate_and_send_mobile_email_otp(email, phone_number, message)

        return RequestOTPForDeleteAccount(session_token=session_token)


class RequestOTP(BaseMutation):
    session_token = graphene.String()

    class Arguments:
        email = graphene.String()
        phone_number = graphene.String()
        recaptcha_token = graphene.String(description="reCaptcha token")
        operation = OTPOperationEnum()

    class Meta:
        description = (
            "Request user verification by sending verification code to email"
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        if settings.RECAPTCHA_ENABLED:
            recaptcha_token = data.get("recaptcha_token")
            if not recaptcha_token:
                raise ValidationError(
                    {
                        "recaptcha_token": ValidationError(
                            "This field is required.", code=AccountErrorCode.REQUIRED
                        )
                    }
                )

            validate_recaptcha_token(recaptcha_token)
        email = data.get("email")
        phone_number = data.get("phone_number")

        if email is not None:
            if not is_email_valid(email):
                raise ValidationError({"email": "email is not valid"})

        if phone_number is not None:
            if phone_number == '':
                raise ValidationError(
                    {"phone_number": "The phone number entered is not valid."})
            validate_possible_number(phone_number)

        if not (bool(email) ^ bool(phone_number)):
            raise ValidationError("email or phone number must be passed (not both)")

        site_settings = Site.objects.get_current().settings

        # get the language of the user
        user_language = info.context.LANGUAGE_CODE.upper()

        operation = data.get("operation")
        if not operation:
            operation = OTPOperation.PATIENT_REGISTRATION

        if operation == OTPOperation.TWO_FACTOR_AUTHENTICATION:
            if user_language == LanguageCodeEnum.AR.name:
                if email:
                    message = site_settings.simple_otp_email_message_ar
                else:
                    message = site_settings.simple_otp_sms_message_ar
            else:
                if email:
                    message = site_settings.simple_otp_email_message
                else:
                    message = site_settings.simple_otp_sms_message

        elif operation == OTPOperation.CHANGE_EMAIL_MOBILE:
            if user_language == LanguageCodeEnum.AR.name:
                if email:
                    message = site_settings.change_email_mobile_email_message_ar
                else:
                    message = site_settings.change_email_mobile_sms_message_ar
            else:
                if email:
                    message = site_settings.change_email_mobile_email_message
                else:
                    message = site_settings.change_email_mobile_sms_message

        else:
            if user_language == LanguageCodeEnum.AR.name:
                if email:
                    message = site_settings.registration_email_message_ar
                else:
                    message = site_settings.registration_sms_message_ar
            else:
                if email:
                    message = site_settings.registration_email_message
                else:
                    message = site_settings.registration_sms_message

        session_token = generate_and_send_mobile_email_otp(email, phone_number, message)

        return RequestOTP(session_token=session_token)


def generate_and_send_mobile_email_otp(email, phone_number, message, app_type=None,
                                       vendor=None):
    # get stored verification count in the last 24 hours
    stored_verification_count = EmailPhoneVerification.objects.filter(
        email=email,
        mobile=phone_number,
        created_at__gte=timezone.now() - timedelta(minutes=1)
    ).count()

    if stored_verification_count >= settings.EMAIL_PHONE_VERIFICATION.get(
            "LIMIT_PER_MINUTE", 3):
        raise ValidationError("Maximum verification limit reached")
    try:
        last_verification = EmailPhoneVerification.objects.filter(
            email=email,
            mobile=phone_number
        ).latest('created_at')
    except EmailPhoneVerification.DoesNotExist:
        last_verification = None

    if last_verification and last_verification.expires_at > timezone.now():
        last_verification.expires_at = timezone.now()
        last_verification.save()

    verification_code = generate_verification_code()

    token_data = None
    if email:
        token_data = {"email": email, "random": random.random()}
    if phone_number:
        token_data = {"mobile": phone_number, "random": random.random()}

    session_token = jwt.encode(token_data, settings.SECRET_KEY, algorithm="HS512")

    vendor_id = None
    if vendor:
        vendor_id = from_global_id_strict_type(vendor, "Vendor")

    EmailPhoneVerification.objects.create(
        email=email,
        mobile=phone_number,
        verification_code=verification_code,
        session_token=session_token,
        app_type=app_type,
        vendor_id=vendor_id
    )

    project_name = settings.PROJECT_NAME
    project_name_ar = settings.PROJECT_NAME_AR

    if email:
        send_email(to=email,
                   title=f"{project_name} Email Verification".
                   format(project_name=project_name),
                   body=message.format(security_code=verification_code,
                                       project_name=project_name if "{project_name}" in message else "",
                                       project_name_ar=project_name_ar if "{project_name_ar}" in message else "")
                   )

    if phone_number:
        send_sms(to=phone_number,
                 body=message.format(security_code=verification_code,
                                     project_name=project_name if "{project_name}" in message else "",
                                     project_name_ar=project_name_ar if "{project_name_ar}" in message else "")
                 )
    return session_token


def generate_verification_code():
    code_length = settings.EMAIL_PHONE_VERIFICATION.get(
        "TOKEN_LENGTH", 4
    )
    verification_code = get_random_string(code_length,
                                          allowed_chars="**********")
    return verification_code


class VerifyCredentials(RequestOTP):
    class Arguments:
        email = graphene.String()
        phone_number = graphene.String()
        recaptcha_token = graphene.String(description="reCaptcha token")
        operation = OTPOperationEnum()

    class Meta:
        description = (
            "Request user verification by sending verification code"
        )
        error_type_class = AccountError
        error_type_field = "account_errors"


class ConfirmVerification(BaseMutation):
    success = graphene.Boolean()

    class Arguments:
        verification_code = graphene.String(required=True)
        session_token = graphene.String(required=True)
        email = graphene.String()
        phone_number = graphene.String()

    class Meta:
        description = (
            "Confirm email verification"
        )
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        if not (bool(data.get("email")) ^ bool(data.get("phone_number"))):
            raise ValidationError("email or phone number must be passed (not both)")

        verification_code = data["verification_code"]
        session_token = data["session_token"]

        email = data.get("email", None)
        phone_number = data.get("phone_number", None)

        check_and_confirm_verification(email, phone_number, session_token,
                                       verification_code)

        return ConfirmVerification(success=True)


def check_and_confirm_verification(email, phone_number, session_token,
                                   verification_code, app_type=None, vendor=None,is_token_used=False):
    vendor_id = None
    if vendor:
        vendor_id = from_global_id_strict_type(vendor, "Vendor")

    stored_verification = EmailPhoneVerification.objects.filter(
        verification_code=verification_code, email=email, mobile=phone_number,
        is_verified=False, app_type=app_type, vendor=vendor_id
    ).order_by('-id').first()
    # check security_code exists
    if not stored_verification:
        raise ValidationError(
            {"verification_code": "Verification code is not valid."}
        )
    # check session code exists
    if stored_verification.session_token != session_token:
        raise ValidationError(
            {"session_token": "OTP token is not valid"}
        )
    # check security_code is not expired
    if timezone.now() > stored_verification.expires_at:
        raise ValidationError(
            {"verification_code": "Verification code is expired."}
        )
    # check security_code is not verified
    if stored_verification.is_verified:
        raise ValidationError(
            {"verification_code": "Verification code is already verified."}
        )
    # mark security_code as verified
    stored_verification.is_verified = True
    stored_verification.is_token_used = is_token_used
    stored_verification.save()


def check_contact_number_verification(contact_number, contact_number_token):
    stored_verification = EmailPhoneVerification.objects.filter(
        mobile=contact_number).order_by('-id').first()

    if (stored_verification
            and stored_verification.session_token == contact_number_token
            and stored_verification.is_verified
            and not stored_verification.is_token_used
            and stored_verification.expires_at >= timezone.now()):
        stored_verification.is_token_used = True
        stored_verification.save()
        return True

    if not stored_verification:
        raise ValidationError("mobile needs to be verified")
    if not stored_verification.is_verified:
        raise ValidationError("you need to confirm mobile verification")
    if stored_verification.is_token_used:
        raise ValidationError({"mobile_verification_token": "The token that you have "
                                                            "provided is used."})
    if stored_verification.session_token != contact_number_token:
        raise ValidationError({"mobile_verification_token": "The token that you have "
                                                            "provided is not valid."})
    if stored_verification.expires_at < timezone.now():
        raise ValidationError({"mobile_verification_token": "The token that you have "
                                                            "provided is expired."})


def check_email_verification(email, email_token):
    stored_verification = EmailPhoneVerification.objects.filter(
        email=email).order_by('-id').first()

    if stored_verification and stored_verification.session_token == email_token \
            and stored_verification.is_verified:
        stored_verification.is_token_used = True
        stored_verification.save()
        return True

    if not stored_verification:
        raise ValidationError("email needs to be verified")
    if not stored_verification.is_verified:
        raise ValidationError("you need to confirm email verification")
    if stored_verification.is_token_used:
        raise ValidationError({"email_verification_token": "The token that you have "
                                                           "provided is used."})
    if stored_verification.session_token != email_token:
        raise ValidationError({"email_verification_token": "The token that you have "
                                                           "provided is not valid."})
    if stored_verification.expires_at < timezone.now():
        raise ValidationError({"email_verification_token": "The token that you have "
                                                           "provided is expired."})


def validate_recaptcha_token(recaptcha_token):
    try:
        response = requests.post(
            url=settings.RECAPTCHA_URL,
            data={
                "secret": settings.RECAPTCHA_PRIVATE_KEY,
                "response": recaptcha_token,
            },
        )
    except Exception:
        raise ValidationError("recaptcha is not valid.")

    if response.status_code != 200 or not response.json().get("success"):
        raise ValidationError("recaptcha is not valid.")


class SendBulkMessagesInput(graphene.InputObjectType):
    title = graphene.String(required=False,
                            description="title of the email message")
    title_ar=graphene.String(required=False,
                             description="title of the email message in arabic")
    message = graphene.String(required=True, description="message to send")
    message_ar = graphene.String(required=True, description="message to send in arabic")
    users = graphene.List(graphene.ID,
                          required=True, description="users to send message")
    method = SendBulkMessagesMethodEnum(required=True,
                                        description="method to send message")

class UpdateUserEmailMobileInput(graphene.InputObjectType):
    email = graphene.String()
    mobile_token = graphene.String()

    mobile = graphene.String()
    email_token = graphene.String()

class BulkNotificationSearchCriteriaInput(graphene.InputObjectType):
    gender = PersonGenderEnum()
    from_age = graphene.Int()
    to_age = graphene.Int()
    chronic_disease_codes=graphene.List(graphene.String)


class SendBulkMessagesBySearchCriteriaInput(graphene.InputObjectType):
    title = graphene.String(required=False,
                            description="title of the email message")
    title_ar=graphene.String(required=False,
                             description="title of the email message in arabic")
    message = graphene.String(required=True, description="message to send")
    message_ar = graphene.String(required=True, description="message to send in arabic")
    method = SendBulkMessagesMethodEnum(required=True,
                                        description="method to send message")
    search_criteria = BulkNotificationSearchCriteriaInput(
        required=True, description="search criteria to send message")


# mutation to attach verified mobile or email to user account
class UpdateUserEmailMobile(ModelMutation):
    class Arguments:

        input = UpdateUserEmailMobileInput(
            required=True,
            description="Fields required to attach mobile or email to the current user"
        )

    class Meta:
        description = "Attach verified mobile or email to user account"
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()

        return {}

    @classmethod
    def get_instance(cls, info, **data):
        return info.context.user

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):

        email = data.get("email")
        mobile = data.get("mobile")
        email_token = data.get("email_token")
        mobile_token = data.get("mobile_token")

        if not (email or mobile):
            raise ValidationError("email or phone number must be passed")

        if email:
            if not email_token:
                raise ValidationError({"email_token": "email_token must be passed"})
            check_email_verification(email, email_token)

        if mobile:
            if not mobile_token:
                raise ValidationError({"mobile_token": "mobile_token must be passed"})
            check_contact_number_verification(mobile, mobile_token)

        return super().clean_input(info, instance, data, input_cls)

    @classmethod
    def clean_instance(cls, info, instance):
        try:
            super().clean_instance(info, instance)
        except ValidationError as e:
            if (
            "User with this App type, Mobile and Vendor id null to zero already exists.") in str(
                    e):

                raise ValidationError({
                    "mobile": "Mobile number already exists for another user."
                })

            else:
                raise e

    @classmethod
    @transaction.atomic
    def save(cls, info, instance: models.User, cleaned_input):
        keycloak_user_data = {}

        mobile = cleaned_input.get('mobile')
        if mobile and instance.is_consumer:
            patient = instance.patient
            patient.mobile = mobile
            patient.save()

        email = cleaned_input.get('email')
        if email:
            if instance.is_consumer:
                patient = instance.patient
                patient.email = email
                patient.save()

            keycloak_user_data['email'] = email

        keycloak_user_data["attributes"] = {}
        prepare_user_attributes(instance, keycloak_user_data["attributes"])
        prepare_user_branches([], instance, keycloak_user_data["attributes"])

        if keycloak_user_data:
            KeycloakAPI().update_user(instance.sso_id, keycloak_user_data)

        super().save(info, instance, cleaned_input)


class LanguageCreateInput(graphene.InputObjectType):
    code = graphene.String(required=True, description="language code")
    display = graphene.String(required=True, description="language display name")
    display_ar = graphene.String(required=False,
                                 description="language display name in arabic")


class LanguageUpdateInput(graphene.InputObjectType):
    code = graphene.String(required=False, description="language code")
    display = graphene.String(required=False, description="language display name")
    display_ar = graphene.String(required=False,
                                 description="language display name in arabic")


# mutation to add new language
class LanguageCreate(ModelMutation):
    class Arguments:
        input = LanguageCreateInput(
            required=True,
            description="Fields required to add new language"
        )

    class Meta:
        description = "Add new language"
        model = models.Language
        permissions = (AccountPermissions.MANAGE_LANGUAGES,)
        error_type_class = AccountError
        error_type_field = "account_errors"


# mutation to update language
class LanguageUpdate(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of the language to update.")
        input = LanguageUpdateInput(
            required=True,
            description="Fields required to update language"
        )

    class Meta:
        description = "Update language"
        model = models.Language
        permissions = (AccountPermissions.MANAGE_LANGUAGES,)
        error_type_class = AccountError
        error_type_field = "account_errors"


class EnableBiometricLoginInput(graphene.InputObjectType):
    public_key = graphene.String(required=True, description="public key")
    device_id = graphene.String(required=True, description="device id")
    device_name = graphene.String(required=True, description="device name")
    type = BiometricLoginTypeEnum(required=True, description="biometric type")


class EnableBiometricLogin(ModelMutation):
    class Arguments:
        input = EnableBiometricLoginInput(
            required=True,
            description="Fields required to enable biometric login"
        )

    class Meta:
        description = "Enable biometric login"
        model = BiometricLoginDevice
        error_type_class = AccountError
        feature = Feature.BIOMETRIC_LOGIN
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:

        current_user = get_current_user()
        if not current_user.is_consumer:
            raise PermissionDenied()

        if current_user.parent_user is not None:
            raise ValidationError("Dependent users are not allowed to enable biometric login")

        device_id = data.get("input").get("device_id")

        if BiometricLoginDevice.objects.filter(device_id=device_id).exclude(
                user_id=current_user.pk).exists():
            raise ValidationError("device already exists")

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):

        cleaned_input = super().clean_input(info, instance, data, input_cls)

        encryption_utils = EncryptionUtils()

        public_key = cleaned_input.get("public_key")
        wrapped_key = "\r\n".join(wrap(public_key, 64))
        pem_key = f"-----BEGIN PUBLIC KEY-----\r\n{wrapped_key}\r\n-----END PUBLIC KEY-----"
        encrypted_public_key = encryption_utils.encrypt(pem_key)

        cleaned_input["public_key"] = encrypted_public_key

        cleaned_input["user"] = get_current_user()

        return cleaned_input


class BiometricLoginInput(graphene.InputObjectType):
    signature = graphene.String(required=True, description="signature")
    device_id = graphene.String(required=True, description="device id")
    payload = graphene.String(required=True, description="payload")


class BiometricLogin(BaseMutation):
    user_id = graphene.ID()
    success = graphene.Boolean()

    class Arguments:
        input = BiometricLoginInput(
            required=True,
            description="Fields required to biometric login"
        )

    class Meta:
        description = "Biometric login"
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.BIOMETRIC_LOGIN

    @classmethod
    def perform_mutation(cls, _root, info, **data):

        biometric_signature = data.get("input").get("signature")
        device_id = data.get("input").get("device_id")
        biometric_payload = data.get("input").get("payload")

        biometric_device = BiometricLoginDevice.objects.filter(
            device_id=device_id).first()

        try:

            encryption_utils = EncryptionUtils()

            decrypted_key = encryption_utils.decrypt(
                biometric_device.public_key)

            public_key = RSA.import_key(decrypted_key.encode('utf-8'))

            hashed_payload = SHA256.new(biometric_payload.encode('utf-8'))

            signature_bytes = base64.b64decode(biometric_signature)

            pkcs1_15.new(public_key).verify(hashed_payload, signature_bytes)

            print("Signature is valid.")

        except:
            return BiometricLogin(success=False, user_id=None)

        return BiometricLogin(success=True, user_id=Node.to_global_id("User", biometric_device.user_id))


class BiometricLoginDeviceDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of the biometric login to delete.")

    class Meta:
        description = "Delete biometric login"
        model = BiometricLoginDevice
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.BIOMETRIC_LOGIN

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.user_id == get_current_user().pk:
            raise PermissionDenied()

        return {}

class SendBulkMessagesBySearchCriteria(BaseMutation):
    success = graphene.Boolean(default_value=False)

    class Arguments:
        input = SendBulkMessagesBySearchCriteriaInput(
            required=True,
            description="Fields required to send bulk messages by search criteria"
        )

    class Meta:
        description = "Send bulk messages by search criteria"
        error_type_class = AccountError
        error_type_field = "account_errors"
        permissions = (AccountPermissions.SEND_BULK_MESSAGES,HealthProgramPermissions.MANAGE_CASE_MANAGEMENT)

    @classmethod
    def check_permissions(cls, context, permissions=None):
        for permission in cls._meta.permissions:
            if context.user.has_perm(permission):
                return True
        raise PermissionDenied()


    @classmethod
    def perform_mutation(cls, _root, info, **data):
        input = data.get('input')
        message = input.message
        title = input.title
        title_ar = input.title_ar
        message_ar = input.message_ar
        search_criteria = input.search_criteria
        method = input.method
        queryset = PatientChronicDiseaseView.objects.all().distinct()

        if search_criteria.gender:
            queryset = queryset.filter(gender=search_criteria.gender)

        if search_criteria.from_age:

            queryset = queryset.filter(date_of_birth__lte=timezone.now() - timedelta(days=search_criteria.from_age * 365))

        if search_criteria.to_age:
            queryset = queryset.filter(date_of_birth__gte=timezone.now() - timedelta(days=search_criteria.to_age * 365))

        if search_criteria.chronic_disease_codes:
            queryset = queryset.filter(chronic_disease_code__in=search_criteria.chronic_disease_codes)

        user_ids = list(queryset.values_list('user_id', flat=True))
        BulkMessageRequest.objects.create(
            requester=info.context.user,
            user_count=len(user_ids),
            input_data=input
        )

        if not user_ids:
            raise ValidationError({"user_ids": "No users found matching the search criteria."})

        global_user_ids = [graphene.Node.to_global_id("User", user_id) for user_id in user_ids]

        data = {
            'input': {
                'title': title,
                'title_ar': title_ar,
                'message': message,
                'message_ar': message_ar,
                'users': global_user_ids,
                'method': method
            }
        }
        send_bulk_messages_mutation = (SendBulkMessages.
                                       perform_mutation(None, info, **data))

        return cls(success=send_bulk_messages_mutation.success)


class SendBulkMessages(BaseMutation):
    success = graphene.Boolean(default_value=False)

    class Arguments:
        input = SendBulkMessagesInput(
            required=True,
            description="Fields required to send bulk messages"
        )

    class Meta:
        description = "Send bulk messages"
        error_type_class = AccountError
        error_type_field = "account_errors"
        permissions = (AccountPermissions.SEND_BULK_MESSAGES,
                       HealthProgramPermissions.MANAGE_CASE_MANAGEMENT)

    @classmethod
    def check_permissions(cls, context, permissions=None):
        for permission in cls._meta.permissions:
            if context.user.has_perm(permission):
                return True
        raise PermissionDenied()

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        input = data.get("input")
        message = input.get('message')
        message_ar = input.get('message_ar')
        title = input.get('title')
        title_ar = input.get('title_ar')
        users = input.get('users')
        method = input.get('method')

        user_pks = [from_global_id_strict_type(user, "User") for user in users]

        db_users = models.User.objects.filter(pk__in=user_pks)

        if not db_users.count() == len(users):
            raise ValidationError({"users": "some users are not found"})

        if method == SendBulkMessagesMethod.EMAIL:
            if not title:
                raise ValidationError({"title": "title is required for email method"})
            if not title_ar:
                raise ValidationError({"title_ar": "title_ar is required for email method"})

            for user in db_users:
                if user.email:
                    if user.preferred_language and user.preferred_language.code == 'ar':
                        send_email(to=user.email, title=title_ar, body=message_ar)
                    else:
                        send_email(to=user.email, title=title, body=message)

        if method == SendBulkMessagesMethod.MOBILE:
            for user in db_users:
                if user.mobile:
                    if user.preferred_language and user.preferred_language.code == 'ar':
                        send_sms(to=user.mobile, body=message_ar)
                    else:
                        send_sms(to=user.mobile, body=message)
        return SendBulkMessages(success=True)
