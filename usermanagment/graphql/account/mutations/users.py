import logging
from collections import defaultdict

import graphene
from django.contrib.sites.models import Site
from django.core.exceptions import ValidationError
from django.db import transaction

from .account import AccountAddressCreate
from .base import BaseUserDelete
from ..enums import PersonGenderEnum
from ..types import AddressInput
from ...account.utils import \
    get_not_manageable_permissions_when_deactivate_or_remove_users, \
    StaffDeleteMixin, fetch_language_instances_by_codes
from ...core.health_license_date import HealthLicenseDatesMixin
from ...core.mutations import ModelMutation, BaseMutation
from ...core.types.common import AccountError
from ...core.utils import get_duplicates_ids, token_utils
from ...core.utils.token_utils import prepare_user_attributes, prepare_user_branches
from ...doctor.mutations.doctor import <PERSON><PERSON><PERSON>, DoctorInput, DoctorUpdate
from ...utils import get_database_id
from ...utils.request_utils import get_current_user
from .... import settings
from ....account import models
from ....account.error_codes import AccountErrorCode
from ....account.utils import get_super_vendor_group, get_pharmacist_vendor_group, \
    get_doctor_group, get_nurse_group, get_payer_user_group, get_payer_admin_group, \
    get_receptionist_group, get_dental_hygienist, \
    get_diabetes_educator, get_fitness_coach, get_nutritionist, get_optometrist, \
    get_podiatric_medical_assistant, get_psychologist, get_social_worker, get_rcm_group
from ....auth.enums import AppTypes, AppRoleTypes, VendorUserTypes
from ....auth.exceptions import PermissionDenied
from ....auth.features import Feature
from ....auth.graphql.enums import AppTypeEnum, AppRoleTypeEnum, VendorUserTypeEnum

from ....auth.permissions import AccountPermissions
from ....authorization import authorization
from ....authorization.authorization import AuthorizationDto
from ....core.utils import datetime_utils
from ....dental_hygienist.models import DentalHygienist
from ....fitness_coach.models import FitnessCoach
from ....graphql_client.backend_client import BackendClient
from ....nutritionist.models import Nutritionist
from ....optometrist.models import Optometrist
from ....podiatric_medical_assistant.models import PodiatricMedicalAssistant
from ....psychologist.models import Psychologist
from ....rcm.models import RCM
from ....social_worker.models import SocialWorker
from ....diabetes_educator.models import DiabetesEducator

from ....keycloak.keycloak_client import KeycloakAPI
from ....manager.models import Manager
from ....nurse.models import Nurse
from ....pharmacist.models import Pharmacist
from ....receptionist.models import Receptionist
import threading

class UserInput(graphene.InputObjectType):
    first_name = graphene.String(description="Given name.")
    second_name = graphene.String(description="Second name.")
    third_name = graphene.String(description="Third name.")
    last_name = graphene.String(description="Family name.")
    first_name_ar = graphene.String(description="Given name.")
    second_name_ar = graphene.String(description="Second name.")
    third_name_ar = graphene.String(description="Third name.")
    last_name_ar = graphene.String(description="Family name.")
    gender = PersonGenderEnum()
    date_of_birth = graphene.Date()
    is_active = graphene.Boolean(required=False, description="User account is active.")
    note = graphene.String(description="A note about the user.")
    mobile = graphene.String(description="mobile of the user.",
                             required=False)
    national_id = graphene.String(description="The national id.")
    photo = graphene.String(description="photo of the user")
    preferred_language = graphene.String(
        description="preferred language code of the user",
        required=False)

    relation_type = graphene.String(required=False)

    departments = graphene.List(graphene.ID,
                                description="vendor departments this user belongs to",
                                required=False)


class UserCreateInput(UserInput):
    password = graphene.String(required=True)
    branches = graphene.List(graphene.ID,
                             description="vendor branches this user has access to",
                             required=False)

    default_branch = graphene.ID(
        description="default branch for the user",
        required=False
    )

    email = graphene.String(description="email address of the user.", required=True)

    address = AddressInput(description="address of the Doctor")

class HealthLicenseInput(graphene.InputObjectType):
    health_license_number = graphene.String(required=False)
    health_license_start_date = graphene.Date(required=False)
    health_license_end_date = graphene.Date(required=False)

class NurseInput(HealthLicenseInput):
    pass

class PharmacistInput(HealthLicenseInput):
    pass

class DentalHygienistInput(HealthLicenseInput):
    pass


class DiabetesEducatorInput(HealthLicenseInput):
    pass

class FitnessCoachInput(HealthLicenseInput):
    pass

class NutritionistInput(HealthLicenseInput):
    pass

class OptometristInput(HealthLicenseInput):
    pass

class PodiatricMedicalAssistantInput(HealthLicenseInput):
    pass

class PsychologistInput(HealthLicenseInput):
    pass

class SocialWorkerInput(HealthLicenseInput):
    pass

class AdminUserCreateInput(UserCreateInput):
    app_type = AppTypeEnum(required=True)
    app_role = AppRoleTypeEnum(required=True)
    vendor_user_type = VendorUserTypeEnum(required=False)
    vendor = graphene.ID(required=False)
    payer = graphene.ID(required=False)

    doctor_info = DoctorInput(required=False)
    nurse_info = NurseInput(required=False)
    pharmacist_info = PharmacistInput(required=False)
    dental_hygienist_info = DentalHygienistInput(required=False)
    diabetes_educator_info = DiabetesEducatorInput(required=False)
    fitness_coach_info = FitnessCoachInput(required=False)
    nutritionist_info = NutritionistInput(required=False)
    optometrist_info = OptometristInput(required=False)
    podiatric_medical_assistant_info = PodiatricMedicalAssistantInput(required=False)
    psychologist_info = PsychologistInput(required=False)
    social_worker_info = SocialWorkerInput(required=False)

    add_groups = graphene.List(
        graphene.NonNull(graphene.ID),
        description="List of permission group IDs to which user should be assigned.",
        required=False,
    )

    parent_user = graphene.ID(required=False)
    marketing_consent = graphene.Boolean(required=False)


class UserProfileUpdateInput(graphene.InputObjectType):
    first_name = graphene.String(description="first name.")
    last_name = graphene.String(description="last name.")
    preferred_language = graphene.String(
        description="preferred language code of the user")


class BaseAccountMutationMixin(ModelMutation, HealthLicenseDatesMixin):
    class Meta:
        abstract = True

    @classmethod
    def ensure_requestor_can_manage_groups(
            cls, requestor: models.User, cleaned_input: dict,
            field: str, errors: dict, user_app_type, user_vendor_id, user_payer_id
    ):
        """Check if requestor can manage group.

        Requestor cannot manage group with wider scope of permissions.
        """
        allowed_types = [AppTypes.VENDOR, AppTypes.PAYER]
        out_of_scope_groups = []
        invalid_user_group_type = []

        if requestor.is_admin:
            allowed_types = [AppTypes.ADMIN, AppTypes.VENDOR, AppTypes.PAYER]

        groups = cleaned_input[field]
        for group in groups:
            if group.group_configuration.group_type not in allowed_types:
                out_of_scope_groups.append(group)
            elif requestor.is_vendor and \
                    (not group.group_configuration.is_global and
                     group.group_configuration.vendor_id != requestor.vendor_id):
                out_of_scope_groups.append(group)

            if group.group_configuration.group_type != user_app_type or \
                    (user_app_type == AppTypes.VENDOR and
                     not group.group_configuration.is_global and
                     group.group_configuration.vendor_id != user_vendor_id) or \
                    (user_app_type == AppTypes.PAYER and
                     not group.group_configuration.is_global and
                     group.group_configuration.payer_id != user_payer_id):
                invalid_user_group_type.append(group)

        invalid_user_group_type = set(invalid_user_group_type)
        out_of_scope_groups = set(out_of_scope_groups)
        if out_of_scope_groups:
            # add error
            ids = [
                graphene.Node.to_global_id("Group", group.pk)
                for group in out_of_scope_groups
            ]
            error_msg = "You can't manage these groups."
            code = AccountErrorCode.OUT_OF_SCOPE_GROUP.value
            params = {"groups": ids}
            error = ValidationError(message=error_msg, code=code, params=params)
            errors[field].append(error)

        elif invalid_user_group_type:
            ids = [
                graphene.Node.to_global_id("Group", group.pk)
                for group in invalid_user_group_type
            ]
            error_msg = "User cant be added to these groups"
            code = AccountErrorCode.OUT_OF_SCOPE_GROUP.value
            params = {"groups": ids}
            error = ValidationError(message=error_msg, code=code, params=params)
            errors[field].append(error)

    @classmethod
    def clean_groups(cls, requestor: models.User, cleaned_input: dict, errors: dict,
                     user_app_type, user_vendor_id, user_payer_id):

        if cleaned_input.get("add_groups"):
            cls.ensure_requestor_can_manage_groups(
                requestor, cleaned_input, "add_groups", errors,
                user_app_type, user_vendor_id, user_payer_id
            )
        if cleaned_input.get("remove_groups"):
            cls.ensure_requestor_can_manage_groups(
                requestor, cleaned_input, "remove_groups", errors,
                user_app_type, user_vendor_id, user_payer_id
            )

    @classmethod
    def clean_health_license_number(cls, app_type: AppTypes, cleaned_input: dict):
        health_license_number = cleaned_input.get('health_license_number')
        if app_type not in [AppTypes.VENDOR, AppTypes.PAYER] and health_license_number:
            cleaned_input['health_license_number'] = None
            cleaned_input['health_license_start_date'] = None
            cleaned_input['health_license_end_date'] = None

    @classmethod
    def clean_is_active(
            cls,
            cleaned_input: dict,
            instance: models.User,
            requestor: models.User,
            errors: dict,
    ):
        is_active = cleaned_input.get("is_active")
        if is_active is None:
            return
        if not is_active:
            cls.check_if_deactivating_superuser_or_own_account(
                instance, requestor, errors
            )
            cls.check_if_deactivating_left_not_manageable_permissions(
                instance, requestor, errors
            )

    @classmethod
    def check_if_deactivating_superuser_or_own_account(
            cls, instance: models.User, requestor: models.User, errors: dict
    ):
        """User cannot deactivate superuser or own account.

        Args:
            instance: user instance which is going to deactivated
            requestor: user who performs the mutation
            errors: a dictionary to accumulate mutation errors

        """
        if requestor == instance:
            error = ValidationError(
                "Cannot deactivate your own account.",
                code=AccountErrorCode.DEACTIVATE_OWN_ACCOUNT.value,
            )
            errors["is_active"].append(error)
        elif instance.is_superuser:
            error = ValidationError(
                "Cannot deactivate superuser's account.",
                code=AccountErrorCode.DEACTIVATE_SUPERUSER_ACCOUNT.value,
            )
            errors["is_active"].append(error)

    @classmethod
    def check_if_deactivating_left_not_manageable_permissions(
            cls, user: models.User, requestor: models.User, errors: dict
    ):
        """Check if after deactivating user all permissions will be manageable.

        After deactivating user, for each permission, there should be at least one
        active staff member who can manage it (has both “manage staff” and
        this permission).
        """
        if requestor.is_superuser:
            return
        permissions = get_not_manageable_permissions_when_deactivate_or_remove_users(
            [user]
        )
        if permissions:
            # add error
            msg = (
                "Users cannot be deactivated, some of permissions "
                "will not be manageable."
            )
            code = AccountErrorCode.LEFT_NOT_MANAGEABLE_PERMISSION.value
            params = {"permissions": permissions}
            error = ValidationError(msg, code=code, params=params)
            errors["is_active"].append(error)

    @classmethod
    def check_for_duplicates(cls, input_data):
        duplicated_ids = get_duplicates_ids(
            input_data.get("add_groups"), input_data.get("remove_groups")
        )
        if duplicated_ids:
            # add error
            msg = (
                "The same object cannot be in both list"
                "for adding and removing items."
            )
            code = AccountErrorCode.DUPLICATED_INPUT_ITEM.value
            params = {"groups": duplicated_ids}
            raise ValidationError(msg, code=code, params=params)

    @classmethod
    def validate_vendor_user_inputs(cls, info, instance, data, vendor_register=False):
        branches = data.get('branches')
        departments = data.get('departments')
        vendor = data.get('vendor', instance.vendor)
        vendor_user_type = data.get('vendor_user_type', instance.vendor_user_type)
        app_role = data.get('app_role', instance.app_role)

        if not vendor_user_type and cls is UserCreate:
            raise ValidationError({
                "vendor_user_type": "vendor user type is required to create vendor user"
            })

        if vendor_user_type == VendorUserTypes.MANAGER and app_role != AppRoleTypes.ADMIN:
            raise ValidationError({
                "vendor_user_type": "appRole must be Admin to create Manager vendor"
            })

        # only vendor of type doctor and nurse can be assigned to a department
        if departments and vendor_user_type not in [VendorUserTypes.DOCTOR,
                                                    VendorUserTypes.NURSE,
                                                    VendorUserTypes.RECEPTIONIST]:
            raise ValidationError({
                "departments": "can assign only doctor or receptionist or nurse to"
                               " a department"
            })

        if not vendor:
            raise ValidationError({
                "vendor": "vendor is required to create vendor user"
            })

        if data.get("payer"):
            raise ValidationError({
                "payer": f"payer must only be provided when creating payer users"
            })

        if app_role == AppRoleTypes.ADMIN and cls.current_user_can_create_vendor_admin(
                vendor_register):
            if data.get("add_groups"):
                raise ValidationError({
                    "add_groups": "add_groups should be empty. "
                                  "When creating a vendor user, "
                                  "it will be added to vendor_staff group automatically"
                })
            if data.get("remove_groups"):
                raise ValidationError({
                    "remove_groups": "remove_groups should be empty when creating "
                                     "SuperVendor user "
                })
            if data.get("branches"):
                raise ValidationError({
                    "branches": "should be empty when appRole is ADMIN"
                })

        if app_role != AppRoleTypes.ADMIN and not branches \
                and (cls is UserCreate or (
                cls is UserUpdate and instance.app_role == AppRoleTypes.ADMIN)):
            raise ValidationError({
                "branches": "must not be empty when appRole is USER"
            })

        cls.validate_default_branch(data, instance)
        if app_role == AppRoleTypes.USER:
            if data.get("add_groups"):
                raise ValidationError({
                    "add_groups": "add_groups should be empty. "
                                  "When creating a vendor user, "
                                  "it will be added to vendor_staff group automatically"
                })
            if data.get("remove_groups"):
                raise ValidationError({
                    "remove_groups": "remove_groups should be empty when "
                                     "creating vendor user"
                })

        if vendor_user_type == VendorUserTypes.DOCTOR:
            group = get_doctor_group()
        elif vendor_user_type == VendorUserTypes.PHARMACIST:
            group = get_pharmacist_vendor_group()
        elif vendor_user_type == VendorUserTypes.NURSE:
            group = get_nurse_group()
        elif vendor_user_type == VendorUserTypes.RECEPTIONIST:
            group = get_receptionist_group()
        elif vendor_user_type == VendorUserTypes.MANAGER:
            group = get_super_vendor_group()
        elif vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST:
            group = get_dental_hygienist()
        elif vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR:
            group = get_diabetes_educator()
        elif vendor_user_type == VendorUserTypes.FITNESS_COACH:
            group = get_fitness_coach()
        elif vendor_user_type == VendorUserTypes.NUTRITIONIST:
            group = get_nutritionist()
        elif vendor_user_type == VendorUserTypes.OPTOMETRIST:
            group = get_optometrist()
        elif vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT:
            group = get_podiatric_medical_assistant()
        elif vendor_user_type == VendorUserTypes.PSYCHOLOGIST:
            group = get_psychologist()
        elif vendor_user_type == VendorUserTypes.SOCIAL_WORKER:
            group = get_social_worker()
        elif vendor_user_type == VendorUserTypes.RCM:
            group = get_rcm_group()
        else:
            raise ValidationError({
                "vendor_user_type": "unknown type"
            })
        data["add_groups"] = [group]
        if app_role == AppRoleTypes.ADMIN:
            data["add_groups"].extend([get_super_vendor_group()])
        if app_role == AppRoleTypes.USER:
            data["remove_groups"] = [get_super_vendor_group()]

        if branches:
            branches_pks = [branch.pk for branch in branches]
            if len(branches) != vendor.branches.filter(pk__in=branches_pks).count():
                raise ValidationError({
                    "branches": "there are branches does not belong user vendor"
                })

        if departments:
            departments_pks = [department.pk for department in departments]
            if len(departments) != vendor.branches.prefetch_related(
                    "departments").filter(departments__id__in=departments_pks).count():
                raise ValidationError({
                    "departments": "there are departments do not belong user vendor"
                })

        return data

    @classmethod
    def current_user_can_create_vendor_admin(cls, vendor_register):
        current_user = get_current_user()
        current_user_app = getattr(current_user, 'app_type', None)
        current_user_role = getattr(current_user, 'app_role', None)
        if current_user_app == AppTypes.ADMIN or (
                current_user_app == AppTypes.VENDOR
                and current_user_role == AppRoleTypes.ADMIN) or vendor_register:
            return True
        raise ValidationError("you are not allowed to create Admin vendor users")

    @classmethod
    def validate_app_roles_manipulation(cls, logged_user, instance, data):

        input_data = data.get('input', {})
        app_type = input_data.get('app_type', instance.app_type)
        app_role = input_data.get('app_role', None)
        if app_role is None:
            return

        if logged_user.id == instance.pk and instance.app_role != app_role:
            raise PermissionDenied()

        if logged_user.id != instance.pk:
            can_manage_user = logged_user.is_superuser or logged_user.is_vendor_admin or logged_user.is_payer_admin
            if can_manage_user:
                return
            # allow staff to create/update vendor admin
            if app_role == AppRoleTypes.ADMIN and app_type == AppTypes.ADMIN:
                raise PermissionDenied()

        if not logged_user.is_anonymous:  # CustomerCreate done with anonymous user

            # prevent user from creating/updating users of higher app_role
            # except (ADMIN USER can create VENDOR ADMIN)
            if logged_user.app_role != AppRoleTypes.ADMIN and \
                    app_role == AppRoleTypes.ADMIN and logged_user.app_type == app_type:
                raise PermissionDenied()

            # user other than admin cannot create/update users of other types
            if logged_user.app_type != AppTypes.ADMIN and logged_user.app_type != app_type:
                raise PermissionDenied()

        elif app_type != AppTypes.CUSTOMER:
            raise PermissionDenied()

        return

    @classmethod
    def add_remove_user_to_keycloak_group(cls, cleaned_input, user):
        client_name = settings.KEYCLOAK_CONFIG['CLIENT_ID']
        client_id = KeycloakAPI().get_client_id(client_name)
        if not client_id:
            logging.error(f"Could not get keycloak client id {client_name}")
            raise ValidationError({
                "sso": "user creation failed on sso server, "
                       "please contact super admin"
            })

        add_groups = cleaned_input.get("add_groups", [])
        for group in add_groups:
            keycloak_group_id = group.group_configuration.keycloak_group_id
            KeycloakAPI().create_update_group(
                name=group.name,
                client_id=client_id,
                added_roles=[],
                added_users=[user],
                removed_roles=[],
                removed_users=[],
                is_create=False,
                group_id=keycloak_group_id)

        remove_groups = cleaned_input.get("remove_groups", [])
        for group in remove_groups:
            keycloak_group_id = group.group_configuration.keycloak_group_id
            KeycloakAPI().create_update_group(
                name=group.name,
                client_id=client_id,
                added_roles=[],
                added_users=[],
                removed_roles=[],
                removed_users=[user],
                is_create=False,
                group_id=keycloak_group_id)

    @classmethod
    def validate_payer_user_inputs(cls, info, instance, data):
        payer = data.get('payer', instance.payer)
        app_role = data.get('app_role', instance.app_role)

        if not payer:
            raise ValidationError({
                "payer": "payer is required to create payer user"
            })

        if (
                app_role == AppRoleTypes.ADMIN and cls.current_user_can_create_payer_admin()) \
                or app_role == AppRoleTypes.USER:
            if data.get("add_groups"):
                raise ValidationError({
                    "add_groups": "add_groups should be empty when creating "
                                  "Payer user "
                                  "will be added to group Automatically"
                })

            if data.get("remove_groups"):
                raise ValidationError({
                    "remove_groups": "remove_groups should be empty when creating payer"
                })

        group = get_payer_user_group()

        data["add_groups"] = [group]
        if app_role == AppRoleTypes.ADMIN:
            data["add_groups"].extend([get_payer_admin_group()])
        if app_role == AppRoleTypes.USER:
            data["remove_groups"] = [get_payer_admin_group()]

    @classmethod
    def current_user_can_create_payer_admin(cls):
        current_user = get_current_user()
        current_user_app = getattr(current_user, 'app_type', None)
        current_user_role = getattr(current_user, 'app_role', None)
        if current_user_app == AppTypes.ADMIN or (
                current_user_app == AppTypes.PAYER
                and current_user_role == AppRoleTypes.ADMIN):
            return True
        raise ValidationError("you are not allowed to create Admin payer users")

    @classmethod
    def validate_default_branch(cls, data, instance):
        app_role = data.get('app_role', instance.app_role)
        vendor = data.get('vendor', instance.vendor)
        branches = data.get('branches')
        default_branch = data.get('default_branch')

        if cls is UserCreate:
            if not default_branch:
                raise ValidationError({
                    "default_branch": "default branch is required to create vendor user"
                })
            if app_role == AppRoleTypes.ADMIN and default_branch.vendor_id != vendor.id:
                raise ValidationError({
                    "default_branch": "branch does not belong to this vendor"
                })
            if app_role == AppRoleTypes.USER and default_branch not in branches:
                raise ValidationError({
                    "default_branch": "default branch must be one of the branches"
                })

        if cls is UserUpdate and default_branch:
            if instance.is_vendor_admin and instance.vendor_id != default_branch.vendor_id:
                raise ValidationError({
                    "default_branch": "cannot update to branch that does not belong to this vendor"
                })
            branches = branches or []
            if instance.is_vendor_staff and default_branch not in instance.branches.all() and default_branch not in branches:
                raise ValidationError({
                    "default_branch": "cannot update to branch that does not belong to this user"
                })


def handle_doctor_create(_root, info, doctor_info, user):
    if not doctor_info:
        raise ValidationError({
            "doctor_info": f"doctorInfo must be provided when creating doctor user"
        })

    if not doctor_info.health_license_number:
        raise ValidationError({
            "health_license_number": "Health license number is required"
        })

    vendor_id = graphene.Node.to_global_id("Vendor", user.vendor_id)

    doctor_create_result = DoctorCreate().perform_mutation(
        _root, info, **{
            "vendor_id": vendor_id,
            "input": doctor_info
        }
    )

    if doctor_create_result.doctor:
        doctor_create_result.doctor.user = user
        doctor_create_result.doctor.save(update_fields=["user"])


def handle_address_create(_root, info, address_info, created_user):
    current_user = info.context.user
    info.context.user = created_user
    AccountAddressCreate().perform_mutation(
        _root, info, **{
            "input": address_info
        }
    )
    info.context.user = current_user


class UserCreate(BaseAccountMutationMixin):
    class Arguments:
        input = AdminUserCreateInput(
            description="Fields required to create user.", required=True
        )

    class Meta:
        description = "Creates a new user."
        exclude = ["password"]
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return super().check_permissions(context, permissions=permissions)

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:

        authorization_dto = AuthorizationDto(instance, data, user_create=True)

        current_user = get_current_user()
        if not authorization.can_manage_user(current_user, authorization_dto):
            raise PermissionDenied()

        return {}

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        vendor_register = data.pop('vendor_register', False)
        if not data.get("national_id") and not data.get(
                "app_type") == AppTypes.CUSTOMER:
            raise ValidationError(
                {
                    "national_id": ValidationError(
                        "nationalId must not be empty in creating user.",
                        code=AccountErrorCode.REQUIRED
                    )
                }
            )

        if data.get("nationality"):
           res= BackendClient().validate_code_system_concept(data.get("nationality"), "NATIONALITY")
           if not res:
               raise ValidationError(
                   {
                       "nationality": ValidationError(
                           "nationality code is not valid",
                           code=AccountErrorCode.INVALID
                       )
                   }
               )
        cleaned_input = super().clean_input(info, instance, data, input_cls=input_cls)
        app_type = cleaned_input['app_type']
        cleaned_input['doctor_id'] = data.pop('doctor_id', None)
        password = cleaned_input.get('password') or ""
        if not password.strip():
            raise ValidationError({
                "password": "password must not be empty"
            })

        app_type = cleaned_input['app_type']
        if app_type not in [AppTypes.ADMIN, AppTypes.VENDOR, AppTypes.PAYER,
                            AppTypes.CUSTOMER]:
            raise ValidationError({
                "appType": f"you can't create {app_type} user."
            })

        vendor_user_type = cleaned_input.get("vendor_user_type")
        vendor = cleaned_input.get("vendor")
        if app_type in [AppTypes.ADMIN, AppTypes.PAYER] and (
                vendor_user_type or vendor):
            raise ValidationError({
                "vendor_user_type,vendor": f"must only be provided when creating vendor user"
            })

        cls.clean_health_license_number(app_type, cleaned_input)

        payer = cleaned_input.get("payer")
        if app_type in [AppTypes.ADMIN, AppTypes.VENDOR] and payer:
            raise ValidationError({
                "payer": f"must only be provided when creating payer user"
            })

        vendor_id = None
        if app_type == AppTypes.VENDOR:
            cls.validate_vendor_user_inputs(info, instance, cleaned_input,
                                            vendor_register)
            vendor = cleaned_input.get('vendor', instance.vendor)
            vendor_id = vendor.id

        if vendor_user_type != VendorUserTypes.DOCTOR and cleaned_input.get(
                "doctor_info"):
            raise ValidationError({
                "doctor_info": f"must only be provided when creating doctor user"
            })

        payer_id = None
        if app_type == AppTypes.PAYER:
            cls.validate_payer_user_inputs(info, instance, cleaned_input)
            payer = cleaned_input.get('payer', instance.payer)
            payer_id = payer.id

        errors = defaultdict(list)
        requestor = info.context.user
        cls.clean_groups(requestor, cleaned_input, errors, app_type, vendor_id,
                         payer_id)
        if errors:
            raise ValidationError(errors)

        if data.get("preferred_language"):
            languages = fetch_language_instances_by_codes(
                [data.get("preferred_language")])
            cleaned_input["preferred_language"] = languages[0]

        return cleaned_input

    @classmethod
    def _save_m2m(cls, info, instance, cleaned_data):
        super()._save_m2m(info, instance, cleaned_data)
        groups = cleaned_data.get("add_groups")
        if groups:
            instance.groups.add(*groups)

        branches = cleaned_data.get('branches')
        if branches:
            instance.branches.set(branches)

        departments = cleaned_data.get('departments')
        if departments:
            instance.departments.set(departments)

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        result = cls.handle_user_create(_root, data, info)
        user = result.user
        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(user, keycloak_user_data["attributes"])
        prepare_user_branches([], user, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(user.sso_id, keycloak_user_data)
        threading.Thread(target=cls.get_meeting_platform_id, args=(data, user)).start()
        return result

    @classmethod
    def get_meeting_platform_id(cls, data, user):
        logging.info("Generating meeting platform id")
        if user.app_type == AppTypes.VENDOR and not user.meeting_platform_id:
            password = data['input'].get('password')
            meeting_platform_id = token_utils.generate_meeting_platform_id(user,
                                                                           str(user.id),
                                                                           password)
            logging.info(f"Generated meeting platform id {meeting_platform_id}")
            if meeting_platform_id:
                user.meeting_platform_id = meeting_platform_id
                user.save(update_fields=['meeting_platform_id'])
                keycloak_user_data = {"attributes": {}}
                prepare_user_attributes(user, keycloak_user_data["attributes"])
                prepare_user_branches([], user, keycloak_user_data["attributes"])
                KeycloakAPI().update_user(user.sso_id, keycloak_user_data)

    @classmethod
    @transaction.atomic
    def handle_user_create(cls, _root, data, info):
        password = data['input'].get('password')
        instance = cls.get_instance(info, **data)
        auth_data = cls.check_authorization(_root, info, instance, **data)
        data = data.get("input")
        cleaned_input = cls.clean_input(info, instance, data)
        if auth_data and cleaned_input:
            cleaned_input.update(auth_data)
        instance = cls.construct_instance(instance, cleaned_input)
        cls.clean_instance(info, instance)
        cls.save(info, instance, cleaned_input)
        cls._save_m2m(info, instance, cleaned_input)
        result = cls.success_response(instance)
        user = result.user

        if cleaned_input['app_type'] == AppTypes.VENDOR:
            vendor_user_type = cleaned_input.get("vendor_user_type")
            if vendor_user_type == VendorUserTypes.DOCTOR:
                doctor_info = cleaned_input.get("doctor_info")
                cls.validate_health_licence_dates(None, None, doctor_info)
                handle_doctor_create(_root, info, doctor_info, user)

            if vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST:
                dental_hygienist_info = cleaned_input.get("dental_hygienist_info")
                cls.handle_health_professional_user_create(dental_hygienist_info, user,
                                                           DentalHygienist)

            if vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR:
                diabetes_educator_info = cleaned_input.get("diabetes_educator_info")
                cls.handle_health_professional_user_create(diabetes_educator_info, user,
                                                           DiabetesEducator)

            if vendor_user_type == VendorUserTypes.FITNESS_COACH:
                fitness_coach_info = cleaned_input.get("fitness_coach_info")
                cls.handle_health_professional_user_create(fitness_coach_info, user,
                                                           FitnessCoach)

            if vendor_user_type == VendorUserTypes.NUTRITIONIST:
                nutritionist_info = cleaned_input.get("nutritionist_info")
                cls.handle_health_professional_user_create(nutritionist_info, user,
                                                           Nutritionist)

            if vendor_user_type == VendorUserTypes.OPTOMETRIST:
                optometrist_info = cleaned_input.get("optometrist_info")
                cls.handle_health_professional_user_create(optometrist_info, user,
                                                           Optometrist)

            if vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT:
                podiatric_medical_assistant_info = cleaned_input.get("podiatric_medical_assistant_info")
                cls.handle_health_professional_user_create(podiatric_medical_assistant_info,
                                                           user,PodiatricMedicalAssistant)

            if vendor_user_type == VendorUserTypes.PSYCHOLOGIST:
                psychologist_info = cleaned_input.get("psychologist_info")
                cls.handle_health_professional_user_create(psychologist_info, user,
                                                           Psychologist)

            if vendor_user_type == VendorUserTypes.SOCIAL_WORKER:
                social_worker_info = cleaned_input.get("social_worker_info")
                cls.handle_health_professional_user_create(social_worker_info, user,
                                                           SocialWorker)

            if vendor_user_type == VendorUserTypes.NURSE:
                nurse_info = cleaned_input.get("nurse_info")
                cls.handle_health_professional_user_create(nurse_info, user,
                                                           Nurse)

            if vendor_user_type == VendorUserTypes.PHARMACIST:
                pharmacist_info = cleaned_input.get("pharmacist_info")
                cls.handle_pharmacist_create(info, pharmacist_info, user)

            if vendor_user_type == VendorUserTypes.RECEPTIONIST:
                Receptionist.objects.create(
                    user=user
                )

            if vendor_user_type == VendorUserTypes.MANAGER:
                Manager.objects.create(
                    user=user
                )
            if vendor_user_type == VendorUserTypes.RCM:
                RCM.objects.create(
                    user=user
                )

        if cleaned_input.get("address"):
            address_info = cleaned_input.get("address")
            handle_address_create(_root, info, address_info, user)

        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(user, keycloak_user_data["attributes"])
        prepare_user_branches([], user, keycloak_user_data["attributes"])
        if user:
            response_data = KeycloakAPI().create_user({
                'username': user.id,
                'email': user.email,
                'credentials': [{
                    'value': password,
                    'type': "password",
                    'temporary': False
                }],
                'firstName': user.first_name,
                'lastName': user.last_name,
                'enabled': user.is_active,
                'attributes': keycloak_user_data["attributes"]
            })

            if not response_data:
                raise ValidationError({
                    "sso": "user creation failed on sso server, "
                           "please contact super admin"
                })

            user.sso_id = response_data
            user.save(update_fields=['sso_id'])

            cls.add_remove_user_to_keycloak_group(cleaned_input, user)
        return result

    @classmethod
    def handle_health_professional_user_create(cls, info, user, model):
        health_license_number = info.health_license_number if info else None
        health_license_start_date = info.health_license_start_date if info else None
        health_license_end_date = info.health_license_end_date if info else None
        if info:
            cls.validate_health_licence_dates(None, None, info)
        model.objects.create(
            health_license_number=health_license_number,
            health_license_start_date=health_license_start_date,
            health_license_end_date=health_license_end_date,
            user=user
        )

    @classmethod
    def handle_pharmacist_create(cls, info, pharmacist_info, user):
        if not pharmacist_info:
            raise ValidationError({
                "pharmacist_info": "pharmacist info must be provided when creating pharmacist user"
            })
        if not pharmacist_info.health_license_number:
            raise ValidationError({
                "health_license_number": "Health license number is required"
            })

        cls.validate_health_licence_dates(info, None, pharmacist_info)

        Pharmacist.objects.create(
            user=user,
            health_license_number=pharmacist_info.health_license_number,
            health_license_start_date=pharmacist_info.health_license_start_date,
            health_license_end_date=pharmacist_info.health_license_end_date
        )


class UserUpdateInput(UserInput):
    password = graphene.String()
    add_groups = graphene.List(
        graphene.NonNull(graphene.ID),
        description="List of permission group IDs to which user should be assigned.",
        required=False,
    )
    remove_groups = graphene.List(
        graphene.NonNull(graphene.ID),
        description=(
            "List of permission group IDs from which user should be unassigned."
        ),
        required=False,
    )

    app_role = AppRoleTypeEnum(required=False)

    branches = graphene.List(graphene.ID,
                             description="vendor branches this user has access to",
                             required=False)
    default_branch = graphene.ID(
        description="default branch for the user",
        required=False
    )

    doctor_info = DoctorInput(required=False)
    nurse_info = NurseInput(required=False)
    pharmacist_info = PharmacistInput(required=False)
    dental_hygienist_info = DentalHygienistInput(required=False)
    diabetes_educator_info = DiabetesEducatorInput(required=False)
    fitness_coach_info = FitnessCoachInput(required=False)
    nutritionist_info = NutritionistInput(required=False)
    optometrist_info = OptometristInput(required=False)
    podiatric_medical_assistant_info = PodiatricMedicalAssistantInput(required=False)
    psychologist_info = PsychologistInput(required=False)
    social_worker_info = SocialWorkerInput(required=False)

    health_license_number = graphene.String(required=False)

    health_license_start_date = graphene.Date(required=False)

    health_license_end_date = graphene.Date(required=False)

    photo = graphene.String(description="photo of the user")

class UserActiveStatusUpdate(BaseAccountMutationMixin):
    class Arguments:
        id = graphene.ID(required=True)

    class Meta:
        description = "Updates an existing user."
        exclude = ["password"]
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def clean_input(cls, info, instance, data):
        return super().clean_input(info, instance, data)
        return cleaned_input

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        current_user = get_current_user()
        authorization_dto = AuthorizationDto(instance, data)
        if not authorization.can_manage_user(current_user, authorization_dto):
            raise PermissionDenied()

        return {}

    @classmethod
    def save(cls, info, instance, cleaned_input):
        instance.is_active = not instance.is_active
        super().save(info, instance, cleaned_input)
        if instance.is_consumer:
            raise ValidationError({
                "is_active": "cannot deactivate/activate customer user"
            })

        keycloak_user_data = {"attributes": {},'enabled': instance.is_active}
        prepare_user_attributes(instance, keycloak_user_data["attributes"])
        prepare_user_branches([], instance, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(instance.sso_id, keycloak_user_data)

class UserUpdate(BaseAccountMutationMixin):
    class Arguments:
        id = graphene.ID(required=True)
        input = UserUpdateInput(required=True)

    class Meta:
        description = "Updates an existing user."
        exclude = ["password"]
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        current_user = get_current_user()

        # user cannot update his app_role
        app_role = data.get('input').get('app_role')
        if app_role and current_user.id == instance.pk and instance.app_role != app_role:
            raise PermissionDenied()

        authorization_dto = AuthorizationDto(instance, data)
        if not authorization.can_manage_user(current_user, authorization_dto):
            raise PermissionDenied()

        return {}

    @classmethod
    def clean_input(cls, info, instance, data):
        requestor = info.context.user
        # check if requestor can manage this user
        # if not (requestor.is_superuser or (requestor.is_staff and instance.is_vendor)) \
        #         and get_out_of_scope_users(requestor, [instance]):
        #     msg = "You can't manage this user."
        #     code = AccountErrorCode.OUT_OF_SCOPE_USER.value
        #     raise ValidationError({"id": ValidationError(msg, code=code)})

        if requestor.pk == instance.pk and not requestor.is_consumer:
            raise ValidationError({
                "is_active": "You can't update your own account."
            })
        cls.check_for_duplicates(data)

        cleaned_input = super().clean_input(info, instance, data)

        password = cleaned_input.get('password', None)
        if password is not None and not password.strip():
            raise ValidationError({
                "password": "password must not be empty"
            })

        vendor_id = None
        if instance.app_type == AppTypes.VENDOR:
            cls.validate_vendor_user_inputs(info, instance, cleaned_input)
            vendor = cleaned_input.get('vendor', instance.vendor)
            vendor_id = vendor.id

        payer_id = None
        if instance.app_type == AppTypes.PAYER:
            cls.validate_payer_user_inputs(info, instance, cleaned_input)
            payer = cleaned_input.get('payer', instance.payer)
            payer_id = payer.id

        cls.clean_health_license_number(instance.app_type, cleaned_input)

        errors = defaultdict(list)
        requestor = info.context.user
        cls.clean_groups(requestor, cleaned_input, errors, instance.app_type, vendor_id,
                         payer_id)
        if errors:
            raise ValidationError(errors)

        if data.get("preferred_language"):
            languages = fetch_language_instances_by_codes(
                [data.get("preferred_language")])
            cleaned_input["preferred_language"] = languages[0]

        return cleaned_input

    @classmethod
    def _save_m2m(cls, info, instance, cleaned_data):
        super()._save_m2m(info, instance, cleaned_data)

        cls.add_remove_user_to_keycloak_group(cleaned_data, instance)

        add_groups = cleaned_data.get("add_groups")
        if add_groups:
            instance.groups.add(*add_groups)
        remove_groups = cleaned_data.get("remove_groups")
        if remove_groups:
            instance.groups.remove(*remove_groups)

        branches = cleaned_data.get('branches', instance.branches.all())
        if instance.app_type == AppTypes.VENDOR:
            if instance.app_role != AppRoleTypes.ADMIN and branches:
                instance.branches.set(branches)
            else:
                instance.branches.clear()

        departments = cleaned_data.get('departments', instance.departments.all())
        if departments:
            instance.departments.set(departments)

    @classmethod
    def save(cls, info, instance: models.User, cleaned_input):
        keycloak_user_data = {}
        first_name = cleaned_input.get('first_name')
        if first_name:
            keycloak_user_data['firstName'] = first_name

        last_name = cleaned_input.get('last_name')
        if last_name:
            keycloak_user_data['lastName'] = last_name

        is_active = cleaned_input.get('is_active', None)
        if is_active is not None:
            keycloak_user_data['enabled'] = is_active

        keycloak_user_data["attributes"] = {}
        prepare_user_attributes(instance, keycloak_user_data["attributes"])
        prepare_user_branches([], instance, keycloak_user_data["attributes"])

        if keycloak_user_data:
            KeycloakAPI().update_user(instance.sso_id, keycloak_user_data)

        password = cleaned_input.pop('password', None)
        if password:
            KeycloakAPI().set_user_password(instance.sso_id, password)

        super().save(info, instance, cleaned_input)

    @classmethod
    def handle_health_professional_user_update(cls, profile, profile_info, model):
        if profile and profile_info:
            cls.validate_health_licence_dates(None, profile, profile_info)
            model.objects.filter(id=profile.id).update(**profile_info)
            profile.refresh_from_db()

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        user_update_result = super().perform_mutation(_root, info, **data)

        doctor_info = data.get("input").get("doctor_info", None)
        dental_hygienist_info = data.get("input").get("dental_hygienist_info",
                                                         None)
        diabetes_educator_info = data.get("input").get("diabetes_educator_info",
                                                      None)
        fitness_coach_info = data.get("input").get("fitness_coach_info",
                                                       None)
        nutritionist_info = data.get("input").get("nutritionist_info",
                                                   None)
        optometrist_info = data.get("input").get("optometrist_info",
                                                  None)
        podiatric_medical_assistant_info = data.get("input").get("podiatric_medical_assistant_info",
                                                 None)
        psychologist_info = data.get("input").get("psychologist_info")

        nurse_info = data.get("input").get("nurse_info")
        pharmacist_info = data.get("input").get("pharmacist_info")
        social_worker_info = data.get("input").get("social_worker_info",
                                                  None)
        updated_user = user_update_result.user

        if updated_user and not user_update_result.errors and updated_user.doctor_id and doctor_info:
            doctor_id = graphene.Node.to_global_id("Doctor",
                                                   user_update_result.user.doctor_id)
            cls.validate_health_licence_dates(None, updated_user.doctor,
                                              doctor_info)
            DoctorUpdate().perform_mutation(
                _root, info, **{
                    "id": doctor_id,
                    "input": doctor_info
                }
            )

        if updated_user and not user_update_result.errors:
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST):
                cls.handle_health_professional_user_update(
                    updated_user.dental_hygienist,
                    dental_hygienist_info, DentalHygienist)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR):
                cls.handle_health_professional_user_update(updated_user.diabetes_educator,
                                                           diabetes_educator_info,DiabetesEducator)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.FITNESS_COACH):
                cls.handle_health_professional_user_update(updated_user.fitness_coach,
                                                       fitness_coach_info,FitnessCoach)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.NUTRITIONIST):
                cls.handle_health_professional_user_update(updated_user.nutritionist,
                                                       nutritionist_info,Nutritionist)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.OPTOMETRIST):
                cls.handle_health_professional_user_update(updated_user.optometrist,
                                                       optometrist_info,Optometrist)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT):
                cls.handle_health_professional_user_update(updated_user.podiatric_medical_assistant,
                                                       podiatric_medical_assistant_info,PodiatricMedicalAssistant)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.PSYCHOLOGIST):
                cls.handle_health_professional_user_update(updated_user.psychologist,
                                                       psychologist_info,Psychologist)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.SOCIAL_WORKER):
                cls.handle_health_professional_user_update(updated_user.social_worker,
                                                       social_worker_info,SocialWorker)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.PHARMACIST):
                cls.handle_health_professional_user_update(updated_user.pharmacist,
                                                       pharmacist_info,Pharmacist)
            if (updated_user.vendor_user_type and
                    updated_user.vendor_user_type == VendorUserTypes.NURSE):
                cls.handle_health_professional_user_update(updated_user.nurse,
                                                       nurse_info,Nurse)
        return user_update_result


class UserDelete(StaffDeleteMixin, BaseUserDelete):
    class Meta:
        description = "Deletes a user."
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    class Arguments:
        id = graphene.ID(required=True, description="ID of a user to delete.")

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:

        authorization_dto = AuthorizationDto(instance, data)

        current_user = get_current_user()
        if not authorization.can_manage_user(current_user, authorization_dto):
            raise PermissionDenied()

        return {}

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        user = result.user
        if user.doctor_id:
            user.doctor.delete()

        return result


class MeetingPlatformIdUpdate(BaseMutation):
    result = graphene.String(description="result")

    class Arguments:
        user_id = graphene.ID(
            required=False, description="User id."
        )

        meeting_platform_id = graphene.String(required=True)

    class Meta:
        description = "update meeting platform id"
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_superuser

    @classmethod
    def perform_mutation(cls, root, info, **data):
        user_id = get_database_id(data.get("user_id"), "User")
        user = models.User.objects.filter(
            id=user_id).first()
        user.meeting_platform_id = data.get('meeting_platform_id')
        user.save()
        return MeetingPlatformIdUpdate(result="success")


# mutation for the user to update basic information of his profile
class UserProfileUpdate(ModelMutation):
    class Arguments:
        input = UserProfileUpdateInput(
            required=True, description="Fields required to update user profile."
        )

    class Meta:
        description = "Updates the profile of the logged-in user."
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()

        if instance.is_consumer:
            raise PermissionDenied()

        return {}

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user()

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data)
        if data.get("preferred_language"):
            languages = fetch_language_instances_by_codes(
                [data.get("preferred_language")])
            cleaned_input["preferred_language"] = languages[0]
        return cleaned_input

    @classmethod
    @transaction.atomic
    def save(cls, info, instance: models.User, cleaned_input):
        # keycloak_user_data = {}
        # first_name = cleaned_input.get('first_name')
        # if first_name:
        #     keycloak_user_data['firstName'] = first_name
        #
        # last_name = cleaned_input.get('last_name')
        # if last_name:
        #     keycloak_user_data['lastName'] = last_name
        #
        # keycloak_user_data["attributes"] = {}
        # prepare_user_attributes(instance, keycloak_user_data["attributes"])
        # prepare_user_branches([], instance, keycloak_user_data["attributes"])
        #
        # if keycloak_user_data:
        #     KeycloakAPI().update_user(instance.sso_id, keycloak_user_data)

        super().save(info, instance, cleaned_input)


class UnbindParent(ModelMutation):
    class Arguments:
        pass

    class Meta:
        description = "Unbind parent from child"
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()
        return {}

    @classmethod
    def clean_input(cls, info, instance, data):
        if instance.date_of_birth is None or datetime_utils.calculate_age(
                instance.date_of_birth) < 18:
            raise ValidationError("Patient age must be older than 18")
        if instance.parent_user is None:
            raise ValidationError("Patient is not child")
        if not instance.email and not instance.mobile:
            raise ValidationError("Patient must have email or mobile")
        return super().clean_input(info, instance, data)

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user()

    @classmethod
    @transaction.atomic
    def save(cls, info, instance: models.User, cleaned_input):
        # keycloak_user_data = {}
        # instance.parent_user = None
        # instance.relation_type = None
        # keycloak_user_data["attributes"] = {}
        # prepare_user_attributes(instance, keycloak_user_data["attributes"])
        # prepare_user_branches([], instance, keycloak_user_data["attributes"])
        # KeycloakAPI().update_user(instance.sso_id, keycloak_user_data)

        super().save(info, instance, cleaned_input)


class UnbindDependentInput(graphene.InputObjectType):
    dependent_user_id = graphene.ID(
        required=True, description="User id."
    )


class UnbindDependent(ModelMutation):
    class Arguments:
        input = UnbindDependentInput(required=True)

    class Meta:
        description = "Unbind parent from child"
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()
        return {}

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        dependent_user_id = get_database_id(data.get("dependent_user_id"),
                                            "User")
        dependent_user = models.User.objects.filter(
            id=dependent_user_id).first()
        if not dependent_user:
            raise ValidationError("Dependent user not found")
        if not dependent_user.parent_user or dependent_user.parent_user.id != instance.id:
            raise ValidationError("user is not dependent to you")

        if dependent_user.date_of_birth is None or datetime_utils.calculate_age(
                dependent_user.date_of_birth) < 18:
            raise ValidationError("Dependent age must be older than 18")
        if not dependent_user.email and not dependent_user.mobile:
            raise ValidationError("Dependent must have email or mobile")
        data["dependent_user"] = dependent_user
        return super().clean_input(info, instance, data)

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        super().perform_mutation(_root, info, **data)
        current_user = get_current_user()
        dependent_user = data.get('input').get("dependent_user")
        dependent_user.parent_user = None
        dependent_user.relation_type = None
        dependent_user.save()
        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(dependent_user, keycloak_user_data["attributes"])
        prepare_user_branches([], dependent_user, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(dependent_user.sso_id, keycloak_user_data)
        return UnbindDependent(user=current_user)

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user()


class ApproveTermsAndConditions(ModelMutation):
    class Arguments:
        pass

    class Meta:
        description = "Approve terms and conditions"
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()
        return {}

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user()

    @classmethod
    @transaction.atomic
    def save(cls, info, instance: models.User, cleaned_input):
        site_settings = Site.objects.get_current().settings
        if instance.terms_and_conditions_accepted_version == site_settings.terms_and_conditions_version:
            raise ValidationError("Terms and conditions version {} is already approved".format(
                site_settings.terms_and_conditions_version))

        instance.terms_and_conditions_accepted_version = site_settings.terms_and_conditions_version
        instance.save()
        return super().save(info, instance, cleaned_input)


class DeleteCurrentCustomerUser(ModelMutation):
    class Arguments:
        delete_reason = graphene.String(required=True, description="reason for deletion")

    class Meta:
        description = "Deletes current user."
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.DELETE_CUSTOMER_ACCOUNT

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated and context.user.is_consumer

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()
        return {}

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user()

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        delete_reason = data.get('delete_reason')
        current_user = get_current_user()
        KeycloakAPI().delete_user(current_user.sso_id)
        current_user.delete_reason = delete_reason
        if current_user.mobile:
            current_user.mobile = str(current_user.mobile) + "_deleted_"+str(current_user.id)
        current_user.save()
        current_user.delete()
        for child in current_user.dependents.all():
            KeycloakAPI().delete_user(child.sso_id)
            child.delete_reason = delete_reason
            child.save()
            child.delete()
        return DeleteCurrentCustomerUser(user=current_user)

class UpdateAskToEnableBioLogin(ModelMutation):
    class Arguments:
        pass

    class Meta:
        description = "Update ask_to_enable_bio_login to true"
        model = models.User
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.is_active:
            raise PermissionDenied()
        return {}

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user()

    @classmethod
    @transaction.atomic
    def save(cls, info, instance: models.User, cleaned_input):
        instance.ask_to_enable_bio_login = False
        instance.save()
        return super().save(info, instance, cleaned_input)
