import graphene
from graphene import relay
from graphene_federation import key

from ...core.connection import CountableDjangoObjectType
from ....keycloak_permissions import models


@key(fields="id")
class KeyCloakPermission(CountableDjangoObjectType):
    keycloak_role_id = graphene.String()
    is_staff = graphene.Boolean()
    is_vendor = graphene.Boolean()
    is_client = graphene.Boolean()

    class Meta:
        description = "Represents a permission object in a friendly form."
        interfaces = [relay.Node]
        model = models.KeyCloakPermission
        fields = ["keycloak_role_id",
                  "is_staff",
                  "is_vendor",
                  "is_client",
                  "is_aggregator"]

    @staticmethod
    def resolve_id(root: models.KeyCloakPermission, info, *_args):
        return root.id

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, models.KeyCloakPermission)
