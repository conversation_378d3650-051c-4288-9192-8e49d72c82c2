import graphene
from django.core.exceptions import ValidationError
from graphene import Node

from usermanagment.account import models
from usermanagment.account.error_codes import AccountErrorCode
from usermanagment.auth.enums import AppTypes
from usermanagment.auth.exceptions import PermissionDenied
from usermanagment.auth.graphql.enums import AppRoleTypeEnum
from usermanagment.auth.permissions import AccountPermissions
from usermanagment.authorization import authorization
from usermanagment.graphql.account.mutations.base import BaseUserDelete
from usermanagment.graphql.account.mutations.users import UserCreateInput, UserCreate, \
    AdminUserCreateInput, UserInput, UserUpdate
from usermanagment.graphql.core.types.common import AccountError
from usermanagment.graphql.utils.request_utils import get_current_user


class VendorUserCreateInput(UserCreateInput):
    app_role = AppRoleTypeEnum(required=True)


class VendorUserCreate(UserCreate):
    class Arguments:
        input = VendorUserCreateInput(required=True)

    class Meta:
        description = "Creates a new vendor user."
        exclude = ["password"]
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        current_user = get_current_user()
        if not current_user.is_vendor:
            raise PermissionDenied()

        cls.validate_app_roles_manipulation(get_current_user(), instance, data)
        return {
            "vendor": current_user.vendor
        }

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        data['app_type'] = AppTypes.VENDOR
        data['vendor'] = Node.to_global_id('Vendor', get_current_user().vendor_id)

        cleaned_data = super().clean_input(info, instance, data,
                                           input_cls=AdminUserCreateInput)

        return cleaned_data


class VendorUserUpdateInput(UserInput):
    health_license_number = graphene.String(required=False)
    health_license_start_date = graphene.Date(required=False)
    health_license_end_date = graphene.Date(required=False)
    app_role = AppRoleTypeEnum(required=False)
    branches = graphene.List(graphene.ID, required=False)
    password = graphene.String(required=False)


class VendorUserUpdate(UserUpdate):
    class Arguments:
        id = graphene.ID(required=True)
        input = VendorUserUpdateInput(required=True)

    class Meta:
        description = "Updates an existing vendor user."
        exclude = ["password"]
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if instance.app_type not in [AppTypes.VENDOR]:
            raise ValidationError({"Cant update user using this mutation"},
                                  code=AccountErrorCode.INVALID)

        current_user = get_current_user()
        if not current_user.is_vendor or not authorization.can_manage_user(current_user,
                                                                           instance):
            raise PermissionDenied()

        cls.validate_app_roles_manipulation(get_current_user(), instance, data)
        return {}


class VendorUserDelete(BaseUserDelete):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor user to delete.")

    class Meta:
        description = "Deletes a vendor user."
        model = models.User
        permissions = (AccountPermissions.MANAGE_USERS,)
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        current_user = get_current_user()

        if not current_user.is_vendor or not authorization.can_manage_user(current_user,
                                                                           instance):
            raise PermissionDenied()

        return {}

    @classmethod
    def clean_instance(cls, info, instance):
        super().clean_instance(info, instance)
        if instance.is_vendor_admin:
            raise ValidationError(
                {
                    "id": ValidationError(
                        "Cannot delete this account.",
                        code=AccountErrorCode.DELETE_SUPERVENDOR_ACCOUNT,
                    )
                }
            )
