from django.db import transaction
from graphene import Node

from .types import AddressInput
from ..core.mutations import ModelMutation
from ..core.types.common import AccountError
from ..core.types.translations import TranslationMixin
from ..core.utils import from_global_id_strict_type
from ...account import models, ADDRESS_TRANSLATABLE_FIELDS
from ...block.models import Block
from django.core.exceptions import ValidationError


class AddressCreate(ModelMutation, TranslationMixin):
    class Arguments:
        input = AddressInput(required=True)

    class Meta:
        model = models.Address
        description = "Create a new address."
        error_type_class = AccountError
        error_type_field = "account_errors"
    # check if block id linked to city
    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls)
        city_id = data.get("city", Node.to_global_id('City',instance.city_id))
        if  data.get("block",instance.block_id):
            block_id = data.get("block", Node.to_global_id('Block',instance.block_id))
        else:
            block_id = None
        # Validate that the block is linked to the provided city
        if block_id and city_id:
            city_id = from_global_id_strict_type(city_id, "City")
            block_id = from_global_id_strict_type(block_id, "Block")
            if not Block.objects.filter(id=block_id, city_id=city_id).exists():
                raise ValidationError({
                    "block": "The provided block is not linked to the specified city."
                })

        return cleaned_data



class I18nMixin:

    @classmethod
    def construct_address_instance(cls, info, address_data: dict, root=None):
        address_id = None
        if root:
            address_id = root.address_id

        data = {
            "input": address_data
        }
        if address_id:
            data["id"] = Node.to_global_id("Address", address_id)

        address_create_result = AddressCreate().perform_mutation(None, info, **data)

        return address_create_result.address
