from ....chat.models import Message
from ....graphql.core.dataloaders import DataLoader


class MessagesByCustomerIdAndMessageIdLoader(DataLoader):
    context_key = "messages_by_customer_and_last_message"

    def batch_load(self, keys):

        message_ids = {}

        for key in keys:
            customer_id, message_id = key.split('__')
            message_ids[message_id] = key

        messages = Message.objects.filter(id__in=message_ids.keys())

        result_map = {}
        for message in messages:
            result_map[message_ids[str(message.id)]] = message

        return [result_map.get(key, None) for key in keys]
