from threading import local

from ...auth.anonymous_user import AnonymousUser

_thread_locals = local()


def set_current_user(user):
    _thread_locals.user = user


def set_current_context(context):
    _thread_locals.context = context


def set_current_gql_operation(gql_operation):
    _thread_locals.gql_operation = gql_operation


def get_current_user():
    return getattr(_thread_locals, 'user', AnonymousUser())


def get_current_context():
    return getattr(_thread_locals, 'context', None)


def get_current_gql_operation():
    return getattr(_thread_locals, 'gql_operation', None)
