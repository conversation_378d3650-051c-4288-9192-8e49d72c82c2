from inspect import signature
from typing import <PERSON>ple

from django.db.models import QuerySet

from ..core.enums import OrderDirection
from ..core.types import SortInputObjectType

REVERSED_DIRECTION = {
    "-": "",
    "": "-",
}

def _get_sorting_direction(sort_by):
    direction = sort_by.get("direction", "") if sort_by else ""
    sorting_desc = direction == OrderDirection.DESC
    return "lt" if sorting_desc else "gt"


def sort_queryset_for_connection(iterable, args):
    sort_by = args.get("sort_by")
    sorting_direction = _get_sorting_direction(sort_by)
    reversed = True if sorting_direction == "lt" else False
    if sort_by:
        iterable = sort_queryset(queryset=iterable, sort_by=sort_by,
                                 args=args)
    elif isinstance(iterable, QuerySet) and iterable.query.order_by:
        sort_by = get_sort_by_from_query(iterable)
    else:
        iterable, sort_by = sort_queryset_by_default(
            queryset=iterable, reversed=reversed
        )
    args["sort_by"] = sort_by
    return iterable, sort_by


def sort_queryset(
        queryset: QuerySet, sort_by: SortInputObjectType,
        args: dict = None
) -> QuerySet:
    """Sort queryset according to given parameters.

    rules:
        - when sorter has custom sorting method it's name must be like
            `prepare_qs_for_sort_{enum_name}` and it must return sorted queryset

    Keyword Arguments:
        queryset - queryset to be sorted
        sort_by - dictionary with sorting field and direction

    """
    sorting_direction = sort_by.direction

    sorting_field = sort_by.field

    sort_enum = sort_by._meta.sort_enum
    sorting_fields = sort_enum.get(sorting_field)
    sorting_field_name = sorting_fields.name.lower()

    custom_sort_by = getattr(sort_enum, f"qs_with_{sorting_field_name}", None)
    if custom_sort_by:
        sig = signature(custom_sort_by)
        if len(sig.parameters) > 1:
            queryset = custom_sort_by(queryset, args=args)
        else:
            queryset = custom_sort_by(queryset)

    sorting_field_value = sorting_fields.value
    sorting_list = [f"{sorting_direction}{field}" for field in sorting_field_value]

    return queryset.order_by(*sorting_list)


def get_model_default_ordering(model_class):
    default_ordering = []
    model_ordering = model_class._meta.ordering
    for field in model_ordering:
        if isinstance(field, str):
            default_ordering.append(field)
        else:
            direction = "-" if field.descending else ""
            default_ordering.append(f"{direction}{field.expression.name}")
    return default_ordering


def sort_queryset_by_default(
        queryset: QuerySet, reversed: bool
) -> Tuple[QuerySet, dict]:
    """Sort queryset by it's default ordering."""
    queryset_model = queryset.model
    default_ordering = ["pk"]
    if queryset_model and queryset_model._meta.ordering:
        default_ordering = get_model_default_ordering(queryset_model)

    ordering_fields = [field.replace("-", "") for field in default_ordering]
    direction = "-" if "-" in default_ordering[0] else ""
    if reversed:
        reversed_direction = REVERSED_DIRECTION[direction]
        default_ordering = [f"{reversed_direction}{field}" for field in ordering_fields]

    order_by = {"field": ordering_fields, "direction": direction}
    return queryset.order_by(*default_ordering), order_by


def get_sort_by_from_query(queryset: QuerySet) -> dict:
    ordering_fields = queryset.query.order_by
    direction = "-" if "-" in ordering_fields[0] else ""
    ordering_fields = [field.replace("-", "") for field in ordering_fields]

    return {"field": ordering_fields, "direction": direction}
