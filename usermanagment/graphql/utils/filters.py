from datetime import timedelta

from django.db.models import Q
from django.utils import timezone

from ..core.enums import ReportingPeriod


def filter_by_query_param(queryset, query, search_fields):
    """Filter queryset according to given parameters.

    Keyword Arguments:
        queryset - queryset to be filtered
        query - search string
        search_fields - fields considered in filtering

    """
    if query:
        query_by = {
            "{0}__{1}".format(field, "icontains"): query for field in search_fields
        }
        query_objects = Q()
        for q in query_by:
            query_objects |= Q(**{q: query_by[q]})
        return queryset.filter(query_objects).distinct()
    return queryset


def reporting_period_to_date(period):
    now = timezone.now()
    if period == ReportingPeriod.TODAY:
        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == ReportingPeriod.THIS_MONTH:
        start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    elif period == ReportingPeriod.LAST_7_DAYS:
        start_date = now - timedelta(days=7)
    else:
        raise ValueError("Unknown period: %s" % period)
    return start_date


def filter_by_period(queryset, period, field_name):
    start_date = reporting_period_to_date(period)
    return queryset.filter(**{"%s__gte" % field_name: start_date})


def filter_range_fields(qs, get_field, lte_field, value):
    gte, lte = value.get("gte"), value.get("lte")
    if gte is not None:
        lookup = {f"{get_field}__gte": gte}
        qs = qs.filter(**lookup)
    if lte is not None:
        lookup = {f"{lte_field}__lte": lte}
        qs = qs.filter(**lookup)
    return qs


def filter_range_field(qs, field, value):
    return filter_range_fields(qs, field, field, value)
