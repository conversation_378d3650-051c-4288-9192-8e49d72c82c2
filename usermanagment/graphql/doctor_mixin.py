from django.core.exceptions import ValidationError

from .utils.request_utils import get_current_user
from ..account.error_codes import AccountErrorCode
from ..authorization.authorization import AuthorizationDto
from ..doctor import AppointmentType


class DoctorMutationMixin:
    @classmethod
    def validate_years_of_experience(cls, cleaned_data, instance):
        years_of_experience = cleaned_data.get('years_of_experience',
                                               instance.years_of_experience)
        if years_of_experience and \
                (years_of_experience > 100 or years_of_experience < 0):
            raise ValidationError(
                {"yearsOfExperience": ValidationError("Years of experience should "
                                                      "be between 0 and 100",
                                                      code=AccountErrorCode.INVALID)
                 })

    @classmethod
    def validate_appointment_time_slot_period(cls, cleaned_data, instance):
        appointment_slot_time_period = cleaned_data.get('appointment_slot_time_period',
                                                        instance.appointment_slot_time_period)
        if appointment_slot_time_period and \
                (
                        appointment_slot_time_period > 120 or appointment_slot_time_period < 0):
            raise ValidationError(
                {"appointmentSlotTimePeriod": ValidationError(
                    "appointmentSlotTimePeriod should be between 0 and 100",
                    code=AccountErrorCode.INVALID)
                })

    @classmethod
    def validate_appointment_types(cls, cleaned_data, instance):
        appointment_types = cleaned_data.get('appointment_types',
                                             instance.appointment_types)
        if not appointment_types:
            raise ValidationError(
                {"appointmentTypes": ValidationError(
                    "This field is required.",
                    code=AccountErrorCode.REQUIRED)
                })


    @classmethod
    def validate_visit_price(cls, cleaned_data, instance):
        online_visit_price = cleaned_data.get('online_visit_price',
                                              instance.online_visit_price)
        onsite_visit_price = cleaned_data.get('onsite_visit_price',
                                              instance.onsite_visit_price)
        at_home_visit_price = cleaned_data.get('at_home_visit_price',
                                               instance.at_home_visit_price)
        appointment_types = cleaned_data.get('appointment_types',
                                             instance.appointment_types)
        if not appointment_types:
            raise ValidationError(
                {"appointmentTypes": ValidationError(
                    "This field is required.",
                    code=AccountErrorCode.REQUIRED)
                })
        if AppointmentType.ONLINE in appointment_types and not online_visit_price:
            raise ValidationError(
                {"onlineVisitPrice": ValidationError("Online visit price must be provided",
                                                     code=AccountErrorCode.INVALID)
                 })

        # because of smc do not send price
        # if AppointmentType.ONSITE in appointment_types and not onsite_visit_price:
            # raise ValidationError(
            #     {"onsiteVisitPrice": ValidationError("Onsite visit price must be provided",
            #                                          code=AccountErrorCode.INVALID)
            #      })
        if AppointmentType.AT_HOME in appointment_types and not at_home_visit_price:
            raise ValidationError(
                {"atHomeVisitPrice": ValidationError("At home visit price must be provided",
                                                     code=AccountErrorCode.INVALID)
                 })

        if online_visit_price and not AppointmentType.ONLINE in appointment_types:
            raise ValidationError(
                {"appointmentTypes": ValidationError("Online appointment type must be provided",
                                                     code=AccountErrorCode.INVALID)
                 })
        if onsite_visit_price and not AppointmentType.ONSITE in appointment_types:
            raise ValidationError(
                {"appointmentTypes": ValidationError("Onsite appointment type must be provided",
                                                     code=AccountErrorCode.INVALID)
                 })
        if at_home_visit_price and not AppointmentType.AT_HOME in appointment_types:
            raise ValidationError(
                {"appointmentTypes": ValidationError("At home appointment type must be provided",
                                                     code=AccountErrorCode.INVALID)
                 })

        if online_visit_price and online_visit_price <= 0:
            raise ValidationError(
                {"onlineVisitPrice": ValidationError(
                    "Online visit price must be provided and must be greater than 0",
                    code=AccountErrorCode.INVALID)
                 })
        # because of smc do not send price
        # if onsite_visit_price and onsite_visit_price <= 0:
        #     raise ValidationError(
        #         {"onsiteVisitPrice": ValidationError(
        #             "Onsite visit price must be provided and must be greater than 0",
        #             code=AccountErrorCode.INVALID)
        #          })
        if at_home_visit_price and at_home_visit_price <= 0:
            raise ValidationError(
                {"atHomeVisitPrice": ValidationError(
                    "At home visit price must be provided and must be greater than 0",
                    code=AccountErrorCode.INVALID)
                 })
