import graphene
from django.contrib.sites.models import Site

from usermanagment.auth.permissions import SitePermissions
from usermanagment.graphql.core.mutations import BaseMutation
from usermanagment.graphql.core.types.common import UserManagementSiteSettingsError
from usermanagment.graphql.site.types import UserManagementSiteSettings


class UserManagementSiteSettingsInput(graphene.InputObjectType):
    range_expansion_max_number_of_rounds = graphene.Int(
        description="Range expansion max number of rounds."
    )
    range_expansion_max_number_of_tries = graphene.Int(
        description="Range expansion max number of tries."
    )
    range_expansion_time_out_period = graphene.Int(
        description="Range expansion time out period."
    )
    range_expansion_round_radius = graphene.Float(
        description="Range expansion round radius."
    )
    range_expansion_round_max_number_of_pharmacies = graphene.Int(
        description="Range expansion round max number of pharmacies."
    )
    range_expansion_round_pharmacies_types = graphene.List(
        graphene.String, description="Range expansion round pharmacies types."
    )
    microservice_context_path = graphene.String(
        description="Microservice context path."
    )
    registration_sms_message = graphene.String(description="Registration sms message.")
    registration_sms_message_ar = graphene.String(
        description="Registration sms message in Arabic."
    )
    password_reset_sms_message = graphene.String(description="Password reset message.")
    password_reset_sms_message_ar = graphene.String(
        description="Password reset sms message in Arabic."
    )
    patient_order_otp_sms_message = graphene.String(
        description="Patient order OTP sms message."
    )
    patient_order_otp_sms_message_ar = graphene.String(
        description="Patient order OTP sms message in Arabic."
    )
    terms_and_conditions_accepted_version = graphene.Float(
        description="Terms and conditions accepted version."
    )

    registration_email_message = graphene.String(description="Registration email message.")
    registration_email_message_ar = graphene.String(
        description="Registration email message in Arabic."
    )
    password_reset_email_message = graphene.String(description="Password reset message.")
    password_reset_email_message_ar = graphene.String(
        description="Password reset email message in Arabic."
    )
    patient_order_otp_email_message = graphene.String(
        description="Patient order OTP email message."
    )
    patient_order_otp_email_message_ar = graphene.String(
        description="Patient order OTP email message in Arabic."
    )
    patient_create_sms_message = graphene.String(
        description="Patient create sms message."
    )
    patient_create_sms_message_ar = graphene.String(
        description="Patient create sms message in Arabic."
    )
    patient_create_email_message = graphene.String(
        description="Patient create email message."
    )
    patient_create_email_message_ar = graphene.String(
        description="Patient create email message in Arabic."
    )
    delete_account_sms_message = graphene.String(
        description="Delete account sms message."
    )
    delete_account_sms_message_ar = graphene.String(
        description="Delete account sms message in Arabic."
    )
    delete_account_email_message = graphene.String(
        description="Delete account email message."
    )
    delete_account_email_message_ar = graphene.String(
        description="Delete account email message in Arabic."
    )

class UserManagementSiteSettingsUpdate(BaseMutation):
    user_management_site_settings = graphene.Field(UserManagementSiteSettings,
                                                   description="Updated site settings.")

    class Arguments:
        input = UserManagementSiteSettingsInput(
            description="Fields required to update site settings.", required=True
        )

    class Meta:
        description = "Updates user management site settings."
        permissions = (SitePermissions.MANAGE_SETTINGS,)
        error_type_class = UserManagementSiteSettingsError
        error_type_field = "site_settings_errors"

    @classmethod
    def construct_instance(cls, instance, cleaned_data):
        for field_name, desired_value in cleaned_data.items():
            current_value = getattr(instance, field_name)
            if current_value != desired_value:
                setattr(instance, field_name, desired_value)
        return instance

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        instance = Site.objects.get_current().settings
        data = data.get("input")
        instance = cls.construct_instance(instance, data)
        cls.clean_instance(info, instance)
        instance.save()

        return UserManagementSiteSettingsUpdate(user_management_site_settings=instance)
