import graphene

from usermanagment.graphql.site.mutations import UserManagementSiteSettingsUpdate
from usermanagment.graphql.site.types import UserManagementSiteSettings, FeatureFlag


class UserManagementSiteSettingsQueries(graphene.ObjectType):
    user_management_site_settings = graphene.Field(
        UserManagementSiteSettings,
        description="Return information about the UserManagementSiteSettings.",
    )
    feature_flag = graphene.Field(
        FeatureFlag, description="Return information about the FeatureFlag.",
    )

    @staticmethod
    def resolve_user_management_site_settings(_root, _info):
        return UserManagementSiteSettings()

    @staticmethod
    def resolve_feature_flag(_root, _info):
        return FeatureFlag()

class UserManagementSiteSettingsMutations(graphene.ObjectType):
    user_management_site_settings_update = UserManagementSiteSettingsUpdate.Field()
