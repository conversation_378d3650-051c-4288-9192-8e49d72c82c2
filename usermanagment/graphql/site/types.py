import graphene
from graphene import relay
from graphene_federation import key
from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.site import models
from usermanagment.settings import get_bool_from_env
from usermanagment.auth.features import Feature
from graphene import Node

@key(fields='id')
class FeatureFlag(graphene.ObjectType):
    id = graphene.ID(description="ID of the feature flag.", required=True)
    customer_registration_feature_enabled = graphene.Boolean(
        description="Customer registration feature flag.",
        required=True,
    )
    delete_customer_account_feature_enabled = graphene.Boolean(
        description="Delete customer account feature flag.",
        required=True,
    )
    dependent_creation_feature_enabled = graphene.Boolean(
        description="Dependent creation feature flag.",
        required=True,
    )

    add_national_id_feature_enabled = graphene.Boolean(
        description="Add national id feature flag.",
        required=True,
    )

    two_factor_authentication_feature_enabled = graphene.Boolean(
        description="Two factor authentication feature flag.",
        required=True,
    )

    biometric_login_feature_enabled = graphene.Boolean(
        description="Biometric login feature flag.",
        required=True,
    )

    @staticmethod
    def resolve_id(_, info):
        return Node.to_global_id("FeatureFlag", 1)

    @staticmethod
    def resolve_customer_registration_feature_enabled(_, info):
        env_variable_name = "_".join([Feature.CUSTOMER_REGISTRATION.name, "FEATURE_ENABLED"])
        return get_bool_from_env(env_variable_name, False)


    @staticmethod
    def resolve_delete_customer_account_feature_enabled(_, info):
        env_variable_name = "_".join([Feature.DELETE_CUSTOMER_ACCOUNT.name, "FEATURE_ENABLED"])
        return get_bool_from_env(env_variable_name, False)

    @staticmethod
    def resolve_dependent_creation_feature_enabled(_, info):
        env_variable_name = "_".join([Feature.DEPENDENT_CREATION.name, "FEATURE_ENABLED"])
        return get_bool_from_env(env_variable_name, False)

    @staticmethod
    def resolve_add_national_id_feature_enabled(_, info):
        env_variable_name = "_".join([Feature.ADD_NATIONAL_ID.name, "FEATURE_ENABLED"])
        return get_bool_from_env(env_variable_name, False)

    @staticmethod
    def resolve_two_factor_authentication_feature_enabled(_, info):
        env_variable_name = "_".join(
            [Feature.TWO_FACTOR_AUTHENTICATION.name, "FEATURE_ENABLED"])
        return get_bool_from_env(env_variable_name, False)

    @staticmethod
    def resolve_biometric_login_feature_enabled(_, info):
        env_variable_name = "_".join(
            [Feature.BIOMETRIC_LOGIN.name, "FEATURE_ENABLED"])
        return get_bool_from_env(env_variable_name, False)


class UserManagementSiteSettings(CountableDjangoObjectType):
    range_expansion_max_number_of_rounds = graphene.Int(
        description="Range expansion max number of rounds."
    )
    range_expansion_max_number_of_tries = graphene.Int(
        description="Range expansion max number of tries."
    )
    range_expansion_time_out_period = graphene.Int(
        description="Range expansion time out period."
    )
    range_expansion_round_radius = graphene.Float(
        description="Range expansion round radius."
    )
    range_expansion_round_max_number_of_pharmacies = graphene.Int(
        description="Range expansion round max number of pharmacies."
    )
    range_expansion_round_pharmacies_types = graphene.List(
        graphene.String, description="Range expansion round pharmacies types."
    )
    microservice_context_path = graphene.String(
        description="Microservice context path."
    )
    registration_sms_message = graphene.String(description="Registration sms message.")
    registration_sms_message_ar = graphene.String(
        description="Registration sms message in Arabic."
    )
    password_reset_sms_message = graphene.String(description="Password reset message.")
    password_reset_sms_message_ar = graphene.String(
        description="Password reset sms message in Arabic."
    )
    patient_order_otp_sms_message = graphene.String(
        description="Patient order OTP sms message."
    )
    patient_order_otp_sms_message_ar = graphene.String(
        description="Patient order OTP sms message in Arabic."
    )

    registration_email_message = graphene.String(
        description="Registration email message.")
    registration_email_message_ar = graphene.String(
        description="Registration email message in Arabic."
    )
    password_reset_email_message = graphene.String(
        description="Password reset message.")
    password_reset_email_message_ar = graphene.String(
        description="Password reset email message in Arabic."
    )
    patient_order_otp_email_message = graphene.String(
        description="Patient order OTP email message."
    )
    patient_order_otp_email_message_ar = graphene.String(
        description="Patient order OTP email message in Arabic."
    )
    terms_and_conditions_accepted_version = graphene.Float(
        description="Terms and conditions accepted version."
    )
    patient_create_email_message = graphene.String(
        description="Patient create email message."
    )
    patient_create_email_message_ar = graphene.String(
        description="Patient create email message in Arabic."
    )
    patient_create_sms_message = graphene.String(
        description="Patient create sms message.")
    patient_create_sms_message_ar = graphene.String(
        description="Patient create sms message in Arabic."
    )
    delete_account_email_message = graphene.String(
        description="Delete account email message.")
    delete_account_email_message_ar = graphene.String(
        description="Delete account email message in Arabic."
    )
    delete_account_sms_message = graphene.String(
        description="Delete account sms message.")
    delete_account_sms_message_ar = graphene.String(
        description="Delete account sms message in Arabic."
    )

    class Meta:
        description = "Site settings data."
        model = models.SiteSettings
        interfaces = [relay.Node]

    @staticmethod
    def resolve_range_expansion_max_number_of_rounds(_, info):
        return info.context.site.settings.range_expansion_max_number_of_rounds

    @staticmethod
    def resolve_range_expansion_max_number_of_tries(_, info):
        return info.context.site.settings.range_expansion_max_number_of_tries

    @staticmethod
    def resolve_range_expansion_time_out_period(_, info):
        return info.context.site.settings.range_expansion_time_out_period

    @staticmethod
    def resolve_range_expansion_round_radius(_, info):
        return info.context.site.settings.range_expansion_round_radius

    @staticmethod
    def resolve_range_expansion_round_max_number_of_pharmacies(_, info):
        return info.context.site.settings.range_expansion_round_max_number_of_pharmacies

    @staticmethod
    def resolve_range_expansion_round_pharmacies_types(_, info):
        return info.context.site.settings.range_expansion_round_pharmacies_types

    @staticmethod
    def resolve_microservice_context_path(_, info):
        return info.context.site.settings.microservice_context_path

    @staticmethod
    def resolve_registration_sms_message(_, info):
        return info.context.site.settings.registration_sms_message

    @staticmethod
    def resolve_registration_sms_message_ar(_, info):
        return info.context.site.settings.registration_sms_message_ar

    @staticmethod
    def resolve_password_reset_sms_message(_, info):
        return info.context.site.settings.password_reset_sms_message

    @staticmethod
    def resolve_password_reset_sms_message_ar(_, info):
        return info.context.site.settings.password_reset_sms_message_ar

    @staticmethod
    def resolve_patient_order_otp_sms_message(_, info):
        return info.context.site.settings.patient_order_otp_sms_message

    @staticmethod
    def resolve_patient_order_otp_sms_message_ar(_, info):
        return info.context.site.settings.patient_order_otp_sms_message_ar

    @staticmethod
    def resolve_registration_email_message(_, info):
        return info.context.site.settings.registration_email_message

    @staticmethod
    def resolve_registration_email_message_ar(_, info):
        return info.context.site.settings.registration_email_message_ar

    @staticmethod
    def resolve_password_reset_email_message(_, info):
        return info.context.site.settings.password_reset_email_message

    @staticmethod
    def resolve_password_reset_email_message_ar(_, info):
        return info.context.site.settings.password_reset_email_message_ar

    @staticmethod
    def resolve_patient_order_otp_email_message(_, info):
        return info.context.site.settings.patient_order_otp_email_message

    @staticmethod
    def resolve_patient_order_otp_email_message_ar(_, info):
        return info.context.site.settings.patient_order_otp_email_message_ar

    @staticmethod
    def resolve_patient_create_email_message(_, info):
        return info.context.site.settings.patient_create_email_message

    @staticmethod
    def resolve_patient_create_email_message_ar(_, info):
        return info.context.site.settings.patient_create_email_message_ar

    @staticmethod
    def resolve_patient_create_sms_message(_, info):
        return info.context.site.settings.patient_create_sms_message

    @staticmethod
    def resolve_patient_create_sms_message_ar(_, info):
        return info.context.site.settings.patient_create_sms_message_ar

    @staticmethod
    def resolve_delete_account_email_message(_, info):
        return info.context.site.settings.delete_account_email_message

    @staticmethod
    def resolve_delete_account_email_message_ar(_, info):
        return info.context.site.settings.delete_account_email_message_ar

    @staticmethod
    def resolve_delete_account_sms_message(_, info):
        return info.context.site.settings.delete_account_sms_message

    @staticmethod
    def resolve_delete_account_sms_message_ar(_, info):
        return info.context.site.settings.delete_account_sms_message_ar

