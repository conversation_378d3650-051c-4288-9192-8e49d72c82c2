import graphene

from .filters import BranchFilterInput
from .sorters import <PERSON><PERSON><PERSON><PERSON>
from .types import Branch
from ..branch.mutations import Branch<PERSON><PERSON>, BranchUpdate, AssignUserToBranch, \
    UnAssignUserFromBranch, BranchDelete, AssignDefaultBranch, BranchActiveStatusUpdate
from ..core.fields import FilterInputCon<PERSON><PERSON><PERSON>ield
from ..core.types.common import LocationInput
from ...vendor import models


class BranchQueries(graphene.ObjectType):
    branch = graphene.Field(
        Branch,
        id=graphene.Argument(graphene.ID, description="ID of the Branch.",
                             required=True),
        description="Look up a Branch by ID.",
    )

    branches = FilterInputConnectionField(
        Branch,
        user_location=LocationInput(),
        filter=BranchFilterInput(description="Filtering options for branches."),
        sort_by=BranchOrder(description="Sort branches."),
        description="List of the shop's branches.",
    )

    @staticmethod
    def resolve_branch(self, info, id):
        return graphene.Node.get_node_from_global_id(info, id, Branch)

    @staticmethod
    def resolve_branches(self, info, **kwargs):
        return models.Branch.objects.accessible_branches()


class BranchMutations(graphene.ObjectType):
    branch_create = BranchCreate.Field()
    branch_update = BranchUpdate.Field()
    branch_delete = BranchDelete.Field()
    branch_active_status_update = BranchActiveStatusUpdate.Field()

    assign_user_to_branch = AssignUserToBranch.Field()
    un_assign_user_from_branch = UnAssignUserFromBranch.Field()
    assign_default_branch = AssignDefaultBranch.Field()
