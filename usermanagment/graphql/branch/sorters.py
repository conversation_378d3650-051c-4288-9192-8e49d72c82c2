import graphene
from django.contrib.gis.geos import Point
from django.core.exceptions import ValidationError
from django.db.models import QuerySet, ExpressionWrapper, DecimalField
from django.db.models.aggregates import Max

from ..core.types import SortInputObjectType
from ..utils.request_utils import get_current_user
from ...map.postgis.functions import DistanceSphere


class BranchOrderField(graphene.Enum):
    NAME = ["name"]
    CREATED = ["created", "pk"]
    LAST_MESSAGE_SENT = ['last_message_created', "pk"]
    NEAREST = ["distance", "pk"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            BranchOrderField.NAME.name: "name",
            BranchOrderField.CREATED.name: "creation date",
            BranchOrderField.LAST_MESSAGE_SENT.name: "last message creation date",
            BranchOrderField.NEAREST.name: "nearest to user location",
        }
        if self.name in descriptions:
            return f"Sort branches by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.value)

    @staticmethod
    def qs_with_last_message_sent(queryset: QuerySet, args: dict) -> QuerySet:
        return queryset.annotate(last_message_created=Max('messages__created'))

    @staticmethod
    def qs_with_nearest(queryset: QuerySet, args: dict) -> QuerySet:
        value = args.get('user_location', {})
        try:
            user_location = Point(x=value.lng, y=value.lat, srid=4326)
        except Exception:
            current_user = get_current_user()
            if current_user.is_authenticated and current_user.default_shipping_address:
                user_location = current_user.default_shipping_address.location
            else:
                raise ValidationError({
                    "nearest": "please provide a valid user location"
                })

        return queryset.annotate(
            distance=ExpressionWrapper(DistanceSphere(
                "address__location",
                user_location,
            ), output_field=DecimalField()))


class BranchOrder(SortInputObjectType):
    field = graphene.Argument(
        BranchOrderField, description="Sort branches by the selected field."
    )

    class Meta:
        sort_enum = BranchOrderField
