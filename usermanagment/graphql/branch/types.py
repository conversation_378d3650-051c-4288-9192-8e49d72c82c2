import graphene
from django.db.models import Q
from graphene import relay
from graphene_federation import key
from ..core.connection import CountableDjangoObjectType
from ..core.fields import FilterInputConnectionField
from ..core.types.common import WorkingHour, WorkingHourOverride
from ..core.types.translations import BaseTranslationType
from ..utils import get_database_id
from ..utils.request_utils import get_current_user
from ...auth.decorators import permission_required, one_of_permissions_required
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import AccountPermissions
from ...vendor import models


@key(fields="id")
class Branch(CountableDjangoObjectType):
    working_hours = graphene.List(
        WorkingHour, description="list of branch working hours")

    working_hours_override = graphene.List(
        WorkingHourOverride, description="list of branch working hours override")

    is_open = graphene.Boolean(description="is branch Open now")

    users = graphene.List("usermanagment.graphql.account.types.User",
                          description="list of vendor branch user accounts")

    messages = FilterInputConnectionField(
        "usermanagment.graphql.chat.types.Message",
        customer_id=graphene.Argument(
            graphene.ID,
            description=(
                "messages with specific customer"
            ),
            required=True
        )
    )

    health_license = graphene.Field(
        graphene.String,
        description="Health License Number"
    )

    preferred_pharmacies = graphene.List(
        "usermanagment.graphql.vendor.types.Branch",
        description="Preferred Pharmacies"
    )

    class Meta:
        description = "Represents vendor-branch in the storefront."
        model = models.Branch
        interfaces = [relay.Node]
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
            "health_license_number",
        ]

    @classmethod
    def get_node(cls, info, pk):
        if info.context:
            qs = models.Branch.objects.accessible_branches().filter(pk=pk).select_related("vendor")
            return qs.first()
        return None

    @classmethod
    def resolve_preferred_pharmacies(cls, root, _info, **_kwargs):
        return root.preferred_pharmacies.all()

    @staticmethod
    def resolve_is_open(root: models.Branch, _info, **_kwargs):
        return root.is_open()

    @staticmethod
    def resolve_working_hours(root: models.Branch, _info, **_kwargs):
        db_branch_working_hours = root.working_hours.all()
        working_hours = {}
        for db_branch_working_hour in db_branch_working_hours:
            time_range = {
                "open_time": db_branch_working_hour.open_time,
                "close_time": db_branch_working_hour.close_time
            }
            if db_branch_working_hour.day in working_hours:
                working_hours[db_branch_working_hour.day].append(time_range)
            else:
                working_hours[db_branch_working_hour.day] = [time_range]

        return [WorkingHour(day, open_time_ranges) for day, open_time_ranges
                in working_hours.items()]

    @staticmethod
    def resolve_working_hours_override(root: models.Branch, _info, **_kwargs):
        db_branch_working_hours_override = root.working_hours_override.all()
        working_hours_override = {}
        for db_branch_working_hour_override in db_branch_working_hours_override:
            time_range = {
                "open_time": db_branch_working_hour_override.open_time,
                "close_time": db_branch_working_hour_override.close_time
            }
            if db_branch_working_hour_override.date in working_hours_override:
                working_hours_override[db_branch_working_hour_override.date].append(
                    time_range)
            else:
                working_hours_override[db_branch_working_hour_override.date] = [
                    time_range]

        return [WorkingHourOverride(date, open_time_ranges) for
                date, open_time_ranges
                in working_hours_override.items()]

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.VIEW_USERS])
    def resolve_users(root: models.Branch, info, **kwargs):
        return root.users.all()

    @staticmethod
    def resolve_messages(root: models.Branch, info, customer_id, **kwargs):
        current_user = info.context.user
        customer_pk = get_database_id(customer_id, "User")
        if not customer_pk:
            return []
        if current_user.vendor_id != root.vendor_id and customer_pk != str(
                current_user.pk):
            raise PermissionDenied()

        return root.messages.filter(Q(sender=customer_pk) | Q(recipient=customer_pk))

    @staticmethod
    def resolve_health_license(root, _info, **_kwargs):
        return root.health_license_number

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Branch)


@key(fields="id")
class BranchUser(CountableDjangoObjectType):
    class Meta:
        description = "Represents branch User "
        model = models.BranchUser
