import django_filters
from django.contrib.gis.geos import Point
from django.db.models import Q
from graphene_django.filter import GlobalIDMultipleChoiceFilter

from ..core.filters import ListObjectTypeFilter, ObjectTypeFilter, \
    filter_entities_by_name__icontains, filter_entities_by_name, \
    filter_branch_global_search
from ..core.types import FilterInputObjectType
from ..core.types.common import DistanceFilterInput
from ..utils import resolve_global_ids_to_primary_keys
from ..utils.request_utils import get_current_user
from ..vendor.enums import VendorBranchTypeEnum
from ...map.postgis.functions import DistanceSphere
from ...vendor.models import Branch


def filter_by_vendors(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "Vendor")
        qs = qs.filter(vendor_id__in=pks)
    return qs


def filter_by_ids(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "Branch")
        qs = qs.filter(id__in=pks)
    return qs

def filter_by_has_chat_with(qs, _, value):
    current_user = get_current_user()
    if value and current_user.is_authenticated:
        qs = qs.filter(Q(messages__sender=current_user) |
                       Q(messages__recipient=current_user)).distinct()

    return qs


def filter_by_is_active(qs, _, value):
    if value:
        qs = qs.filter(is_active=value, vendor__is_active=value)
    else:
        qs = qs.filter(is_active=value)
    return qs


def filter_by_is_active_branch(qs, _, value):
    qs = qs.filter(is_active=value)
    return qs


def filter_by_accepts_delivery(qs, _, value):
    qs = qs.filter(accepts_delivery=value)
    return qs


def filter_by_accepts_pickup(qs, _, value):
    qs = qs.filter(accepts_pickup=value)
    return qs


def filter_by_is_integrated(qs, _, value):
    qs = qs.filter(vendor__is_integrated=value)
    return qs


def filter_by_enable_visit_details(qs, _, value):
    qs = qs.filter(vendor__enable_visit_details=value)
    return qs


def filter_by_health_license_number(qs, _, value):
    qs = qs.filter(health_license_number=value)
    return qs


def filter_branch_by_type(qs, _, value):
    if value:
        qs = qs.filter(type__in=value)

    return qs


def filter_by_location(qs, _, value):
    if value:
        location = Point(x=value.coordinates.lng, y=value.coordinates.lat, srid=4326)
        qs = qs.annotate(distance=DistanceSphere('address__location', location)) \
            .filter(is_active=True,
                    vendor__is_active=True,
                    ) \
            .order_by('distance')

        if value.distance:
            qs = qs.filter(distance__lte=value.distance)
    return qs


class BranchFilter(django_filters.FilterSet):
    ids = GlobalIDMultipleChoiceFilter(method=filter_by_ids)

    vendors = GlobalIDMultipleChoiceFilter(method=filter_by_vendors)

    has_chat_with = django_filters.BooleanFilter(method=filter_by_has_chat_with, )

    type = ListObjectTypeFilter(
        input_class=VendorBranchTypeEnum, method=filter_branch_by_type
    )

    location = ObjectTypeFilter(
        input_class=DistanceFilterInput,
        method=filter_by_location,
        field_name="address",
    )
    is_active = django_filters.BooleanFilter(method=filter_by_is_active, )

    accepts_delivery = django_filters.BooleanFilter(method=filter_by_accepts_delivery, )

    accepts_pickup = django_filters.BooleanFilter(method=filter_by_accepts_pickup, )

    is_active_branch = django_filters.BooleanFilter(method=filter_by_is_active_branch, )

    is_integrated = django_filters.BooleanFilter(method=filter_by_is_integrated, )

    enable_visit_details = django_filters.BooleanFilter(method=filter_by_is_integrated, )

    health_license_number = django_filters.CharFilter(method=
                                                      filter_by_health_license_number, )

    name__icontains = django_filters.CharFilter(
        method=filter_entities_by_name__icontains)
    name = django_filters.CharFilter(method=filter_entities_by_name)

    branch_global_search = django_filters.CharFilter(method=filter_branch_global_search)

    class Meta:
        model = Branch
        fields = {
            "description": ["exact", "icontains"],
            "contact_number": ["exact", "icontains"],
        }


class BranchFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = BranchFilter
