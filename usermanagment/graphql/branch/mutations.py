import graphene
from django.core.exceptions import ValidationError
from django.db import transaction

from .types import Branch
from ..account.i18n import I18nMixin
from ..account.types import User
from ..core.health_license_date import HealthLicenseDatesMixin
from ..core.mutations import ModelMutation, ModelDeleteMutation
from ..core.types.common import BranchError, WorkingHourInput, \
    WorkingHourOverrideInput
from ..core.utils import clean_seo_fields
from ..vendor.enums import VendorBranchTypeEnum
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import BranchPermissions
from ...authorization import authorization
from ...authorization.authorization_mixins import SuperuserOrVendorAdminMixin
from usermanagment.graphql.core.working_hours import WorkingHoursMixin
from ...graphql.account.types import AddressInput
from ...vendor import models
from ...vendor.enums import ALLOWED_VENDOR_BRANCH_TYPES, VendorBranchTypes


class BranchInput(graphene.InputObjectType):
    name = graphene.String(description="Branch Name.")
    name_ar = graphene.String(description="Branch Name Arabic.")
    description = graphene.String(description="Branch description")
    working_hours = graphene.List(
        WorkingHourInput,
        description="Branch Working Hours")
    working_hours_override = graphene.List(
        WorkingHourOverrideInput,
        description="Branch Working Hours override")
    address_input = AddressInput(description="Address of the branch.")
    is_active = graphene.Boolean(
        description="Determine if users will be set active or not."
    )
    contact_number = graphene.String(
        description="Branch contact number that displayed to customers.")
    contact_email = graphene.String(
        description="Branch contact email that displayed to customers.")
    type = VendorBranchTypeEnum(description="Branch Type")
    health_license_number = graphene.String(name="healthLicense",
                                            description="Health License Number")
    health_license_start_date = graphene.Date()
    health_license_end_date = graphene.Date()
    is_trained = graphene.Boolean()
    accepts_delivery = graphene.Boolean(description="Accepts Delivery")
    accepts_pickup = graphene.Boolean(description="Accepts Pickup")
    preferred_pharmacies = graphene.List(
        graphene.ID,
        description="Preferred Pharmacies"
    )


class BranchCreate(SuperuserOrVendorAdminMixin, ModelMutation, I18nMixin,
                   WorkingHoursMixin, HealthLicenseDatesMixin):
    class Arguments:
        vendor_id = graphene.ID(
            description="ID of a vendor to create branch for."
        )
        input = BranchInput(
            required=True, description="Fields required to create a Branch."
        )

    class Meta:
        description = "Creates a new branch."
        model = models.Branch
        permissions = (BranchPermissions.MANAGE_BRANCHES,)
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def clean_input(cls, info, instance: models.Branch, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data)
        clean_seo_fields(cleaned_input)

        branch_type = cleaned_input.get('type', instance.type)

        preferred_pharmacies = cleaned_input.get("preferred_pharmacies",None)

        if preferred_pharmacies:
            if len(preferred_pharmacies) > 10:
                raise ValidationError('Preferred pharmacies can not be more than 10')
            for preferred_pharmacy in preferred_pharmacies:
                if preferred_pharmacy.type != VendorBranchTypes.PHARMACY:
                    raise ValidationError(
                        'Preferred pharmacies can only be set for pharmacy branches')
        if branch_type == VendorBranchTypes.PHARMACY and preferred_pharmacies:
            raise ValidationError(
                'Preferred pharmacies can only be set for non-pharmacy branches')

        if branch_type is None:
            raise ValidationError('Branch type is mandatory')



        cls.validate_working_hours(info, data)
        cls.validate_working_hours_override(info, data)

        address_data = data.pop("address_input", None)
        if address_data:
            cleaned_input["address"] = cls.construct_address_instance(info,
                                                                      address_data,
                                                                      instance)
        cls.validate_health_licence_dates(info, instance, cleaned_input)
        return cleaned_input

    @classmethod
    def clean_instance(cls, info, instance):
        if not instance.pk and not instance.vendor.has_multiple_branches:
            raise ValidationError({
                "vendor": "you can not create more than one branch for this vendor"
            })

        if instance.type not in ALLOWED_VENDOR_BRANCH_TYPES[instance.vendor.type]:
            raise ValidationError(
                {"type": "Branch type is not allowed for this vendor type."}
            )

        return super().clean_instance(info, instance)

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        res = super().perform_mutation(_root, info, **data)
        for preferred_pharmacy in res.branch.preferred_pharmacies.all():
            if preferred_pharmacy.vendor_id != res.branch.vendor_id:
                raise ValidationError(
                    'Preferred pharmacy is not part of the same vendor')
        return res

    @classmethod
    def _save_m2m(cls, info, instance, cleaned_data):
        super()._save_m2m(info, instance, cleaned_data)
        cls.save_working_hours(info, instance, cleaned_data, models.BranchWorkingHour,
                               'branch_id')
        cls.save_working_hours_override(info, instance, cleaned_data,
                                        models.BranchWorkingHourOverride, 'branch_id')


class BranchUpdate(BranchCreate):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a branch to update.")
        input = BranchInput(required=True,
                            description="Fields required to update a branch")

    class Meta:
        description = "Updates an existing branch."
        model = models.Branch
        permissions = (BranchPermissions.MANAGE_BRANCHES,)
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not authorization.can_manage_branch(info.context.user, data["id"]):
            raise PermissionDenied()

        return {}

class BranchActiveStatusUpdate(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a branch to update.")

    class Meta:
        description = "Updates an existing branch."
        model = models.Branch
        permissions = (BranchPermissions.MANAGE_BRANCHES,)
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not authorization.can_manage_branch(info.context.user, data["id"]):
            raise PermissionDenied()

        return {}

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        instance = cls.get_node_or_error(info, data["id"], models.Branch)
        cls.check_authorization(_root, info, instance, **data)
        instance.is_active = not instance.is_active
        instance.save(update_fields=["is_active"])
        return cls.success_response(instance)


class UserBranchInput(graphene.InputObjectType):
    user = graphene.ID(description="user id to assign to branch")
    branch = graphene.ID(description="branch id to assign user")


def check_assign_user_to_branch_authorization(cls, root, info, instance, **data):
    current_user = info.context.user
    assigned_user = cls.get_node_or_error(info, data["input"]["user"], User)

    if not (authorization.can_manage_branch(user=current_user,
                                            branch_id=data["input"]["branch"]) \
            and assigned_user.vendor_id == current_user.vendor_id):
        raise PermissionDenied()


class AssignUserToBranch(ModelMutation):
    class Arguments:
        input = UserBranchInput(
            description="Fields required to assign user to branch.")

    class Meta:
        description = "assign user to branch"
        model = models.BranchUser
        permissions = (BranchPermissions.MANAGE_BRANCHES,)
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        check_assign_user_to_branch_authorization(cls, root, info, instance, **data)


class UnAssignUserFromBranch(ModelDeleteMutation):
    class Arguments:
        input = UserBranchInput(
            description="Fields required to assign user to branch.")

    class Meta:
        description = "un assign user from branch"
        model = models.BranchUser
        permissions = (BranchPermissions.MANAGE_BRANCHES,)
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        check_assign_user_to_branch_authorization(cls, root, info, instance, **data)

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        if not cls.check_permissions(info.context):
            raise PermissionDenied()

        assigned_user = cls.get_node_or_error(info, data["input"]["user"], User)
        assigned_branch = cls.get_node_or_error(info, data["input"]["branch"], Branch)
        instance = models.BranchUser.objects.filter(user=assigned_user,
                                                    branch=assigned_branch).first()

        cls.check_authorization(_root, info, instance, **data)

        if instance:
            cls.clean_instance(info, instance)

        db_id = instance.id
        instance.delete()

        # After the instance is deleted, set its ID to the original database's
        # ID so that the success response contains ID of the deleted object.
        instance.id = db_id
        return cls.success_response(instance)


class BranchDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(description="ID of branch to delete.", required=True)

    class Meta:
        description = "deletes an existing vendor-branch."
        model = models.Branch
        permissions = (BranchPermissions.MANAGE_BRANCHES,)
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        raise ValidationError("Mutation is deprecated do not call")
        # if not info.context.user.is_superuser:
        #     raise PermissionDenied()

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)

        # branch = result.branch
        # for user in branch.users.all():
        #     user.delete()

        return result


# mutation to assign a default branch to a vendor user
class AssignDefaultBranch(ModelMutation):
    class Arguments:
        branch_id = graphene.ID(description="ID of branch to assign as default.",
                                required=True)

    class Meta:
        description = "assigns a default branch to a vendor user"
        model = models.Branch
        error_type_class = BranchError
        error_type_field = "branch_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data):

        current_user = info.context.user
        if not current_user.is_vendor:
            raise PermissionDenied()

        _type, branch_pk = graphene.Node.from_global_id(data["branch_id"])
        branch = models.Branch.objects.filter(id=branch_pk).first()
        if not branch:
            raise ValidationError("Branch not found")

        if current_user.is_vendor_admin and current_user.vendor_id != branch.vendor_id:
            raise PermissionDenied()

        if current_user.is_vendor_staff and branch not in current_user.branches.all():
            raise PermissionDenied()

        return {"branch": branch}

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        auth_data = cls.check_authorization(_root, info, None, **data)
        branch = auth_data["branch"]
        user = info.context.user
        user.default_branch = branch
        user.save()
        return cls.success_response(branch)
