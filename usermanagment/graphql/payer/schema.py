import graphene

from usermanagment.graphql.core.fields import FilterInputConnectionField
from usermanagment.graphql.payer.filters import PayerFilterInput
from usermanagment.graphql.payer.mutations import PayerCreate, PayerUpdate, \
    PayerActiveStatusUpdate
from usermanagment.graphql.payer.resolvers import resolve_payers
from usermanagment.graphql.payer.sorters import PayerOrder
from usermanagment.graphql.payer.types import Payer


class PayerQueries(graphene.ObjectType):
    payer = graphene.Field(
        Payer,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the payer.",
            required=True
        ),
        description="Look up a payer by ID.",
    )

    payers = FilterInputConnectionField(
        Payer,
        filter=PayerFilterInput(description="Filtering options for payers."),
        sort_by=PayerOrder(description="Sort payers."),
        description="List of the payers.",
    )

    @staticmethod
    def resolve_payer(_root, info, **kwargs):
        return resolve_payers(info, **kwargs)

    @staticmethod
    def resolve_payers(_root, info, **kwargs):
        return resolve_payers(info, **kwargs)


class PayerMutations(graphene.ObjectType):
    payer_create = PayerCreate.Field()
    payer_update = PayerUpdate.Field()
    payer_active_status_update = PayerActiveStatusUpdate.Field()
