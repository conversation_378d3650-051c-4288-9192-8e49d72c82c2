import django_filters

from usermanagment.graphql.core.filters import filter_entities_by_name__icontains, \
    filter_entities_by_name
from usermanagment.graphql.core.types import FilterInputObjectType
from usermanagment.payer import models


def filter_by_is_active(qs, _, value):
    return qs.filter(is_active=value)

class PayerFilter(django_filters.FilterSet):
    name__icontains = django_filters.CharFilter(
        method=filter_entities_by_name__icontains)
    name = django_filters.CharFilter(method=filter_entities_by_name)
    is_active = django_filters.BooleanFilter(method=filter_by_is_active)

    class Meta:
        model = models.Payer
        fields = {
            "license_number": ["exact"],
        }



class PayerFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = PayerFilter
