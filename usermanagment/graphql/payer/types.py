import graphene

from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.payer import models
from graphene_federation import key


@key(fields="id")
class Payer(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Insurance"
        interfaces = [graphene.relay.Node]
        model = models.Payer
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Payer)
