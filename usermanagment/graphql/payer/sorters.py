import graphene

from usermanagment.graphql.core.types import SortInputObjectType


class PayerOrderField(graphene.Enum):
    NAME = ["name", "pk"]
    NAME_AR = ["name_ar", "pk"]
    CREATED = ["created", "pk"]
    TPO = ["tpo", "pk"]

    @property
    def description(self):
        descriptions = {
            PayerOrderField.NAME.name: "name",
            PayerOrderField.NAME_AR.name: "Arabic name",
            PayerOrderField.CREATED.name: "creation date",
            PayerOrderField.TPO.name: "TPO",
        }
        if self.name in descriptions:
            return f"Sort vendor departments by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.name)


class PayerOrder(SortInputObjectType):
    field = graphene.Argument(
        PayerOrderField, description="Sort vendors by the selected field."
    )

    class Meta:
        sort_enum = PayerOrderField
