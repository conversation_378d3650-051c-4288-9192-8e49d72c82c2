import graphene

from usermanagment.auth.exceptions import PermissionDenied
from usermanagment.graphql.payer.types import Payer
from usermanagment.graphql.utils.request_utils import get_current_user
from usermanagment.payer import models


def resolve_payers(info, **kwargs):
    current_user = get_current_user()
    if not current_user.is_authenticated:
        raise PermissionDenied()

    if "id" in kwargs:
        return graphene.Node.get_node_from_global_id(info, kwargs['id'], Payer)

    return models.Payer.objects.all()
