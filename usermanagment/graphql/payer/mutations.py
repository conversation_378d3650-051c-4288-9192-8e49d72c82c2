import graphene
from django.core.exceptions import ValidationError
from usermanagment.auth.permissions import PayerPermissions
from usermanagment.graphql.core.mutations import ModelMutation
from usermanagment.graphql.core.types.common import PayerError
from usermanagment.graphql.payer.enums import TPO, PayerTypeEnum
from usermanagment.payer import models


class PayerInput(graphene.InputObjectType):
    name = graphene.String(description="The name of the payer")
    name_ar = graphene.String(description="The arabic name of the payer")
    contact_name = graphene.String(description="The contact name of the payer")
    contact_phone_number = graphene.String(description="Phone number of the payer")
    contact_mobile_number = graphene.String(description="Mobile number of the payer")
    contact_email = graphene.String(description="email address of the payer.")
    is_active = graphene.Boolean(
        description="Determine if payer will be set active or not.")
    license_number = graphene.String()
    logo = graphene.String(description="Payer logo Url")
    background_image = graphene.String(description="Payer back ground image")
    tpo = TPO(description="TPO of the payer")
    type = PayerTypeEnum(description="Type of the payer")
    parent_id = graphene.ID(description="Parent payer ID")
    insurance_card_logo = graphene.String(description="Insurance card logo Url")


class PayerCreate(ModelMutation):
    class Arguments:
        input = PayerInput(required=True,
                           description="Fields required to create a Payer.")

    class Meta:
        description = "Creates a new payer."
        model = models.Payer
        permissions = (PayerPermissions.MANAGE_PAYERS,)
        error_type_class = PayerError
        error_type_field = "payer_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data)
        payer_type = cleaned_input.get("type", instance.type)
        parent = cleaned_input.get("parent_id", instance.parent)
        if payer_type == models.PayerType.INSURANCE and parent:
            raise ValidationError(
                {
                    "parent": ValidationError(
                        "Parent field is not allowed for INSURANCE type.", code="invalid"
                    )
                }
            )
        if payer_type == models.PayerType.TPA and not parent:
            raise ValidationError(
                {
                    "parent": ValidationError(
                        "This field is required for TPA type.", code="required"
                    )
                }
            )
        if parent:
            if parent.type != models.PayerType.INSURANCE:
                raise ValidationError(
                    {
                        "parent": ValidationError(
                            "Parent payer should be of type INSURER.", code="invalid"
                        )
                    }
                )
            cleaned_input["parent"] = parent
        return cleaned_input


class PayerUpdate(PayerCreate):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a payer to update.")
        input = PayerInput(required=True,
                           description="Fields required to update a payer.")

    class Meta:
        description = "Creates a new payer."
        model = models.Payer
        permissions = (PayerPermissions.MANAGE_PAYERS,)
        error_type_class = PayerError
        error_type_field = "payer_errors"


class PayerActiveStatusUpdate(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a payer to update.")

    class Meta:
        description = "Update the status of a payer."
        model = models.Payer
        permissions = (PayerPermissions.MANAGE_PAYERS,)
        error_type_class = PayerError
        error_type_field = "payer_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data)
        instance.is_active = not instance.is_active
        return cleaned_input
