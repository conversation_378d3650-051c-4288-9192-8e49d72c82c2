import graphene

from usermanagment.complaint.enums import InsuranceSector, ComplaintType
from usermanagment.graphql.core.utils import str_to_enum

InsuranceSectorEnum = graphene.Enum(
    "InsuranceSectorEnum",
    [(str_to_enum(status[0]), status[0]) for status in InsuranceSector.choices],
)

ComplaintTypeEnum = graphene.Enum(
    "ComplaintTypeEnum",
    [(str_to_enum(status[0]), status[0]) for status in ComplaintType.choices],
)
