from graphene import List, String

from usermanagment.complaint import models
from usermanagment.graphql.core.connection import CountableDjangoObjectType


class Complaint(CountableDjangoObjectType):
    upload_files = List(String, description="List of uploaded files")

    class Meta:
        description = "Represents a complaint"
        model = models.Complaint
        fields = [
            "id",
            "insurance_sector",
            "complaint_type",
            "approval_number",
            "claim_number",
            "policy_number",
            "fact_of_complaint",
            "upload_files"
        ]

    def resolve_upload_files(self, info):
        return self.upload_files if self.upload_files else []
