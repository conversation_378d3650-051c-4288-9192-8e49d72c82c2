from usermanagment.auth.decorators import permission_required
from usermanagment.auth.permissions import ComplaintPermissions
from usermanagment.complaint import models
from usermanagment.graphql.complaint.filters import ComplaintFilterInput
from usermanagment.graphql.complaint.mutations import ComplaintCreate
from usermanagment.graphql.complaint.types import Complaint
from usermanagment.graphql.core.fields import FilterInputConnectionField


class ComplaintQueries:
    complaints = FilterInputConnectionField(
        Complaint,
        filter=ComplaintFilterInput(description="Filtering options for complaints."),
        description="List of complaints.",
    )

    @staticmethod
    @permission_required(ComplaintPermissions.VIEW_COMPLAINTS)
    def resolve_complaints(_root, info, **kwargs):
        return models.Complaint.objects.all()


class ComplaintMutations:
    complaint_create = ComplaintCreate.Field()
