import graphene
from django.db import transaction

from usermanagment import settings
from usermanagment.account.email import send_email
from usermanagment.auth.exceptions import PermissionDenied
from usermanagment.complaint import models
from usermanagment.graphql.complaint.enums import InsuranceSectorEnum, ComplaintTypeEnum
from usermanagment.graphql.core.mutations import ModelMutation
from usermanagment.graphql.core.types.common import PatientError
from usermanagment.graphql.utils.request_utils import get_current_user


class ComplaintInput(graphene.InputObjectType):
    insurance_sector = InsuranceSectorEnum(description="Insurance sector of the "
                                                       "complaint.", required=True)
    complaint_type = ComplaintTypeEnum(description="Complaint type.", required=True)
    approval_number = graphene.String(description="Approval number of the complaint.")
    claim_number = graphene.String(description="Claim number of the complaint.")
    policy_number = graphene.String(description="Policy number of the complaint.")
    fact_of_complaint = graphene.String(description="Fact of the complaint.",
                                        required=True)
    upload_files = graphene.List(graphene.String, description="List of uploaded files.")


class ComplaintCreate(ModelMutation):
    class Arguments:
        input = ComplaintInput(required=True,
                               description="Fields required to create a Complaint.")

    class Meta:
        description = "Creates a new complaint."
        model = models.Complaint
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_consumer:
            raise PermissionDenied()

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls=None)
        cleaned_data["user"] = get_current_user()
        return cleaned_data

    @classmethod
    def save(cls, info, instance, cleaned_input):
        super().save(info, instance, cleaned_input)

        current_user = get_current_user()

        email_body = "Dear Support team,\n\nWe have received the following complaint:\n\n"

        if cleaned_input.get('insurance_sector'):
            email_body += f"Insurance Sector: {cleaned_input.get('insurance_sector')}\n"
        if cleaned_input.get('complaint_type'):
            email_body += f"Complaint Type: {cleaned_input.get('complaint_type')}\n"
        if cleaned_input.get('approval_number'):
            email_body += f"Approval Number: {cleaned_input.get('approval_number')}\n"
        if cleaned_input.get('claim_number'):
            email_body += f"Claim Number: {cleaned_input.get('claim_number')}\n"
        if cleaned_input.get('policy_number'):
            email_body += f"Policy Number: {cleaned_input.get('policy_number')}\n"
        if cleaned_input.get('fact_of_complaint'):
            email_body += f"Fact of Complaint: {cleaned_input.get('fact_of_complaint')}\n"

        if cleaned_input.get('upload_files'):
            email_body += "Uploaded Files:\n"
            for file in cleaned_input.get('upload_files'):
                email_body += f" - {file}\n"

        email_body += "\n"
        if current_user.full_name:
            email_body += f"Patient Name: {current_user.full_name}\n"
        if current_user.email:
            email_body += f"Patient Email: {current_user.email}\n"
        if current_user.mobile:
            email_body += f"Patient Mobile: {current_user.mobile}\n"
        email_body += "\nThanks"

        send_email(
            to=settings.COMPLAINTS_RECEIVER_EMAIL,
            title="Complaint",
            body=email_body,
        )
