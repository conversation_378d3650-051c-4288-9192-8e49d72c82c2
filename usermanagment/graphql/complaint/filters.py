import django_filters

from usermanagment.graphql.complaint.enums import ComplaintType<PERSON>num, InsuranceSectorEnum
from usermanagment.graphql.core.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, ListObjectTypeFilter
from usermanagment.graphql.core.types import FilterInputObjectType


def filter_by_complaint_type(qs, _, value):
    if value:
        qs = qs.filter(complaint_type__in=value)
    return qs


def filter_by_insurance_sector(qs, _, value):
    if value:
        qs = qs.filter(insurance_sector__in=value)
    return qs


class ComplaintFilter(django_filters.FilterSet):
    insurance_sectors = ListObjectTypeFilter(
        input_class=InsuranceSectorEnum, method=filter_by_insurance_sector
    )
    complaint_types = ListObjectTypeFilter(
        input_class=ComplaintTypeEnum, method=filter_by_complaint_type
    )


class ComplaintFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = ComplaintFilter
