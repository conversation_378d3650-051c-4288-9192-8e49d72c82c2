from django.db.models import Avg, <PERSON><PERSON><PERSON><PERSON><PERSON>, Count
from django.db.models.functions import Cast

from usermanagment.feature.models import FeatureRating


def resolve_feature_average_rating(info, **kwargs):
    features = kwargs.get("features")

    average_ratings = FeatureRating.objects.filter(
        feature__in=features
    ).values('feature').annotate(
        average_rating=Cast(Avg('rating'),
                            DecimalField(max_digits=4, decimal_places=1)),
        total_ratings=Count('rating')
    )

    return average_ratings
