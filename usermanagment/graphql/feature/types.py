import graphene

from usermanagment.feature import models
from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.graphql.feature.enums import FeatureTypeEnum


class FeatureRating(CountableDjangoObjectType):
    class Meta:
        description = "Represents a rating for a feature"
        model = models.FeatureRating
        fields = [
            "id",
            "feature",
            "rating",
        ]


class FeatureAverageRating(graphene.ObjectType):
    class Meta:
        description = "Represents the average rating of a feature"

    feature = FeatureTypeEnum()
    average_rating = graphene.Float()
    total_ratings = graphene.Int()
