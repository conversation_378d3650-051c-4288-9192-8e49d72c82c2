import graphene
import requests
import logging

from django.db import transaction
from usermanagment import settings
from usermanagment.auth.exceptions import PermissionDenied
from usermanagment.feature import models
from usermanagment.feature.enums import FeatureType
from usermanagment.graphql.core.mutations import ModelMutation
from usermanagment.graphql.core.types.common import PatientError
from usermanagment.graphql.feature.enums import FeatureTypeEnum
from usermanagment.graphql.utils.request_utils import get_current_user
from usermanagment.graphql_client.backend_client import BackendClient
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)

class RateFeatureInput(graphene.InputObjectType):
    feature = FeatureTypeEnum(required=True)
    rating = graphene.Int(required=True)
    entity_id = graphene.Int(required=False)


class RateFeature(ModelMutation):
    class Arguments:
        input = RateFeatureInput(required=True)

    class Meta:
        description = "Rate a feature."
        model = models.FeatureRating
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_consumer:
            raise PermissionDenied()

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls=None)
        cleaned_data["user"] = get_current_user()
        return cleaned_data

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):

        flow_rating_features = [
            FeatureType.APPOINTMENT,
            FeatureType.CASH_CLAIM,
            FeatureType.APPROVAL,
            FeatureType.HOME_VACCINATION,
            FeatureType.TELEHEALTH_VISIT,
        ]

        if cleaned_input.get("feature") in flow_rating_features:
            cls.send_to_flow_rating(cleaned_input, info)

        super().save(info, instance, cleaned_input)

    @classmethod
    def send_to_flow_rating(cls, cleaned_input, info):
        authorization_header = info.context.META.get("HTTP_AUTHORIZATION")

        flow_rating_types = cleaned_input.get("feature")
        flow_rating_types = FeatureType(flow_rating_types).name

        res = BackendClient().get_national_id_and_payer_license_from_user(
            authorization_header)
        national_id = res.get("national_id")
        insurance_company_license_number = res.get("insurance_company_license_number")

        api_url = f"{settings.PAYER_INTEGRATION_API_URL}/api/flow-rating"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": authorization_header,
        }
        payload = {
            "payerLicense": insurance_company_license_number,
            "nationalId": national_id,
            "rateNo": cleaned_input.get("rating"),
            "entityId": cleaned_input.get("entity_id"),
            "type": flow_rating_types.upper()
        }

        response = requests.post(api_url, json=payload, headers=headers)

        if response.status_code != 200:
            logger.error(response.json())
            error_message = response.json().get("Message", "Already Submitted Before.")
            raise ValidationError(error_message)
