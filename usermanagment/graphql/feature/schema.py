import graphene

from usermanagment.auth.decorators import permission_required
from usermanagment.auth.permissions import FeatureRatingPermissions
from usermanagment.graphql.feature.enums import FeatureTypeEnum
from usermanagment.graphql.feature.mutations import RateFeature
from usermanagment.graphql.feature.resolvers import resolve_feature_average_rating
from usermanagment.graphql.feature.types import FeatureAverageRating


class FeatureQueries(graphene.ObjectType):
    feature_average_ratings = graphene.List(
        FeatureAverageRating,
        features=graphene.List(
            FeatureTypeEnum,
            description="List of features to get the average rating.",
            required=True
        ),
        description="Get the average rating of a feature.",
    )

    @staticmethod
    @permission_required(FeatureRatingPermissions.VIEW_FEATURE_RATING)
    def resolve_feature_average_ratings(root, info, **kwargs):
        return resolve_feature_average_rating(info, **kwargs)


class FeatureMutations(graphene.ObjectType):
    rate_feature = RateFeature.Field()
