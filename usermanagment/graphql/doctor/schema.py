import graphene
from django.core.exceptions import ValidationError
from graphene import Node

from .sorters import <PERSON><PERSON><PERSON><PERSON>, HealthSymptomOrder
from .subscriptions.doctor import DoctorAvailabilityStatusChange
from ..account.types import User
from ..core.fields import FilterInputConnectionField
from ..doctor.filters import Doctor<PERSON>ilterInput, \
    HealthSymptomFilterInput
from ..doctor.mutations.doctor import DoctorAvailabilityStatusUpdate, \
    DoctorCanAcceptCallUpdate, DoctorProfileUpdate, HealthSymptomCreate, \
    HealthSymptomDelete, HealthSymptomSpecializationsCreate, \
    HealthSymptomSpecializationDelete, DoctorExperienceCreate, DoctorExperienceUpdate, \
    DoctorExperienceDelete, DoctorRating, HealthSymptomUpdate, \
    DoctorActivityTrackerUpdate
from ..doctor.resolvers import resolve_doctors, resolve_doctors_availabilities_sum, \
    resolve_activity_based_doctors
from ..doctor.types import Doctor, HealthSymptom, DoctorAvailabilitiesSum, \
    DoctorExperience


class DoctorQueries(graphene.ObjectType):
    doctor = graphene.Field(
        Doctor,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the doctor.",
            required=False
        ),
        sso_id=graphene.Argument(
            graphene.String,
            description="sso id of the doctor.",
            required=False
        ),
        description="Look up a doctor by ID or federated ID.",
    )
    doctors = FilterInputConnectionField(
        Doctor,
        filter=DoctorFilterInput(description="Filtering options for doctors."),
        sort_by=DoctorOrder(description="Sort doctors."),
    )

    health_symptom = graphene.Field(
        HealthSymptom,
        id=graphene.Argument(graphene.ID, description="ID of the health condition."),
        description="Look up a health condition by ID.",
    )

    health_symptoms = FilterInputConnectionField(
        HealthSymptom,
        description="List of health conditions.",
        sort_by=HealthSymptomOrder(description="Sort health conditions."),
        filter=HealthSymptomFilterInput(
            description="Filtering options for health conditions."
        ),
    )

    doctors_availabilities_sum = graphene.List(
        DoctorAvailabilitiesSum,
        doctors=graphene.List(
            graphene.ID,
            description="List of doctors IDs",
            required=True
        ),
        from_date=graphene.Date(required=True),
        to_date=graphene.Date(required=True),
        description="List of doctors with availability audits sum for each one"
    )

    activity_based_doctors = FilterInputConnectionField(
        User,
        doctors_with_activities=graphene.Boolean(required=True),
        from_date=graphene.Date(required=True),
        to_date=graphene.Date(required=True),
        description="List of doctors based on their activity"
    )


    @staticmethod
    def resolve_doctor(root, info, **kwargs):
        if kwargs.get('id') is None and kwargs.get("sso_id") is None:
            raise ValidationError("you have to provide id or ssoId to this query")
        return resolve_doctors(info, **kwargs)

    @staticmethod
    def resolve_doctors(root, info, **kwargs):
        return resolve_doctors(info, **kwargs)

    @staticmethod
    def resolve_doctors_availabilities_sum(root, info, **kwargs):
        return resolve_doctors_availabilities_sum(info, **kwargs)

    @staticmethod
    def resolve_health_symptom(root, info, id, **kwargs):
        return Node.get_node_from_global_id(info, id, HealthSymptom)

    @staticmethod
    def resolve_activity_based_doctors(root, info, **kwargs):
        return resolve_activity_based_doctors(info, **kwargs)


class DoctorMutations(graphene.ObjectType):
    # doctor_create = DoctorCreate.Field()
    # doctor_update = DoctorUpdate.Field()
    # doctor_delete = DoctorDelete.Field()
    doctor_profile_update = DoctorProfileUpdate.Field()
    doctor_availability_status_update = DoctorAvailabilityStatusUpdate.Field()
    doctor_can_accept_call_update = DoctorCanAcceptCallUpdate.Field()

    health_symptom_create = HealthSymptomCreate.Field()
    health_symptom_update = HealthSymptomUpdate.Field()
    health_symptom_delete = HealthSymptomDelete.Field()
    health_symptom_specializations_create = HealthSymptomSpecializationsCreate.Field()
    health_symptom_specialization_delete = HealthSymptomSpecializationDelete.Field()

    doctor_experience_create = DoctorExperienceCreate.Field()
    doctor_experience_update = DoctorExperienceUpdate.Field()
    doctor_experience_delete = DoctorExperienceDelete.Field()
    doctor_rating = DoctorRating.Field()
    doctor_activity_tracker_update = DoctorActivityTrackerUpdate.Field()

class DoctorSubscriptions(graphene.ObjectType):
    doctor_availability = DoctorAvailabilityStatusChange.Field()
