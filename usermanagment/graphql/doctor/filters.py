import django_filters
import graphene
from django.db.models import Q
from django.utils import timezone
from graphene_django.filter import GlobalIDMultipleChoiceFilter, GlobalIDFilter
from django.core.exceptions import ValidationError

from .enums import DoctorAvailabilityStatus, AppointmentTypeEnum
from ..account.enums import PersonGenderEnum
from ..core.filters import ObjectTypeFilter, ListObjectTypeFilter, EnumFilter
from ..core.types import FilterInputObjectType
from ..core.types.common import DecimalRangeInput, IntRangeInput, DistanceFilterInput, \
    DateTimeFilterInput
from ..core.utils import from_global_id_strict_type
from ..utils import resolve_global_ids_to_primary_keys
from ..utils.filters import filter_range_field
from ...auth.enums import AppRoleTypes
from ...auth.permissions import AccountPermissions
from ...doctor import models, DoctorAvailabilityStatuses
from ...doctor.models import HealthSymptomSpecialization, HealthProgramsDoctorsView, \
    SearchKeyword
from ...graphql.utils.request_utils import get_current_user
from ...open_search.open_search_service import OpenSearchService
from ...vendor.enums import DayOfWeekEnum


def filter_specializations(qs, _, value):
    return qs.filter(specializations__code__in=value)


def filter_doctors_by_rating(qs, _, value):
    return filter_range_field(qs, "average_rating", value)


def filter_doctors_by_years_of_experience(qs, _, value):
    return filter_range_field(qs, "years_of_experience", value)


def filter_languages(qs, _, value):
    if value:
        return qs.filter(languages__code__in=value)
    return qs


def filter_by_can_accept_call(qs, _, value):
    if value is not None:
        return qs.filter(can_accept_call=value)

    return qs


def filter_by_gender(qs, _, value):
    if value:
        return qs.filter(user__gender=value)
    return qs


def filter_by_meeting_id(qs, _, value):
    if value:
        return qs.filter(user__meeting_platform_id=value)
    return qs


def filter_availability_status(qs, _, value):
    if value:

        current_time = timezone.now()

        query = Q(availabilities__status__in=value) \
                & Q(availabilities__start_time__lte=current_time) \
                & Q(availabilities__end_time__gte=current_time)

        if DoctorAvailabilityStatuses.UNAVAILABLE in value:
            query = query | Q(availabilities__end_time__lte=current_time)

        qs = qs.filter(query)

    return qs


def filter_by_location(qs, _, value):
    if value and value.distance:
        qs = qs.filter(distance__lte=value.distance)
    return qs


def filter_doctors_by_working_hours(qs, _, value):
    if value:
        if value.time.end < value.time.start:
            raise ValidationError("end time must be greater the start time "
                                  "when filtering doctors by appointmentAvailableTime")

        weekday = DayOfWeekEnum.from_python_weekday(value.date.weekday())
        qs = qs.filter(working_hours__day=weekday,
                       working_hours__open_time__lt=value.time.end,
                       working_hours__close_time__gt=value.time.start)

    return qs


def filter_doctors_by_vendor(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "Vendor")
        qs = qs.filter(vendor_id__in=pks)

    return qs


def filter_doctors_by_branch(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "Branch")
        qs = qs.filter(user__branches__in=pks)

    return qs


def filter_appointment_types(qs, _, values):
    if values:
        q = Q()
        for value in values:
            q |= Q(appointment_types__icontains=value)
        return qs.filter(q)
    return qs


def filter_doctors_by_patient_health_conditions(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "HealthSymptom")

        qs = qs.filter(specializations__code__in=
                       HealthSymptomSpecialization.objects.filter(
                           health_symptom_id__in=pks
                       ).values_list('code', flat=True)
                       )
    return qs


def filter_by_is_active(qs, _, value):
    current_time = timezone.now()
    if value:
        qs = qs.filter(Q(user__is_active=value)
                       , Q(user__vendor__is_active=value)
                       , (Q(user__app_role=AppRoleTypes.ADMIN) |
                            Q(user__branches__is_active=value))
                       , Q(health_license_number__isnull=False)
                       , Q(health_license_start_date__lte=current_time)
                       , Q(health_license_end_date__gte=current_time))
    else:
        qs = qs.filter(Q(user__is_active=value)
                       | Q(user__vendor__is_active=value)
                       | Q(user__branches__isnull=True)
                       | Q(user__branches__is_active=value)
                       | Q(health_license_number__isnull=True)
                       | Q(health_license_start_date__gt=current_time)
                       | Q(health_license_end_date__lt=current_time)) \
            .exclude(Q(user__vendor__is_active=True)
                     , Q(user__is_active=True)
                     , Q(user__branches__is_active=True)
                     , Q(health_license_number__isnull=False)
                     , Q(health_license_start_date__lte=current_time)
                     , Q(health_license_end_date__gte=current_time)
                     )
    return qs


def filter_by_from_online_visit_price(qs, _, value):
    if value:
        qs = qs.filter(online_visit_price__gte=value)
    return qs


def filter_by_to_online_visit_price(qs, _, value):
    if value:
        qs = qs.filter(online_visit_price__lte=value)
    return qs


def filter_by_from_at_home_visit_price(qs, _, value):
    if value:
        qs = qs.filter(at_home_visit_price__gte=value)
    return qs


def filter_by_to_at_home_visit_price(qs, _, value):
    if value:
        qs = qs.filter(at_home_visit_price__lte=value)
    return qs


def filter_by_from_onsite_visit_price(qs, _, value):
    if value:
        qs = qs.filter(onsite_visit_price__gte=value)
    return qs


def filter_by_to_onsite_visit_price(qs, _, value):
    if value:
        qs = qs.filter(onsite_visit_price__lte=value)
    return qs


def filter_by_name(qs, _, value):
    if value:
        qs = qs.filter(user__full_name__icontains=value)
    return qs


def filter_by_program_id(qs, _, value):
    if value:
        qs = qs.filter(id__in=
                       HealthProgramsDoctorsView.objects.filter(
                           program_id=value
                       ).values_list('doctor_id', flat=True)
                       )
    return qs


def filter_by_network_id(qs, _, value):
    if value:
        qs = qs.filter(id__in=
                       HealthProgramsDoctorsView.objects.filter(
                           network_id=value
                       ).values_list('doctor_id', flat=True)
                       )
    return qs


def filter_by_program_name(qs, _, value):
    if value:
        qs = qs.filter(id__in=
                       HealthProgramsDoctorsView.objects.filter(
                           program_name__icontains=value
                       ).values_list('doctor_id', flat=True)
                       )
    return qs


def filter_by_network_name(qs, _, value):
    if value:
        qs = qs.filter(id__in=
                       HealthProgramsDoctorsView.objects.filter(
                           network_name__icontains=value
                       ).values_list('doctor_id', flat=True)
                       )
    return qs


def filter_by_license_number(qs, _, value):
    if value:
        qs = qs.filter(health_license_number=value)
    return qs


def filter_by_license_number__icontains(qs, _, value):
    if value:
        qs = qs.filter(health_license_number__icontains=value)
    return qs


def filter_by_experience(qs, _, value):
    if value:
        qs = qs.filter(experiences__job_title__icontains=value)
    return qs

def filter_by_support_immediate_call(qs, _, value):
    if value is not None:
        qs = qs.filter(vendor__support_immediate_call=value)

    return qs

def filter_by_search(qs, _, value):
    if value:
        # check if the keyword is already in the database in SearchKeyword add 1 to the count
        # if not create a new SearchKeyword
        keyword = SearchKeyword.objects.filter(keyword=value).first()
        if keyword:
            keyword.count += 1
            keyword.save()
        else:
            SearchKeyword.objects.create(keyword=value, count=1)


        rs = OpenSearchService().search_by_keyword(value, "Doctor", 0,
                                                   100)
        ids = [from_global_id_strict_type(r['entity_id'], "Doctor") for r in rs]
    return qs.filter(id__in=ids)


def filter_ids(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "Doctor")
        qs = qs.filter(id__in=pks)
    return qs


class DoctorFilter(django_filters.FilterSet):
    ids = GlobalIDMultipleChoiceFilter(method=filter_ids)

    specializations = ListObjectTypeFilter(input_class=graphene.String,
                                           method=filter_specializations)
    location = ObjectTypeFilter(
        input_class=DistanceFilterInput,
        method=filter_by_location,
        field_name="address",
    )
    rating = ObjectTypeFilter(
        input_class=DecimalRangeInput,
        method=filter_doctors_by_rating,
        field_name="average_rating",
    )
    years_of_experience = ObjectTypeFilter(
        input_class=IntRangeInput,
        method=filter_doctors_by_years_of_experience,
        field_name="years_of_experience",
    )

    languages = ListObjectTypeFilter(
        input_class=graphene.String, method=filter_languages
    )

    gender = EnumFilter(
        input_class=PersonGenderEnum, method=filter_by_gender
    )

    can_accept_call = django_filters.BooleanFilter(method=filter_by_can_accept_call)

    meeting_platform_id = django_filters.CharFilter(method=filter_by_meeting_id)

    availability_status = ListObjectTypeFilter(
        input_class=DoctorAvailabilityStatus, method=filter_availability_status
    )

    appointment_available_time = ObjectTypeFilter(
        input_class=DateTimeFilterInput,
        method=filter_doctors_by_working_hours,
        field_name="working_hours",
    )

    vendor = GlobalIDMultipleChoiceFilter(method=filter_doctors_by_vendor)

    branch = GlobalIDMultipleChoiceFilter(method=filter_doctors_by_branch)

    appointment_types = ListObjectTypeFilter(
        input_class=AppointmentTypeEnum, method=filter_appointment_types
    )

    health_conditions = GlobalIDMultipleChoiceFilter(
        method=filter_doctors_by_patient_health_conditions
    )

    city = GlobalIDFilter(field_name="user__addresses__city")

    is_active = django_filters.BooleanFilter(method=filter_by_is_active)

    license_number = django_filters.CharFilter(method=filter_by_license_number)

    license_number__icontains = django_filters.CharFilter(
        method=filter_by_license_number__icontains)

    name = django_filters.CharFilter(method=filter_by_name)

    program_name = django_filters.CharFilter(method=filter_by_program_name)

    network_name = django_filters.CharFilter(method=filter_by_network_name)

    program_id = django_filters.NumberFilter(method=filter_by_program_id)

    network_id = django_filters.NumberFilter(method=filter_by_network_id)

    from_online_visit_price = django_filters.NumberFilter(
        method=filter_by_from_online_visit_price)

    to_online_visit_price = django_filters.NumberFilter(
        method=filter_by_to_online_visit_price)

    from_at_home_visit_price = django_filters.NumberFilter(
        method=filter_by_from_at_home_visit_price)

    to_at_home_visit_price = django_filters.NumberFilter(
        method=filter_by_to_at_home_visit_price)

    from_onsite_visit_price = django_filters.NumberFilter(
        method=filter_by_from_onsite_visit_price)

    to_onsite_visit_price = django_filters.NumberFilter(
        method=filter_by_to_onsite_visit_price)

    experience = django_filters.CharFilter(method=filter_by_experience)

    search = django_filters.CharFilter(method=filter_by_search)

    department_ids = GlobalIDMultipleChoiceFilter(field_name="user__departments")

    support_immediate_call = django_filters.BooleanFilter(method=filter_by_support_immediate_call)


    def __init__(self, data=None, *args, **kwargs):
        current_user = get_current_user()
        if not current_user.has_perm(AccountPermissions.MANAGE_USERS):
            data["is_active"] = True

        # is_languages_filter_provided = data and data.get("languages")
        # if current_user.is_consumer and not is_languages_filter_provided:
        #     preferred_language = current_user.preferred_language
        #     if preferred_language:
        #         data["languages"] = [
        #             graphene.Node.to_global_id("Language", preferred_language.id)]

        super(DoctorFilter, self).__init__(data, *args, **kwargs)

    class Meta:
        model = models.Doctor
        fields = {}


class DoctorFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = DoctorFilter


def filter_top_level(qs, _, value):
    if value is not None:
        if value:
            qs = qs.filter(parent__isnull=True)
        else:
            qs = qs.filter(parent__isnull=False)

    return qs


def filter_specializations_by_health_symptoms(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "HealthSymptom")
        qs = qs.filter(health_conditions__in=pks)

    return qs


def filter_health_symptoms_by_specializations(qs, _, value):
    if value:
        return qs.filter(specializations__code__in=value)


class HealthSymptomFilter(django_filters.FilterSet):
    specializations = ListObjectTypeFilter(input_class=graphene.String,
                                           method=filter_health_symptoms_by_specializations
                                           )

    class Meta:
        model = models.HealthSymptom
        fields = {
            "name": ["exact", "icontains"],
        }


class HealthSymptomFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = HealthSymptomFilter
