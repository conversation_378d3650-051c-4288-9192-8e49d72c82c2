import graphene
from django.db.models import F

from ..core.types import SortInputObjectType


class DoctorOrderField(graphene.Enum):
    RATING = ["average_rating", "pk"]
    CREATED = ["created", "pk"]
    DISTANCE = ["distance", "pk"]
    FIRST_AVAILABLE_DATE_TIME = ["is_first_available_date_time_null","first_available_date_time", "pk"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            DoctorOrderField.RATING.name: "rating",
            DoctorOrderField.CREATED.name: "creation date",
            DoctorOrderField.DISTANCE.name: "distance",
            DoctorOrderField.FIRST_AVAILABLE_DATE_TIME.name: "first available date time",
        }
        if self.name in descriptions:
            return f"Sort doctors by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.value)

class DoctorOrder(SortInputObjectType):
    field = graphene.Argument(
        Doctor<PERSON>rde<PERSON><PERSON>ield, description="Sort doctors by the selected field."
    )

    class Meta:
        sort_enum = DoctorOrderField


class HealthSymptomOrderField(graphene.Enum):
    CREATED = ["created", "pk"]
    NAME = ["name", "pk"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            HealthSymptomOrderField.CREATED.name: "creation date",
            HealthSymptomOrderField.NAME.name: "name",
        }
        if self.name in descriptions:
            return f"Sort health conditions by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.name)


class HealthSymptomOrder(SortInputObjectType):
    field = graphene.Argument(
        HealthSymptomOrderField,
        description="Sort health conditions by the selected field."
    )

    class Meta:
        sort_enum = HealthSymptomOrderField
