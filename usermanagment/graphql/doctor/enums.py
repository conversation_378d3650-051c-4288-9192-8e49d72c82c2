import graphene

from ..core.enums import to_enum
from ..core.utils import str_to_enum
from ...doctor import DoctorAvailabilityStatuses, AppointmentType, \
    DoctorSeniority, RatingAction

DoctorAvailabilityStatus = graphene.Enum(
    "DoctorAvailabilityStatusEnum",
    [(str_to_enum(status[0]), status[0]) for status in
     DoctorAvailabilityStatuses.choices],
)


AppointmentTypeEnum = to_enum(AppointmentType)

DoctorSeniorityEnum = graphene.Enum(
    "DoctorSeniorityEnum",
    [(seniority[1].upper(), seniority[0]) for seniority in DoctorSeniority.choices]
)

RatingActionEnum = graphene.Enum(
    "RatingActionEnum",
    [(action[1].upper(), action[0]) for action in RatingAction.choices]
)
