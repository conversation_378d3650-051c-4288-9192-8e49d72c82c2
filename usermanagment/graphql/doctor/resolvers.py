from django.core.exceptions import ValidationError
from django.db.models import Sum

from graphene import Node
from django.contrib.gis.geos import Point
from django.db.models import Value

from .types import DoctorAvailabilitiesSum
from ..core.utils import from_global_id_strict_type
from ..utils.request_utils import get_current_user
from ... import settings
from ...auth.enums import VendorUserTypes
from ...auth.exceptions import PermissionDenied
from ...doctor import models
from ...account.models.user import User
from django.db.models import FloatField
from django.db.models.functions import Cast

from ...map.postgis.functions import DistanceSphere
from ...vendor.models import UserNetworkView


def resolve_doctors(info, **_kwargs):
    qs = models.Doctor.objects.accessible_doctors()
    if settings.FILTER_DOCTORS_BY_PATIENT_NETWORK:
        if get_current_user().is_consumer:
            qs = qs.filter(user__branches__id__in=
                           UserNetworkView.objects.filter(
                               user_id=get_current_user().id).
                           values_list('branch_id', flat=True))

    if _kwargs.get('id') is not None:
        pk = from_global_id_strict_type(_kwargs.get('id'), "Doctor", "id")
        return qs.filter(pk=pk).first()
    elif _kwargs.get("sso_id") is not None:
        return qs.filter(user__sso_id=_kwargs.get("sso_id")).first()
    if _kwargs.get('filter') and _kwargs.get('filter').get('location'):
        value = _kwargs.get('filter').get('location')
        location = Point(x=value.coordinates.lng, y=value.coordinates.lat, srid=4326)
        qs = qs.annotate(
            distance=Cast(DistanceSphere('user__branches__address__location', location),
                          FloatField())
        )
    else:
        qs = qs.annotate(
            distance=Value(0, output_field=FloatField())
        )

    return qs.distinct()


def resolve_doctors_availabilities_sum(info, **kwargs):
    current_user = get_current_user()

    if not current_user.is_admin and not current_user.is_vendor:
        raise PermissionDenied()

    if not kwargs.get("doctors"):
        raise ValidationError("One doctor id at least must be provided")

    doctors_pks = [from_global_id_strict_type(doctor_id, "Doctor") for doctor_id in
                   kwargs["doctors"]]

    from_date = kwargs.get("from_date")
    to_date = kwargs.get("to_date")

    allowed_doctors = filter_allowed_doctors(current_user, doctors_pks)

    qs = models.DoctorAvailability.objects \
        .filter(doctor__in=allowed_doctors, start_time__range=(from_date, to_date)) \
        .values('doctor') \
        .annotate(total_periods=Sum('period'))

    result = []

    for doctor_availability in qs:
        doctor_availability_audits_sum = DoctorAvailabilitiesSum()

        total_hours = doctor_availability['total_periods'].total_seconds() / 3600
        doctor_global_id = Node.to_global_id("Doctor", doctor_availability['doctor'])

        doctor_availability_audits_sum.doctor = doctor_global_id
        doctor_availability_audits_sum.sum_of_availabilities = round(total_hours, 2)

        result.append(doctor_availability_audits_sum)

    return result


def filter_allowed_doctors(current_user: User, doctors_pks):
    allowed_doctors = []

    if current_user.is_superuser:
        allowed_doctors = doctors_pks

    elif current_user.is_vendor_admin:

        allowed_doctors = models.Doctor.objects. \
            filter(vendor=current_user.vendor_id, id__in=doctors_pks). \
            values_list('id', flat=True)

    elif current_user.is_doctor:
        current_doctor_id = str(current_user.doctor_id)
        if current_doctor_id in doctors_pks:
            allowed_doctors = [current_doctor_id]
        else:
            raise ValidationError("You can get your data only")
    else:
        raise PermissionDenied()

    return allowed_doctors


def resolve_activity_based_doctors(info, **kwargs):
    doctors_with_activities = kwargs.get("doctors_with_activities")
    from_date = kwargs.get("from_date")
    to_date = kwargs.get("to_date")

    if doctors_with_activities:
        return User.objects.filter(
            id__in=models.DoctorActivityTracker.objects.filter(
                activity_date__range=(from_date, to_date)
            ).values_list('user', flat=True)
        ).filter(vendor_user_type=VendorUserTypes.DOCTOR)

    else:
        return User.objects.exclude(
            id__in=models.DoctorActivityTracker.objects.filter(
                activity_date__range=(from_date, to_date)
            ).values_list('user', flat=True)
        ).filter(vendor_user_type=VendorUserTypes.DOCTOR)
