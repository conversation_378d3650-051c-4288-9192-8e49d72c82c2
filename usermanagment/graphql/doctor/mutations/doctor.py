from datetime import datetime, timezone
from datetime import <PERSON><PERSON><PERSON>

import graphene
from django.core.exceptions import ValidationError
from django.db import transaction

from ..enums import DoctorAvailabilityStatus, AppointmentTypeEnum, \
    DoctorSeniorityEnum, RatingActionEnum
from ..subscriptions.doctor import DoctorAvailabilityStatusChange
from ..types import <PERSON>, DoctorLastVisit
from ...account.i18n import I18nMixin
from ...account.utils import fetch_language_instances_by_codes
from ...core.mutations import ModelMutation, ModelDeleteMutation, BaseMutation
from ...core.scalars import Decimal
from ...core.types.common import DoctorError, WorkingHourInput, HealthSymptomError, \
    SpecializationError
from ...core.working_hours import WorkingHoursMixin
from ...doctor_mixin import DoctorMutationMixin
from ...utils import get_database_id
from ...utils.request_utils import get_current_user
from ....account.error_codes import AccountErrorCode
from ....auth.exceptions import PermissionDenied
from ....auth.permissions import DoctorPermissions
from ....authorization.authorization_mixins import SuperuserOrVendorAdminMixin
from ....doctor import models, DoctorAvailabilityStatuses, RatingAction
from ....doctor.error_codes import DoctorErrorCode
from ....doctor.models import HealthSymptomSpecialization
from ....kafka.topics import KafkaTopics

class QualificationInput(graphene.InputObjectType):
    code = graphene.String()
    from_date = graphene.Date()
    to_date = graphene.Date()
    issuer = graphene.String()


class DoctorRatingInput(graphene.InputObjectType):
    doctor = graphene.ID(
        required=True, description="Doctor ID."
    )
    old_rating = Decimal(
        required=False, description="Old Doctor Rating."
    )
    rating = Decimal(
        required=True, description="Doctor Rating."
    )
    action = RatingActionEnum(
        required=True, description="Rating Action."
    )


class DoctorProfileInput(graphene.InputObjectType):
    is_date_of_birth_public = graphene.Boolean()
    bio = graphene.String()
    languages = graphene.List(graphene.String, description="Doctor Languages")
    is_languages_public = graphene.Boolean()
    is_mobile_number_public = graphene.Boolean()
    social_links = graphene.List(graphene.NonNull(graphene.String))
    is_social_links_public = graphene.Boolean()
    is_address_public = graphene.Boolean()
    working_hours = graphene.List(
        WorkingHourInput,
        description="Doctor Working Hours")

    appointment_slot_time_period = graphene.Int()

    qualifications = graphene.List(QualificationInput)
    specializations = graphene.List(graphene.String)
    appointment_types = graphene.List(AppointmentTypeEnum)
    years_of_experience = graphene.Int()
    is_years_of_experience_public = graphene.Boolean()
    seniority = DoctorSeniorityEnum(description="Seniority of the doctor")
    photo = graphene.String()
    date_of_birth = graphene.Date()
    notification_email = graphene.String()


class DoctorInput(DoctorProfileInput):
    is_national_id_public = graphene.Boolean()
    is_license_number_public = graphene.Boolean()
    years_of_experience = graphene.Int()
    is_years_of_experience_public = graphene.Boolean()
    specializations = graphene.List(graphene.String)
    second_mobile_number = graphene.String(description="Doctor 2nd mobile number.")
    is_second_mobile_number_public = graphene.Boolean()
    seniority = DoctorSeniorityEnum(description="Seniority of the doctor")
    online_visit_price = graphene.Float(required=False)
    onsite_visit_price = graphene.Float(required=False)
    at_home_visit_price = graphene.Float(required=False)
    health_license_number = graphene.String(
        description="Health license number.", required=False)
    health_license_start_date = graphene.Date(
        description="Health license start date.", required=False)
    health_license_end_date = graphene.Date(
        description="Health license end date.", required=False)
    first_available_date_time = graphene.DateTime(
        description="First available date time.", required=False)


class DoctorUserInput(graphene.InputObjectType):
    # the next five lines were copied from UserInput class
    first_name = graphene.String(description="Given name.")
    last_name = graphene.String(description="Family name.")
    is_active = graphene.Boolean(required=False, description="User account is active.")
    note = graphene.String(description="A note about the user.")
    meeting_platform_id = graphene.String(required=False)
    email = graphene.String(description="The unique email address of the user.",
                            required=True)
    mobile = graphene.String(description="The unique mobile of the user.",
                             required=False)
    branches = graphene.List(graphene.ID,
                             description="vendor branches this user has access to",
                             required=False)


class DoctorCreateInput(DoctorInput):
    user_info = DoctorUserInput(required=True)
    national_id = graphene.String()


class HealthSymptomInput(graphene.InputObjectType):
    name = graphene.String(description="Health condition name.", required=True)
    specializations = graphene.List(
        graphene.String, required=False, description="Specializations codes."
    )

class DoctorExperienceInput(graphene.InputObjectType):
    description = graphene.String(description="Job description.")
    end_date = graphene.Date(description="Job end date.")


class DoctorExperienceCreateInput(DoctorExperienceInput):
    job_title = graphene.String(description="Job title.", required=True)
    provider = graphene.String(description="Job provider.", required=True)
    start_date = graphene.Date(description="Job start date.", required=True)


class DoctorExperienceUpdateInput(DoctorExperienceInput):
    job_title = graphene.String(description="Job title.")
    provider = graphene.String(description="Job provider.")
    start_date = graphene.Date(description="Job start date.")


class DoctorCreate(SuperuserOrVendorAdminMixin, ModelMutation, I18nMixin,
                   WorkingHoursMixin, DoctorMutationMixin):
    class Arguments:
        vendor_id = graphene.ID(
            description="ID of a vendor the doctor belong to. "
                        "this field for super admin use"
        )
        input = DoctorInput(
            description="Fields required to create a doctor", required=True
        )

    class Meta:
        description = "Create a new Doctor"
        model = models.Doctor
        permissions = (DoctorPermissions.MANAGE_DOCTORS,)
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):

        if not data.get("seniority", instance.seniority):
            raise ValidationError({
                "seniority": ValidationError("This field is required.",
                                             code=DoctorErrorCode.REQUIRED.value)
            })

        cleaned_data = super().clean_input(info, instance, data, input_cls)
        address_data = data.pop("address_input", None)
        if address_data:
            cleaned_data["address"] = cls.construct_address_instance(info,
                                                                     address_data,
                                                                     instance)

        cls.validate_working_hours(info, data)
        cls.validate_years_of_experience(cleaned_data, instance)
        cls.validate_appointment_time_slot_period(cleaned_data, instance)
        cls.validate_appointment_types(cleaned_data, instance)
        cls.validate_visit_price(cleaned_data, instance)

        qualifications = cleaned_data.get('qualifications',[])

        # if cls is DoctorCreate:
        #     if not qualifications:
        #         raise ValidationError({
        #             "qualifications": ValidationError("This field is required.",
        #                                               code=DoctorErrorCode.REQUIRED.value)
        #         })

        for qualification in qualifications:
            if qualification.get('from_date') and qualification.get('to_date') and \
                    qualification.get('from_date') > qualification.get('to_date'):
                raise ValidationError({
                    "qualifications": ValidationError(
                        "End date must be after start date.",
                        code=DoctorErrorCode.INVALID.value)
                })

        photo = data.get("photo")
        if photo:
            cleaned_data["photo"] = photo

        date_of_birth = data.get("date_of_birth")
        if date_of_birth:
            # must not be less than 18 years ago
            if date_of_birth > (
                    datetime.now(timezone.utc) - timedelta(days=365 * 18)).date():
                raise ValidationError({
                    "date_of_birth": ValidationError(
                        "Doctor must be at least 18 years old.",
                        code=DoctorErrorCode.INVALID.value)
                })
            cleaned_data["date_of_birth"] = date_of_birth

        if data.get("languages"):
            languages = fetch_language_instances_by_codes(data.get("languages"))
            cleaned_data["languages"] = languages
        if data.get('first_available_date_time', instance.first_available_date_time):
            cleaned_data["is_first_available_date_time_null"] = False
        else:
            cleaned_data["is_first_available_date_time_null"] = True
        return cleaned_data

    @classmethod
    def save(cls, info, instance, cleaned_input):

        if cls is DoctorProfileUpdate:
            user = instance.user

            photo = cleaned_input.get("photo", user.photo)
            date_of_birth = cleaned_input.get("date_of_birth", user.date_of_birth)

            user.photo = photo
            user.date_of_birth = date_of_birth

            user.save(update_fields=["photo", "date_of_birth"])

        qualifications = cleaned_input.pop('qualifications', [])
        specializations = cleaned_input.pop('specializations', [])
        super().save(info, instance, cleaned_input)
        if qualifications:
            instance.qualifications.all().delete()
            for qualification in qualifications:
                models.Qualification.objects.create(
                    doctor=instance,
                    **qualification
                )
        if specializations:
            instance.specializations.all().delete()
            for specialization in specializations:
                models.DoctorSpecialization.objects.create(
                    doctor=instance,
                    code=specialization
                )

    @classmethod
    def _save_m2m(cls, info, instance, cleaned_data):
        super()._save_m2m(info, instance, cleaned_data)
        cls.save_working_hours(info, instance, cleaned_data, models.DoctorWorkingHour,
                               'doctor_id')

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        # user_info = data["input"].pop("user_info", None)
        doctor_create_result = super().perform_mutation(_root, info, **data)

        # if doctor_create_result.doctor and user_info:
        #     user_info["app_type"] = AppTypes.VENDOR
        #     user_info["app_role"] = AppRoleTypes.USER
        #     # TODO [MOA] review this
        #     user_info["password"] = "password"
        #     user_info["first_name"] = user_info.get("first_name",
        #                                             doctor_create_result.doctor.first_name)
        #     user_info["last_name"] = user_info.get("last_name",
        #                                            doctor_create_result.doctor.last_name)
        #     user_info["vendor"] = data['vendor_id']
        #     user_info["validate_type"] = False
        #     user_info["doctor_id"] = doctor_create_result.doctor.id
        #     user_info["national_id"] = doctor_create_result.doctor.national_id
        #
        #     user_create_result = UserCreate().perform_mutation(
        #         _root, info, **{
        #             "input": user_info
        #         }
        #     )
        #     if user_create_result.user:
        #         user_create_result.user.user_permissions.add(
        #             *get_permissions([HealthProgramPermissions.MANAGE_VISITS.value])
        #         )
        #         doctor_create_result.doctor.user = user_create_result.user
        #         doctor_create_result.doctor.save(update_fields=["user"])
        #         keycloak_user_data = {"attributes": {}}
        #         user = user_create_result.user
        #         prepare_user_attributes(user, keycloak_user_data["attributes"])
        #         prepare_user_branches([], user, keycloak_user_data["attributes"])
        #         KeycloakAPI().update_user(user.sso_id, keycloak_user_data)
        #     else:
        #         return DoctorCreate(errors=user_create_result.errors)

        return doctor_create_result


class DoctorUpdate(DoctorCreate):
    class Arguments:
        id = graphene.ID(description="ID of the doctor to be updated.", required=True)
        input = DoctorInput(
            description="Fields required to update a doctor", required=True
        )

    class Meta:
        description = "Updates an existing Doctor"
        model = models.Doctor
        permissions = (DoctorPermissions.MANAGE_DOCTORS,)
        error_type_class = DoctorError
        error_type_field = "doctor_errors"


class DoctorDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(description="ID of doctor to delete.", required=True)

    class Meta:
        description = "deletes an existing doctor."
        model = models.Doctor
        permissions = (DoctorPermissions.MANAGE_DOCTORS,)
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        doctor = result.doctor
        if doctor.user:
            doctor.user.delete()

        return result


def handle_doctor_availability_status_update(doctor, data):
    availability_status = data["availability_status"]
    current_time = datetime.now(timezone.utc)
    period = data.get("period")
    if period < 0:
        raise ValidationError(
            {
                "period": ValidationError(
                    "period cannot be negative", code=AccountErrorCode.INVALID
                )
            }
        )

    if period == 0 and availability_status != DoctorAvailabilityStatuses.UNAVAILABLE:
        raise ValidationError(
            {
                "period": ValidationError(
                    "period cannot be zero", code=AccountErrorCode.INVALID
                )
            }
        )

    current_availability = models.DoctorAvailability.objects \
        .filter(doctor_id=doctor.id,
                start_time__lt=current_time,
                end_time__gt=current_time).first()

    if availability_status in [DoctorAvailabilityStatuses.AVAILABLE_L1,
                               DoctorAvailabilityStatuses.AVAILABLE_L2]:
        doctor.can_accept_call = True
        doctor.save(update_fields=["can_accept_call"])

    # if the previous status period has not ended, then update it
    if current_availability:

        if availability_status != current_availability.status:

            current_availability.end_time = current_time
            current_availability.period = current_time - current_availability.start_time
            current_availability.save(update_fields=["end_time", "period"])

            added_availability = add_new_availability(availability_status, current_time,
                                                      doctor, period,
                                                      current_availability)
            return added_availability

        elif availability_status == current_availability.status:

            current_availability.end_time = current_time + timedelta(hours=period)
            current_availability.period = current_availability.end_time - current_availability.start_time
            current_availability.save(update_fields=["end_time", "period"])

            return current_availability

    # if the user sent UNAVAILABLE status and the user is already UNAVAILABLE
    elif availability_status == DoctorAvailabilityStatuses.UNAVAILABLE:

        raise ValidationError({
            "availabilityStatus": ValidationError(
                "Your current status is Unavailable")
        })

    # Add new availability status
    else:
        added_availability = add_new_availability(availability_status, current_time,
                                                  doctor, period, None)
        return added_availability


def add_new_availability(availability_status, current_time, doctor, period,
                         previous_availability):
    if availability_status == DoctorAvailabilityStatuses.UNAVAILABLE:
        return previous_availability

    availability_data = {
        "doctor": doctor,
        "vendor": doctor.vendor,
        "status": availability_status,
        "period": timedelta(hours=period),
        "start_time": current_time,
        "end_time": current_time + timedelta(hours=period)
    }
    added_availability = models.DoctorAvailability.objects.create(
        **availability_data
    )
    return added_availability


class DoctorCanAcceptCallUpdate(BaseMutation):
    result = graphene.String(description="result")

    class Arguments:
        doctor_id = graphene.ID(
            required=False, description="Doctor id."
        )
        can_accept_call = graphene.Boolean(
            description="Doctor Can Accept Call", required=True
        )

    class Meta:
        description = "update can accept call status"
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        doctor_id = data.pop('doctor_id', None)
        if doctor_id:
            doctor_id = get_database_id(doctor_id, "Doctor")
            current_doctor = models.Doctor.objects.filter(
                id=doctor_id).first()
        else:
            current_doctor = get_current_user().doctor

        if current_doctor is None:
            raise ValidationError("Doctor not found")

        if not current_doctor.user.is_active:
            raise ValidationError("Doctor not active")

        if not (get_current_user().is_superuser or \
                current_doctor.id == get_current_user().doctor.id):
            raise PermissionDenied()

        return {
            "doctor": current_doctor
        }

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, root, info, **data):
        auth_data = cls.check_authorization(root, info, None, **data)
        doctor = auth_data["doctor"]
        doctor.can_accept_call = data.get('can_accept_call')
        doctor.save()
        return DoctorCanAcceptCallUpdate(result="success")


class DoctorAvailabilityStatusUpdate(BaseMutation):
    result = graphene.String(description="result")

    class Arguments:
        doctor_id = graphene.ID(
            required=False, description="Doctor id."
        )
        availability_status = DoctorAvailabilityStatus(
            description="Doctor Availability Status", required=True
        )
        period = graphene.Int(description="Availability period", required=True)

    class Meta:
        description = "update the availability status of the doctor"
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        doctor_id = data.pop('doctor_id', None)
        if doctor_id:
            doctor_id = get_database_id(doctor_id, "Doctor")
            current_doctor = models.Doctor.objects.filter(
                id=doctor_id).first()
        else:
            current_doctor = get_current_user().doctor

        if current_doctor is None:
            raise ValidationError("Doctor not found")

        if not current_doctor.user.is_active:
            raise ValidationError("Doctor not active")

        if not (get_current_user().is_superuser or \
                current_doctor.id == get_current_user().doctor.id):
            raise PermissionDenied()

        return {
            "doctor": current_doctor
        }

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, root, info, **data):
        auth_data = cls.check_authorization(root, info, None, **data)
        doctor = auth_data["doctor"]

        last_availability = handle_doctor_availability_status_update(doctor, data)

        doctor_availability_status_changed(last_availability)
        return DoctorAvailabilityStatusUpdate(result="success")


def doctor_availability_status_changed(last_availability: models.DoctorAvailability):
    period = (
                     last_availability.end_time - last_availability.start_time).total_seconds() / 3600
    DoctorAvailabilityStatusChange.broadcast(
        topic=KafkaTopics.DOCTOR_AVAILABILITY_STATUS_TOPIC,
        payload={
            'doctor': graphene.Node.to_global_id("Doctor", last_availability.doctor_id),
            'user': graphene.Node.to_global_id("User",
                                               last_availability.doctor.user_id),
            'status': last_availability.status,
            'startTime': str(last_availability.start_time),
            "endTime": str(last_availability.end_time),
            'period': round(period, 2)
        },
    )


class DoctorProfileUpdate(DoctorUpdate):
    class Arguments:
        input = DoctorProfileInput(required=True)

    class Meta:
        description = "Updates Doctor Profile"
        model = models.Doctor
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not instance or not instance.user.is_active:
            raise PermissionDenied()

        return {}

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user().doctor

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls)

        if data.get("languages"):
            languages = fetch_language_instances_by_codes(data.get("languages"))
            cleaned_data["languages"] = languages

        return cleaned_data


class HealthSymptomCreate(ModelMutation):
    class Arguments:
        input = HealthSymptomInput(
            required=True, description="Fields required to create a health condition."
        )

    class Meta:
        description = "Creates a new health condition."
        model = models.HealthSymptom
        permissions = (DoctorPermissions.MANAGE_HEALTH_SYMPTOMS,)
        error_type_class = HealthSymptomError
        error_type_field = "health_symptom_errors"

    @classmethod
    @transaction.atomic
    def _save_m2m(cls, info, instance, cleaned_data):
        super()._save_m2m(info, instance, cleaned_data)
        specializations = cleaned_data.get("specializations", [])
        for specialization in specializations:
            HealthSymptomSpecialization.objects.create(
                code=specialization,
                health_symptom=instance
            )

class HealthSymptomUpdate(HealthSymptomCreate):
    class Arguments:
        id = graphene.ID(
            required=True, description="ID of a health condition to update."
        )
        input = HealthSymptomInput(
            required=True, description="Fields required to update a health condition."
        )

    class Meta:
        description = "Updates an existing health condition."
        model = models.HealthSymptom
        permissions = (DoctorPermissions.MANAGE_HEALTH_SYMPTOMS,)
        error_type_class = HealthSymptomError
        error_type_field = "health_symptom_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls)
        specializations = data.get("specializations", None)
        if specializations:
            instance.specializations.all().delete()
        return cleaned_data

class HealthSymptomDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a health condition to delete.")

    class Meta:
        description = "Deletes a health condition."
        model = models.HealthSymptom
        permissions = (DoctorPermissions.MANAGE_HEALTH_SYMPTOMS,)
        error_type_class = HealthSymptomError
        error_type_field = "health_symptom_errors"


class HealthSymptomSpecializationsCreate(ModelMutation):
    class Arguments:
        id = graphene.ID(
            required=True, description="Health condition id."
        )
        specializations = graphene.List(
            graphene.String, required=True, description="Specializations codes."
        )

    class Meta:
        model = models.HealthSymptom
        permissions = (DoctorPermissions.MANAGE_HEALTH_SYMPTOMS,)
        description = "Add specializations to health condition"
        error_type_class = SpecializationError
        error_type_field = "specialization_errors"

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, root, info, **data):
        instance = super().get_instance(info, **data)
        input_specializations = data.get("specializations", None)

        specializations = [
            HealthSymptomSpecialization(code=specialization,
                                        health_symptom=instance)
            for specialization in input_specializations]

        HealthSymptomSpecialization.objects.bulk_create(specializations,
                                                          ignore_conflicts=True)

        return super().success_response(instance)


class HealthSymptomSpecializationDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a health condition specialization to delete.")

    class Meta:
        description = "Deletes a health condition specialization."
        model = models.HealthSymptomSpecialization
        permissions = (DoctorPermissions.MANAGE_HEALTH_SYMPTOMS,)
        error_type_class = SpecializationError
        error_type_field = "specialization_errors"


# mutation to create doctor experience
class DoctorExperienceCreate(ModelMutation):
    class Arguments:
        input = DoctorExperienceCreateInput(
            required=True, description="Fields required to create a doctor experience."
        )

    class Meta:
        description = "Creates a new doctor experience."
        model = models.DoctorExperience
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_doctor

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls)

        # make sure that end date is after start date
        end_date = cleaned_data.get("end_date")
        if end_date and end_date < cleaned_data.get("start_date"):
            raise ValidationError({"end_date": "end date cannot be before start date"})

        if cls is DoctorExperienceCreate:
            cleaned_data["doctor"] = get_current_user().doctor
        return cleaned_data


# mutation to update doctor experience
class DoctorExperienceUpdate(DoctorExperienceCreate):
    class Arguments:
        id = graphene.ID(
            required=True, description="Doctor experience id."
        )
        input = DoctorExperienceUpdateInput(
            required=True, description="Fields required to update a doctor experience."
        )

    class Meta:
        description = "Updates an existing doctor experience."
        model = models.DoctorExperience
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        # doctor can update his own experience
        if instance.doctor.id != get_current_user().doctor.id:
            raise PermissionDenied()
        return {}


# mutation to delete doctor experience
class DoctorExperienceDelete(DoctorExperienceUpdate, ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a doctor experience to delete.")

    class Meta:
        description = "Deletes a doctor experience."
        model = models.DoctorExperience
        error_type_class = DoctorError
        error_type_field = "doctor_errors"


class DoctorRating(ModelMutation):
    class Arguments:
        input = DoctorRatingInput(
            required=True, description="Fields required to create a doctor rating."
        )
    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_superuser

    class Meta:
        description = "Creates a new doctor rating."
        model = models.Doctor
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        rating = data.get('input').get('rating')
        old_rating = data.get('input').get('old_rating')
        action = data.get('input').get('action')
        doctor = cls.get_node_or_error(info, data.get('input').get('doctor'), Doctor)
        if action == RatingAction.CREATE:
            doctor.total_ratings += 1
            doctor.ratings_sum += rating
        else:
            if not old_rating:
                raise ValidationError({
                    "old_rating": ValidationError("Old Rating is required.", code=DoctorErrorCode.REQUIRED.value)
                })
            doctor.ratings_sum = doctor.ratings_sum - old_rating + rating
        doctor.average_rating = 0 if not doctor.total_ratings else round(
            doctor.ratings_sum / doctor.total_ratings, 2)
        doctor.save(update_fields=["total_ratings", "ratings_sum", "average_rating"])
        return DoctorRating(doctor=doctor)


class DoctorActivityTrackerUpdate(BaseMutation):
    doctor_activity_tracker = graphene.Field(DoctorLastVisit,
                                             description="Doctor Activity Tracker")

    class Meta:
        description = "Create or update doctor activity tracker"
        error_type_class = DoctorError
        error_type_field = "doctor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated and context.user.is_doctor

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, root, info, **data):

        current_user = get_current_user()

        doctor_activity_tracker = models.DoctorActivityTracker.objects.filter(
            user=current_user,
            activity_date=datetime.now().date()).first()

        if doctor_activity_tracker:
            doctor_activity_tracker.activity_date = datetime.now()
            doctor_activity_tracker.count += 1
            doctor_activity_tracker.save()
        else:
            doctor_activity_tracker = models.DoctorActivityTracker.objects.create(
                user=current_user, activity_date=datetime.now())

        return DoctorActivityTrackerUpdate(
            doctor_activity_tracker=doctor_activity_tracker)
