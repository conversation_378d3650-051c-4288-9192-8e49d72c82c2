import graphene
from django.utils import timezone
from graphene import relay
from graphene_federation import key

from .enums import AppointmentType<PERSON><PERSON>, Doctor<PERSON>eniorityE<PERSON>, \
    DoctorAvailabilityStatus
from ..account.types import Language
from ..core.connection import CountableDjangoObjectType
from ..core.types.common import WorkingHour, Image
from ..utils.request_utils import get_current_user
from ...doctor import models, DoctorAvailabilityStatuses


@key(fields="id")
class Doctor(CountableDjangoObjectType):
    specializations = graphene.List(lambda: DoctorSpecialization)
    qualifications = graphene.List(lambda: Qualification)
    experiences = graphene.List(lambda: DoctorExperience)

    last_availability = graphene.Field(lambda: DoctorAvailability)

    working_hours = graphene.List(
        WorkingHour, description="list of doctor working hours")

    appointment_types = graphene.List(AppointmentTypeEnum)

    years_of_experience = graphene.Int()

    seniority = DoctorSeniorityEnum()

    availability_status = DoctorAvailabilityStatus(
        description="Doctor Availability Status", required=True
    )

    languages = graphene.List(Language)

    class Meta:
        description = "Represents an individual Doctor"
        interfaces = [relay.Node]
        model = models.Doctor
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]

    @staticmethod
    def resolve_specializations(root, info, **kwargs):
        return root.specializations.all()

    @staticmethod
    def resolve_qualifications(root, info, **kwargs):
        return root.qualifications.all()

    @staticmethod
    def resolve_last_availability(root, info, **kwargs):
        last_availability = root.availabilities.order_by('-start_time').first()
        if last_availability:
            last_availability.period = round(
                last_availability.period.total_seconds() / 3600, 2)
        return last_availability

    @staticmethod
    def resolve_working_hours(root: models.Doctor, _info, **_kwargs):
        db_working_hours = root.working_hours.all()
        working_hours = {}
        for db_working_hour in db_working_hours:
            time_range = {
                "open_time": db_working_hour.open_time,
                "close_time": db_working_hour.close_time
            }
            if db_working_hour.day in working_hours:
                working_hours[db_working_hour.day].append(time_range)
            else:
                working_hours[db_working_hour.day] = [time_range]

        return [WorkingHour(day, open_time_ranges) for day, open_time_ranges
                in working_hours.items()]

    @staticmethod
    def resolve_license_number(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_license_number_public):
            return root.license_number
        return None

    @staticmethod
    def resolve_years_of_experience(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_years_of_experience_public):
            return root.years_of_experience
        return None

    @staticmethod
    def resolve_second_mobile_number(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_second_mobile_number_public):
            return root.second_mobile_number
        return None

    @staticmethod
    def resolve_social_links(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_social_links_public):
            return root.social_links
        return None

    @staticmethod
    def resolve_address(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_address_public):
            return root.address
        return None

    @staticmethod
    def resolve_national_id(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_national_id_public):
            return root.national_id
        return None

    @staticmethod
    def resolve_languages(root, info, **kwargs):
        if models.Doctor.objects.is_accessible_property(root.user,
                                                        root.is_languages_public):
            return root.languages.all()
        return None

    @staticmethod
    def resolve_availability_status(root: models.Doctor, info, **kwargs):

        current_time = timezone.now()

        current_availability = models.DoctorAvailability.objects \
            .filter(doctor_id=root.id,
                    start_time__lte=current_time,
                    end_time__gte=current_time).first()
        if current_availability:
            return current_availability.status

        return DoctorAvailabilityStatuses.UNAVAILABLE

    @staticmethod
    def resolve_photo(root: models.Doctor, info, size=None, **_kwargs):
        if root.photo:
            return Image.get_adjusted(
                image=root.photo,
                alt="",
                size=size,
                rendition_key_set="doctor-photos",
                info=info,
            ).url

    @staticmethod
    def resolve_experiences(root, info, **kwargs):
        return root.experiences.all()

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Doctor)


@key(fields="code")
class DoctorSpecialization(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Specialization"
        interfaces = [relay.Node]
        model = models.DoctorSpecialization
        exclude = [
            "doctor"
        ]


@key(fields="code")
class HealthSymptomSpecialization(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Specialization"
        interfaces = [relay.Node]
        model = models.HealthSymptomSpecialization


@key(fields="id")
class Qualification(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Qualification"
        interfaces = [relay.Node]
        model = models.Qualification

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Qualification)


class DoctorAvailability(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual DoctorAvailability"
        interfaces = [relay.Node]
        model = models.DoctorAvailability


class DoctorAvailabilitiesSum(graphene.ObjectType):
    class Meta:
        description = "Represents sum of availability audits of a doctor"

    doctor = graphene.ID()
    sum_of_availabilities = graphene.Float()


class HealthSymptom(CountableDjangoObjectType):
    specializations = graphene.List(HealthSymptomSpecialization)

    class Meta:
        description = "Represents an individual HealthSymptom"
        interfaces = [relay.Node]
        model = models.HealthSymptom

    @staticmethod
    def resolve_specializations(root: models.HealthSymptom, info, **kwargs):
        return root.specializations.all()


class DoctorExperience(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Experience"
        interfaces = [relay.Node]
        model = models.DoctorExperience
        fields = [
            "id",
            "job_title",
            "description",
            "provider",
            "start_date",
            "end_date",
            "doctor",
        ]


class DoctorLastVisit(CountableDjangoObjectType):
    class Meta:
        description = "Represents an doctor Last Visit"
        model = models.DoctorActivityTracker
        fields = [
            "id",
            "user",
            "activity_date",
            "count"
        ]


class DoctorSpecialityInput(graphene.InputObjectType):
    code = graphene.String(description="Speciality Code.")
