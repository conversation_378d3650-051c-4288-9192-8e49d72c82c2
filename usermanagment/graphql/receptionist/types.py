from graphene import relay

from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.receptionist import models


class Receptionist(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Receptionist"
        model = models.Receptionist
        interfaces = [relay.Node]
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]
        only_fields = [
            "id",
            "user",
            "created",
            "modified",
        ]

    @staticmethod
    def resolve_user(root, info, **kwargs):
        return root.user
