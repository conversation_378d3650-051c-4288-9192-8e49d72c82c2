import graphene

from .filters import PlanFilterInput, SubscriptionFilterInput
from .resolvers import resolve_plans, resolve_subscriptions
from .sorters import PlanOrder
from .types import VendorSubscription, Plan
from ..core.fields import FilterInputConnectionField
from ..core.utils import str_to_enum
from ..subscription.mutations import PlanCreate, PlanUpdate
from ...auth.decorators import permission_required, is_authenticated, \
    one_of_permissions_required
from ...auth.permissions import SubscriptionPermissions
from ...subscription.enums import Period


class PlanQueries(graphene.ObjectType):
    plan = graphene.Field(
        Plan,
        id=graphene.Argument(graphene.ID, description="ID of the plan.", ),
        description="Look up a plan by ID.",
    )

    plans = FilterInputConnectionField(
        Plan,
        filter=PlanFilterInput(description="Filtering options for plans."),
        sort_by=PlanOrder(description="Sort plans."),
        description="List of plans.",
    )

    vendor_subscription = graphene.Field(
        VendorSubscription,
        id=graphene.Argument(graphene.ID,
                             description="ID of the vendor subscription.", ),
        description="Look up a vendor subscription by ID.",
    )

    vendor_subscriptions = FilterInputConnectionField(
        VendorSubscription,
        filter=SubscriptionFilterInput(
            description="Filtering options for vendor subscriptions."),
        description="List of subscriptions.",
    )
    plan_periods = graphene.List(
        graphene.String,
        description="Plan periods"
    )

    @is_authenticated()
    def resolve_plan(self, info, id=None):
        return graphene.Node.get_node_from_global_id(info, id, Plan)

    @is_authenticated()
    def resolve_plans(self, info, **kwargs):
        return resolve_plans(info, **kwargs)

    def resolve_plan_periods(self, info, **kwargs):
        return [str_to_enum(choice[0]) for choice in Period.CHOICES]

    @one_of_permissions_required([SubscriptionPermissions.MANAGE_SUBSCRIPTIONS,
                                  SubscriptionPermissions.VIEW_SUBSCRIPTIONS])
    def resolve_vendor_subscription(self, info, id=None):
        return graphene.Node.get_node_from_global_id(info, id, VendorSubscription)

    @one_of_permissions_required([SubscriptionPermissions.MANAGE_SUBSCRIPTIONS,
                                  SubscriptionPermissions.VIEW_SUBSCRIPTIONS])
    def resolve_vendor_subscriptions(self, info, **kwargs):
        return resolve_subscriptions(info, **kwargs)


class PlanMutations(graphene.ObjectType):
    plan_create = PlanCreate.Field()
    plan_update = PlanUpdate.Field()
