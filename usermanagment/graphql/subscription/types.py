import graphene
from graphene_federation import key

from ..core.connection import CountableDjangoObjectType
from ...subscription import models


@key(fields="id")
class Plan(CountableDjangoObjectType):
    is_editable = graphene.Boolean(description="represent if this plan is editable")

    class Meta:
        description = "Represents a plan"
        interfaces = [graphene.relay.Node]
        model = models.Plan

    @staticmethod
    def resolve_is_editable(root: models.Plan, _info, **kwargs):
        return root.subscriptions.filter(is_active=True).count() == 0


@key(fields="id")
class VendorSubscription(CountableDjangoObjectType):
    class Meta:
        description = "Represents a vendor subscription"
        interfaces = [graphene.relay.Node]
        model = models.Subscription
