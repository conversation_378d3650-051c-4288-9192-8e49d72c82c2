import graphene
from django.core.exceptions import ValidationError
from django.utils import timezone

from .enums import PeriodEnum
from ..core.mutations import ModelMutation
from ..core.scalars import Decimal
from ..core.types.common import SubscriptionError
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import SubscriptionPermissions
from ...subscription import models


class PlanInput(graphene.InputObjectType):
    name = graphene.String(description="Plan Name.")
    valid_from = graphene.DateTime(description="The date the plan is introduced.")
    valid_till = graphene.DateTime(description="The validity of the plan")
    fixed_cost_amount = Decimal(description="Plan fixed cost")
    fixed_order_cost_amount = Decimal(description="Plan fixed cost per order")
    fixed_order_percentage = Decimal(description="Plan percentage cut of the order")
    is_active = graphene.Boolean(
        description="Determine if plan will be set active or not."
    )
    description = graphene.String(description="Plan description.")
    period = PeriodEnum(description="Plan period")


class PlanCreate(ModelMutation):
    class Arguments:
        input = PlanInput(required=True,
                          description="Fields required to create a vendor.")

    class Meta:
        description = "Creates a new plan."
        model = models.Plan
        permissions = (SubscriptionPermissions.MANAGE_SUBSCRIPTIONS,)
        error_type_class = SubscriptionError
        error_type_field = "subscription_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_superuser:
            raise PermissionDenied()
        return {}

    @classmethod
    def validate_from_to_date(cls, info, instance, data):
        valid_from = data.get("valid_from", None)
        valid_till = data.get("valid_till", instance.valid_till)
        now = timezone.now()

        if (not instance.pk and (not valid_from or valid_from < now)) \
                or (instance.pk and valid_from and valid_from < now):
            raise ValidationError({
                "valid_from": "should not be empty or in the past"})

        valid_from = valid_from if valid_from else instance.valid_from

        if valid_till and valid_till <= valid_from:
            raise ValidationError({
                "valid_till": "should be greater than valid_from"})

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):

        if instance.pk and instance.subscriptions.filter(is_active=True):
            fields_names = [field.name for field in instance._meta.fields]
            fields_names.remove('is_active')
            for field_name in fields_names:
                if data.get(field_name):
                    raise ValidationError(
                        "you can't edit plan with active subscriptions")

        cleaned_input = super().clean_input(info, instance, data)
        cls.validate_from_to_date(info, instance, cleaned_input)

        # fixed_cost = cleaned_input.get("fixed_cost_amount", instance.fixed_cost_amount)
        # fixed_order_cost = cleaned_input.get("fixed_order_cost_amount", instance.fixed_order_cost_amount)
        # fixed_order_percentage = cleaned_input.get("fixed_order_percentage", instance.fixed_order_percentage)
        # if not (fixed_cost or fixed_order_cost or fixed_order_percentage):
        #     raise ValidationError(
        #         {'fixedCostAmount/fixedOrderCostAmount/fixedOrderPercentage':
        #              "please provide at least one of the paramaters"})
        return cleaned_input


class PlanUpdate(PlanCreate):
    class Arguments:
        id = graphene.ID(required=True, description="ID of the plan to update.")
        input = PlanInput(required=True,
                          description="Fields required to update a plan.")

    class Meta:
        description = "Updates an existing plan."
        model = models.Plan
        permissions = (SubscriptionPermissions.MANAGE_SUBSCRIPTIONS,)
        error_type_class = SubscriptionError
        error_type_field = "subscription_errors"


class SubscriptionInput(graphene.InputObjectType):
    plan = graphene.ID(description="Determine plan object.")
    vendor = graphene.ID(description="Determine vendor object.")
    fixed_cost_amount = Decimal(description="Plan fixed cost")
    fixed_order_cost_amount = Decimal(description="Plan fixed cost per order")
    fixed_order_percentage = Decimal(description="Plan percentage cut of the order")
