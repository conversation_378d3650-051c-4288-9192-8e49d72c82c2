import django_filters

from ..core.types import FilterInputObjectType
from ...subscription import models


class PlanFilter(django_filters.FilterSet):
    class Meta:
        model = models.Plan
        fields = {
            "name": ["exact", "icontains"],
            "is_active": ["exact"],
        }


class PlanFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = PlanFilter


class SubscriptionFilter(django_filters.FilterSet):
    class Meta:
        model = models.Subscription
        fields = [
            "valid_from",
            "valid_till",
        ]


class SubscriptionFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = SubscriptionFilter
