from collections import namedtuple
from datetime import timedelta

from dateutil.relativedelta import relativedelta

from ...subscription.enums import Period
from ...subscription.models import Subscription


def get_subscription_fixed_costs(subscription: Subscription, period: str, date_from,
                                 date_till):
    fixed_costs = 0

    Range = namedtuple('Range', ['start', 'end'])

    valid_from = subscription.valid_from.date()
    valid_till = subscription.valid_till.date() if subscription.valid_till else None

    def is_date_ranges_intersected(range1, range2):
        delta = min(range1.end, range2.end) - max(range1.start, range2.start)
        return max(0, delta.days)

    period_till = valid_from

    while True:
        period_from = period_till
        period_till = add_plan_period(period_from, period)

        inside_subscription = period_from < date_till and period_from <= valid_till if valid_till else period_from < date_till
        if not inside_subscription:
            return fixed_costs

        if is_date_ranges_intersected(
                range1=Range(start=period_from, end=period_till),
                range2=Range(start=date_from, end=date_till)
        ):
            fixed_costs += subscription.fixed_cost_amount


def add_plan_period(valid_from, period):
    if period == Period.WEEkLY:
        return valid_from + timedelta(weeks=1)
    if period == Period.MONTHLY:
        return valid_from + relativedelta(months=1)
    if period == Period.THREE_MONTHS:
        return valid_from + relativedelta(months=3)
    if period == Period.HALF_YEAR:
        return valid_from + timedelta(days=365 // 2)
    if period == Period.YEARLY:
        return valid_from + relativedelta(years=1)
