import graphene

from ..core.types import SortInputObjectType


class PlanOrderField(graphene.Enum):
    NAME = ["name"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            PlanOrderField.NAME.name: "name",
        }
        if self.name in descriptions:
            return f"Sort plans by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.value)


class PlanOrder(SortInputObjectType):
    field = graphene.Argument(
        PlanOrderField, description="Sort plans by the selected field."
    )

    class Meta:
        sort_enum = PlanOrderField
