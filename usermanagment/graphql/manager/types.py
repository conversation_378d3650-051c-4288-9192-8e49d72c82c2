from graphene import relay

from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.manager import models


class Manager(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Manager"
        model = models.Manager
        interfaces = [relay.Node]
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]
        only_fields = [
            "id",
            "user",
            "created",
            "modified",
        ]

    @staticmethod
    def resolve_user(root, info, **kwargs):
        return root.user
