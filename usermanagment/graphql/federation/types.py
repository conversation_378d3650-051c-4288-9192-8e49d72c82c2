import graphene
from graphene_federation import extend, external
from usermanagment.graphql.utils import get_database_id


class BaseFederatedType(graphene.ObjectType):
    id = external(graphene.ID(required=True))

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'id' in kwargs:
            self.pk = get_database_id(kwargs['id'], str(self.__class__))


@extend(fields='id')
class Category(BaseFederatedType):
    pass


@extend(fields='id')
class Order(BaseFederatedType):
    pass
