import graphene
from graphene import Node
from graphene.utils.str_converters import to_snake_case


def external_field_resolver(instance, info, field_name=None):
    if not field_name:
        field_name = f'{to_snake_case(info.field_name)}_id'

    if getattr(instance, field_name, None) is not None:
        return info.return_type.graphene_type(
            id=Node.to_global_id(str(info.return_type.graphene_type),
                                 getattr(instance, field_name)))

    return None


class ExternalObjectField(graphene.Field):
    def __init__(self, model, description=None):
        super().__init__(
            model,
            description=description,
            resolver=external_field_resolver,
        )
        self._external = True
