import graphene

from .filters import VendorFilterInput, VendorDepartmentFilterInput
from .resolvers import resolve_vendors, resolve_divisions
from .sorters import VendorOrder, VendorDepartmentOrder
from .types import Vendor, Division, Department
from ..core.fields import FilterInputConnectionField
from ..core.types.common import LocationInput
from ..utils.request_utils import get_current_user
from ..vendor.mutations import VendorCreate, VendorUpdate, RejectVendor, \
    VendorRegister, ApproveVendor, VendorDelete, \
    AddVendorToFavorite, RemoveVendorFromFavorite, \
    TerminateVendorSubscription, VendorImageCreate, VendorImageUpdate, \
    VendorImageDelete, VendorImageReorder, VendorUpdateIsIntegrated, \
    AvailabilitiesAverage, ReviewVendor, VendorReviewDelete, \
    VendorReviewPublish, MyReviewForVendor, VendorDepartmentCreate, \
    VendorDepartmentUpdate, VendorDepartmentDelete, VendorUpdateEnableVisitDetails, \
    VendorActiveStatusUpdate, VendorSupportOutpatientJourneyStatusUpdate
from ...vendor import models


class VendorQueries(graphene.ObjectType):
    vendor = graphene.Field(
        Vendor,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the vendor.",
            required=True
        ),
        description="Look up a vendor by ID.",
    )

    vendors = FilterInputConnectionField(
        Vendor,
        user_location=LocationInput(),
        filter=VendorFilterInput(description="Filtering options for vendors."),
        sort_by=VendorOrder(description="Sort vendors."),
        description="List of the shop's vendors.",
    )

    division = graphene.Field(
        Division,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the Division.",
            required=True
        ),
        description="Look up a Division by ID.",
    )

    divisions = FilterInputConnectionField(
        Division,
        description="List of the Divisions.",
    )

    vendor_department = graphene.Field(
        Department,
        id=graphene.Argument(
            graphene.ID,
            description="ID of the Department.",
            required=True
        ),
        description="Look up a Department by ID."
    )

    vendor_departments = FilterInputConnectionField(
        Department,
        description="List of the Departments.",
        filter=VendorDepartmentFilterInput(
            description="Filtering options for Departments."),
        sort_by=VendorDepartmentOrder(description="Sort Departments."),

    )

    @staticmethod
    def resolve_vendor(_root, info, **kwargs):
        return resolve_vendors(info, **kwargs)

    @staticmethod
    def resolve_vendors(_root, info, **kwargs):
        return resolve_vendors(info, **kwargs)

    @staticmethod
    def resolve_division(_root, info, **kwargs):
        return resolve_divisions(info, **kwargs)

    @staticmethod
    def resolve_divisions(_root, info, **kwargs):
        return resolve_divisions(info, **kwargs)

    @staticmethod
    def resolve_vendor_department(_root, info, **kwargs):
        return graphene.Node.get_node_from_global_id(info, kwargs['id'], Department)

    @staticmethod
    def resolve_vendor_departments(_root, info, **kwargs):
        qs = models.Department.objects
        current_user = get_current_user()
        if not current_user.is_superuser and not current_user.is_staff:
            vendor_id = current_user.vendor_id
            if vendor_id:
                if current_user.is_vendor_admin:
                    qs = qs.filter(branch__vendor_id=current_user.vendor_id)
                else:
                    qs = qs.filter(branch_id__in=current_user.branches.all())
        return qs.all()


class VendorMutations(graphene.ObjectType):
    vendor_create = VendorCreate.Field()
    vendor_update = VendorUpdate.Field()
    vendor_delete = VendorDelete.Field()
    vendor_update_is_integrated = VendorUpdateIsIntegrated.Field()
    vendor_update_enable_visit_details = VendorUpdateEnableVisitDetails.Field()


    vendor_active_status_update = VendorActiveStatusUpdate.Field()
    vendor_support_outpatient_journey_status_update= VendorSupportOutpatientJourneyStatusUpdate.Field()

    vendor_register = VendorRegister.Field()

    reject_vendor = RejectVendor.Field()
    approve_vendor = ApproveVendor.Field()

    add_vendor_to_favorite = AddVendorToFavorite.Field()
    remove_vendor_from_favorite = RemoveVendorFromFavorite.Field()

    terminate_vendor_subscription = TerminateVendorSubscription.Field()

    vendor_image_create = VendorImageCreate.Field()
    vendor_image_update = VendorImageUpdate.Field()
    vendor_image_delete = VendorImageDelete.Field()
    vendor_image_reorder = VendorImageReorder.Field()

    availabilities_average = AvailabilitiesAverage.Field()

    review_vendor = ReviewVendor.Field()
    vendor_review_delete = VendorReviewDelete.Field()
    vendor_review_publish = VendorReviewPublish.Field()
    my_review_for_vendor = MyReviewForVendor.Field()

    vendor_department_create = VendorDepartmentCreate.Field()
    vendor_department_update = VendorDepartmentUpdate.Field()
    vendor_department_delete = VendorDepartmentDelete.Field()
