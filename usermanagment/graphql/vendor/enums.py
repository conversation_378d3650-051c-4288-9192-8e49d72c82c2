import graphene

from ..core.utils import str_to_enum
from ...vendor import enums

DayOfWeekEnum = graphene.Enum(
    "DayOfWeekEnum",
    [(str_to_enum(day[0]), day[0]) for day in enums.DayOfWeekEnum.CHOICES],
)

PriceRangeEnum = graphene.Enum(
    "PriceRangeEnum",
    [(str_to_enum(rng[0]), rng[0]) for rng in enums.PriceRangeEnum.CHOICES],
)

VendorTypeEnum = graphene.Enum(
    "VendorTypeEnum",
    [(str_to_enum(rng[0]), rng[0]) for rng in enums.VendorTypes.choices],
)

VendorBranchTypeEnum = graphene.Enum(
    "VendorBranchTypeEnum",
    [(str_to_enum(val[0]), val[0]) for val in enums.VendorBranchTypes.choices],
)
