import graphene

from .types import Vendor, Division
from ...vendor import models


def resolve_vendors(info, **kwargs):
    if "id" in kwargs:
        return graphene.Node.get_node_from_global_id(info, kwargs['id'], Vendor)

    return models.Vendor.objects.all() \
        .accessible_by_user() \
        .distinct()


def resolve_divisions(info, **kwargs):
    if "id" in kwargs:
        return graphene.Node.get_node_from_global_id(info, kwargs['id'], Division)

    qs = models.Division.objects.all()

    return qs.distinct()
