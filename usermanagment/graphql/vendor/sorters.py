import graphene
from django.contrib.gis.geos import Point
from django.core.exceptions import ValidationError
from django.db.models import QuerySet, Min, ExpressionWrapper, DecimalField

from ..core.types import SortInputObjectType
from ..utils.request_utils import get_current_user
from ...map.postgis.functions import DistanceSphere


class VendorOrderField(graphene.Enum):
    NAME = ["name"]
    ORDER_COUNT = ["total_orders_count", "pk"]
    RATING = ["average_rating", "pk"]
    CREATED = ["created", "pk"]
    NEAREST = ["distance", "pk"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            VendorOrderField.NAME.name: "name",
            VendorOrderField.ORDER_COUNT.name: "ordersCount",
            VendorOrderField.RATING.name: "rating",
            VendorOrderField.CREATED.name: "creation date",
            VendorOrderField.NEAREST.name: "nearest to user",
        }
        if self.name in descriptions:
            return f"Sort vendors by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.name)

    @staticmethod
    def qs_with_nearest(queryset: QuerySet, args) -> QuerySet:
        value = args.get('user_location', {})
        try:
            user_location = Point(x=value.lng, y=value.lat, srid=4326)
        except Exception:
            current_user = get_current_user()
            if current_user.is_authenticated and current_user.default_shipping_address:
                user_location = current_user.default_shipping_address.location
            else:
                raise ValidationError({
                    "nearest": "please provide a valid user location"
                })

        return queryset.annotate(
            distance=Min(ExpressionWrapper(DistanceSphere(
                "branches__address__location",
                user_location,
            ), output_field=DecimalField())))


class VendorOrder(SortInputObjectType):
    field = graphene.Argument(
        VendorOrderField, description="Sort vendors by the selected field."
    )

    class Meta:
        sort_enum = VendorOrderField


class VendorDepartmentOrderField(graphene.Enum):
    NAME = ["name"]
    CODE = ["code"]
    BRANCH=["branch__name"]

    @property
    def description(self):
        # pylint: disable=no-member
        descriptions = {
            VendorDepartmentOrderField.NAME.name: "name",
            VendorDepartmentOrderField.CODE.name: "code",
            VendorDepartmentOrderField.BRANCH.name: "branch",
        }
        if self.name in descriptions:
            return f"Sort vendor departments by {descriptions[self.name]}."
        raise ValueError("Unsupported enum value: %s" % self.name)


class VendorDepartmentOrder(SortInputObjectType):
    field = graphene.Argument(
        VendorDepartmentOrderField,
        description="Sort vendor departments by the selected field."
    )

    class Meta:
        sort_enum = VendorDepartmentOrderField
