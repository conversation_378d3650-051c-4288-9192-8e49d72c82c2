from threading import Thread
import graphene
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import F
from django.db.models.aggregates import Sum, Count
from django.utils import timezone
from graphene import Node
from .enums import PriceRangeEnum, VendorTypeEnum, VendorBranchTypeEnum
from .types import Vendor, CustomerFavoriteVendor, VendorImage, VendorReview
from ..account.i18n import I18nMixin
from ..account.mutations.users import UserCreate
from ..account.types import AddressInput
from ..branch.mutations import BranchCreate
from ..core.health_license_date import HealthLicenseDatesMixin
from ..core.mutations import ModelMutation, ModelDeleteMutation, BaseMutation
from ..core.scalars import Decimal
from ..core.types.common import VendorError, WorkingHourInput, WorkingHourOverrideInput, \
    VendorDepartmentError
from ..core.utils import validate_slug_and_generate_if_needed, clean_seo_fields, \
    from_global_id_strict_type
from ..subscription.mutations import SubscriptionInput
from ..subscription.types import Plan
from ..utils import get_database_id
from ..utils.request_utils import get_current_user
from ...account import sms
from ...account.models import User
from ...auth.enums import AppTypes, AppRoleTypes, VendorUserTypes
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import VendorPermissions, BranchPermissions
from ...authorization import authorization
from ...authorization.authorization import AuthorizationDto
from ...core.validaters import generate_and_validate_password
from ...doctor.models import DoctorAvailability, Doctor
from ...graphql_client.backend_client import BackendClient
from ...subscription import models as subscription_models
from ...subscription.error_codes import SubscriptionErrorCode
from ...vendor import models
from ...vendor.enums import ALLOWED_VENDOR_BRANCH_TYPES, VendorBranchTypes
from ...vendor.error_codes import VendorErrorCode


class VendorBankInfoInput(graphene.InputObjectType):
    bank_name = graphene.String(description="bank Name.")
    account_number = graphene.String(description="account number.")
    iban = graphene.String(description="IBAN.")
    account_name = graphene.String(description="Account Name.")


class VendorManagersContactInfoInput(graphene.InputObjectType):
    general_manager_email = graphene.String(description="managers contact info")
    purchasing_manager_name = graphene.String(description="managers contact info")
    purchasing_manager_mobile_number = graphene.String(
        description="managers contact info")
    purchasing_manager_email = graphene.String(description="managers contact info")
    financial_manager_name = graphene.String(description="managers contact info")
    financial_manager_mobile_number = graphene.String(
        description="managers contact info")
    financial_manager_email = graphene.String(description="managers contact info")


class VendorBaseInput(graphene.InputObjectType):
    name = graphene.String(description="Vendor Name.")
    name_ar = graphene.String(description="Vendor Name arabic")
    support_immediate_call = graphene.Boolean(
        description="Determine if vendor support immediate call or not.")
    description = graphene.String(description="Vendor description")
    logo = graphene.String(
        required=False,
        description="Represents an image file",
    )
    back_ground_image = graphene.String(
        required=False,
        description="Represents an image file",
    )
    commercial_registration_number = graphene.String(
        description="Vendor commercial registration number.")
    owner_name = graphene.String(description="Vendor owner Name.")
    trade_name = graphene.String(description="Vendor trade Name.")
    tax_license_number = graphene.String(description="Vendor tax license number.")
    national_id = graphene.String(description="Vendor owner national id.")
    contact_mobile_number = graphene.String(
        description="Vendor contact mobile number.")
    contact_phone_number = graphene.String(
        description="Vendor contact number that displayed to customers.")
    bank_info = VendorBankInfoInput(description="Vendor bank info.")
    has_multiple_branches = graphene.Boolean(
        description="Determine if vendor has more than one branch")
    branch_working_hours = graphene.List(
        WorkingHourInput, description="Branch Working Hours")
    branch_working_hours_override = graphene.List(
        WorkingHourOverrideInput, description="Branch Working Hours override")
    branch_type = VendorBranchTypeEnum(description="Branch Type")
    health_license_number = graphene.String(description="Branch Health License Number")
    health_license_start_date = graphene.Date(
        description="Branch Health License Start Date")
    health_license_end_date = graphene.Date(
        description="Branch Health License End Date")
    accepts_delivery = graphene.Boolean(description="Accepts Delivery")
    accepts_pickup = graphene.Boolean(description="Accepts Pickup")
    address_input = AddressInput(description="Address of the vendor.")

    delivery_min_from = graphene.Int(
        description="Returns order minimum delivery time required")
    delivery_min_to = graphene.Int(
        description="Returns order maximum delivery time required")
    price_range = PriceRangeEnum(description="Price range ")
    type = VendorTypeEnum(description="Vendor Type")
    managers_contact_info = VendorManagersContactInfoInput(description="Managers Info")
    is_live_booking_integrated = graphene.Boolean(description="Is Integrated")
    notify_by_email = graphene.Boolean(description="Notify by email")
    delivery_price = graphene.Int(description="Delivery Price", required=False)
    has_own_payment_gateway = graphene.Boolean(description="Has Own Payment Gateway")

class VendorCreateInput(graphene.InputObjectType):
    mobile_number = graphene.String(description="Vendor admin user mobile number.",
                                    required=True)
    email = graphene.String(description="the email address of the Vendor admin user",
                            required=True)

    support_outpatient_journey = graphene.Boolean(required=True, description=
    "support outpatient journey")


class AdminInput(graphene.InputObjectType):
    slug = graphene.String(description="Vendor Slug.")
    is_active = graphene.Boolean(
        description="Determine if vendor will be set active or not.")
    is_vip = graphene.Boolean(
        description="Determine if vendor will be VIP or not.")
    subscription = SubscriptionInput(description="fields required for subscription")
    has_own_drivers = graphene.Boolean(
        description="Determine if vendor will use system drivers or not")

    authority_code = graphene.String()
    operation_status = graphene.String()
    cluster = graphene.String()
    group = graphene.String()
    virtual_group = graphene.String()
    source = graphene.String()
    org_id_nhic = graphene.String()
    max_number_of_users = graphene.Int()
    call_doctor_now_platform_share = graphene.Float()
    is_integrated = graphene.Boolean()
    enable_visit_details = graphene.Boolean()
    edit_like_enabled = graphene.Boolean()


class VendorUpdateAdminInput(VendorBaseInput, AdminInput):
    support_outpatient_journey = graphene.Boolean(required=False, description=
    "Support outpatient journey")


class VendorAdminInput(VendorBaseInput, VendorCreateInput, AdminInput):
    pass


class VendorInput(VendorBaseInput, VendorCreateInput):
    pass


class VendorRejectionReasonInput(graphene.InputObjectType):
    reason = graphene.String(description="rejection reason", required=True)
    vendor = graphene.ID(description="ID of the rejected vendor", required=True)


class VendorReviewCreateInput(graphene.InputObjectType):
    rating = graphene.Int(
        description="Rating of the vendor.",
        required=True,
    )
    content = graphene.String(
        description="Content of the vendor review.",
        required=False,
    )
    vendor = graphene.ID(
        description="ID of the vendor to be reviewed.",
        required=True,
    )


class VendorReviewPublishInput(graphene.InputObjectType):
    published = graphene.Boolean(
        description="Publish the vendor review.",
        required=True,
    )


class VendorDepartmentCreateInput(graphene.InputObjectType):
    name = graphene.String(description="Name of the department.", required=True)
    code = graphene.String(description="Code of the department.", required=True)
    description = graphene.String(
        description="Description of the department.",
        required=False,
    )
    directions = graphene.String(
        description="Directions of the department.",
        required=False,
    )
    medical_services = graphene.List(
        graphene.String,
        description="Medical services of the department.",
        required=False,
    )
    branch = graphene.ID(
        description="ID of the vendor which the department belongs to.",
        required=True,
    )


class VendorDepartmentUpdateInput(graphene.InputObjectType):
    name = graphene.String(description="Name of the department.", required=False)
    code = graphene.String(description="Code of the department.", required=False)
    description = graphene.String(
        description="Description of the department.",
        required=False,
    )
    directions = graphene.String(
        description="Directions of the department.",
        required=False,
    )
    medical_services = graphene.List(
        graphene.String,
        description="Medical services of the department.",
        required=False,
    )


class VendorSupportOutpatientJourneyStatusUpdate(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor to update.")
    class Meta:
        description = "Creates a new vendor."
        model = models.Vendor
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"
    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data)
        instance.support_outpatient_journey = not instance.support_outpatient_journey
        return cleaned_input



class VendorActiveStatusUpdate(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor to update.")
    class Meta:
        description = "Creates a new vendor."
        model = models.Vendor
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"
    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_input = super().clean_input(info, instance, data)
        instance.is_active = not instance.is_active
        return cleaned_input



class VendorCreate(ModelMutation, I18nMixin, HealthLicenseDatesMixin):
    class Arguments:
        input = VendorAdminInput(required=True,
                                 description="Fields required to create a vendor.")

    class Meta:
        description = "Creates a new vendor."
        model = models.Vendor
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def validate_default_branch_working_hours(cls, info, instance, data):
        branch_working_hours = data.get("branch_working_hours", None)
        branch_working_hours_override = data.get("branch_working_hours_override", None)
        if branch_working_hours or branch_working_hours_override:
            branches_count = instance.branches.count()
            if branches_count > 1:
                raise ValidationError({
                    "branchWorkingHours": "you cannot set branchWorkingHours and "
                                          "branchWorkingHoursOverride, "
                                          "this vendor has multiple branches"})

            BranchCreate.validate_working_hours(info,
                                                {"working_hours": branch_working_hours})
            BranchCreate.validate_working_hours_override(info, {
                "working_hours_override": branch_working_hours_override})

    @classmethod
    def clean_input(cls, info, instance, data):
        vendor_register = data.pop('vendor_register', False)
        cleaned_input = super().clean_input(info, instance, data)
        cleaned_input['vendor_register'] = vendor_register
        try:
            cleaned_input = validate_slug_and_generate_if_needed(
                instance, "name", cleaned_input
            )
        except ValidationError as error:
            error.code = VendorErrorCode.REQUIRED.value
            raise ValidationError({"slug": error})

        clean_seo_fields(cleaned_input)

        address_data = data.pop("address_input", None)
        if address_data:
            cleaned_input["address"] = cls.construct_address_instance(info,
                                                                      address_data,
                                                                      instance)

        cls.validate_default_branch_working_hours(info, instance, data)

        cls.validate_branch_type(cleaned_input)
        cls.validate_health_licence_dates(info, models.Branch(name="fake"), data)

        cleaned_input['subscription'] = cls.validate_subscription_data(info, instance,
                                                                       data)

        cls.validate_call_doctor_now_platform_share(cleaned_input, instance)

        return cleaned_input

    @classmethod
    def validate_call_doctor_now_platform_share(cls, cleaned_input, instance):
        call_doctor_now_platform_share = cleaned_input.get("call_doctor_now_platform_share", instance.call_doctor_now_platform_share)
        current_user = get_current_user()
        if call_doctor_now_platform_share:
            if not current_user.is_superuser:
                raise ValidationError(
                    {"call_doctor_now_platform_share": "Call Doctor Now Platform Share can only be filled by ADMIN"})
            if call_doctor_now_platform_share < 0 or call_doctor_now_platform_share > 100:
                raise ValidationError(
                    {"call_doctor_now_platform_share": "Call Doctor Now Platform Share must be between 0 and 100"})

    @classmethod
    def validate_branch_type(cls, cleaned_input):
        branch_type = cleaned_input.get("branch_type", None)
        vendor_type = cleaned_input.get("type", None)

        if (branch_type and vendor_type) and branch_type not in \
                ALLOWED_VENDOR_BRANCH_TYPES[vendor_type]:
            raise ValidationError(
                {"branch_type": "Branch type is not allowed for this vendor type."}
            )

    @classmethod
    def save_bank_info(cls, info, instance, cleaned_data):
        cleaned_bank_info_data = cleaned_data.get("bank_info")
        if cleaned_bank_info_data:
            try:
                bank_info_instance = instance.bank_info
            except models.VendorBankInfo.DoesNotExist:
                bank_info_instance = models.VendorBankInfo(vendor=instance)
            bank_info_instance = super().construct_instance(bank_info_instance,
                                                            cleaned_bank_info_data)
            super().clean_instance(info, bank_info_instance)
            bank_info_instance.save()

    @classmethod
    def save_managers_contact_info(cls, info, instance, cleaned_data):
        cleaned_managers_contact_info = cleaned_data.get("managers_contact_info")
        if cleaned_managers_contact_info:
            obj, _ = models.VendorManagersContactInfo.objects.update_or_create(
                vendor=instance,
                defaults=cleaned_managers_contact_info
            )
            super().clean_instance(info, obj)

    @classmethod
    def save_default_branch_working_hours(cls, info, instance, cleaned_data):

        branch_type = cleaned_data.pop("branch_type", None)
        health_license_number = cleaned_data.pop("health_license_number", None)
        health_license_start_date = cleaned_data.pop("health_license_start_date", None)
        health_license_end_date = cleaned_data.pop("health_license_end_date", None)
        accepts_delivery = cleaned_data.pop("accepts_delivery", True)
        accepts_pickup = cleaned_data.pop("accepts_pickup", True)

        branch_working_hours = cleaned_data.get("branch_working_hours", None)
        branch_working_hours_override = cleaned_data.get(
            "branch_working_hours_override", None)
        if instance.branches.count() <= 1:
            default_branch = instance.branches.first()
            if not default_branch:
                branch_name = instance.name
                default_branch = models.Branch(name=branch_name)
                default_branch.name_ar = instance.name_ar
                default_branch.vendor = instance
                default_branch.description = instance.description
                default_branch.type = ALLOWED_VENDOR_BRANCH_TYPES[instance.type][0]
                if branch_type:
                    default_branch.type = branch_type
                default_branch.address = instance.address.get_copy()
                default_branch.contact_number = instance.contact_phone_number
                default_branch.contact_email = instance.contact_email
                default_branch.is_active = False
                default_branch.health_license_number = health_license_number
                default_branch.health_license_start_date = health_license_start_date
                default_branch.health_license_end_date = health_license_end_date
                default_branch.accepts_delivery = accepts_delivery
                default_branch.accepts_pickup = accepts_pickup
                default_branch.full_clean()
                default_branch.save()

            if branch_working_hours:
                BranchCreate.save_working_hours(info, default_branch,
                                                {"working_hours": branch_working_hours},
                                                models.BranchWorkingHour,
                                                'branch_id'
                                                )
            if branch_working_hours_override:
                BranchCreate.save_working_hours_override(
                    info, default_branch,
                    {"working_hours_override": branch_working_hours_override},
                    models.BranchWorkingHourOverride,
                    'branch_id')

    @classmethod
    def save_subscription(cls, info, instance, cleaned_data):
        subscription = cleaned_data.get('subscription')
        if subscription:
            active_subscriptions = instance.subscriptions.filter(is_active=True).all()
            for active_subscription in active_subscriptions:
                active_subscription.is_active = False
                active_subscription.valid_till = timezone.now()
                active_subscription.save(update_fields=['is_active', 'valid_till'])

            subscription.is_active = True
            subscription.vendor = instance
            subscription.valid_from = timezone.now()
            subscription.save()

    @classmethod
    def _save_m2m(cls, info, instance, cleaned_data):
        super()._save_m2m(info, instance, cleaned_data)

        cls.save_bank_info(info, instance, cleaned_data)
        cls.save_default_branch_working_hours(info, instance, cleaned_data)
        cls.save_subscription(info, instance, cleaned_data)
        cls.save_managers_contact_info(info, instance, cleaned_data)

        if cleaned_data.get('mobile_number', None):
            password = generate_and_validate_password()
            user_create_result = UserCreate().perform_mutation(None, info, **{
                "input": {
                    "first_name": cleaned_data.get('owner_name'),
                    "last_name": "Vendor",
                    # TODO [MOA] review this for production
                    "password": password,
                    "mobile": cleaned_data.pop('mobile_number'),
                    "email": cleaned_data.pop('email'),
                    "app_type": AppTypes.VENDOR,
                    "app_role": AppRoleTypes.ADMIN,
                    "default_branch": Node.to_global_id("Branch", instance.branches.first().pk),
                    "is_active": True,
                    "vendor": Node.to_global_id("Vendor", instance.pk),
                    "vendor_register": cleaned_data.pop('vendor_register', False),
                    "national_id": cleaned_data.pop("national_id", None),
                    "vendor_user_type": VendorUserTypes.MANAGER
                }
            })

            if not user_create_result.user:
                raise ValidationError(user_create_result.errors)

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        return super().perform_mutation(_root, info, **data)

    @classmethod
    def validate_subscription_data(cls, info, instance, data):
        subscription = data.pop("subscription", None)

        if not subscription:
            return

        plan_id = subscription.pop('plan', None)
        if not plan_id:
            raise ValidationError(
                {'plan': ValidationError(
                    code=SubscriptionErrorCode.REQUIRED.name,
                    message="plan is required for subscription"
                )}
            )

        plan = cls.get_node_or_error(info, plan_id, field="subscription.plan",
                                     only_type=Plan)
        subscription['plan'] = plan

        fixed_cost = subscription.get("fixed_cost_amount")
        fixed_order_cost = subscription.get("fixed_order_cost_amount")
        fixed_order_percentage = subscription.get("fixed_order_percentage")
        if not (fixed_cost or fixed_order_cost or fixed_order_percentage):
            raise ValidationError(
                {'fixedCostAmount/fixedOrderCostAmount/fixedOrderPercentage':
                     "please provide at least one of the parameters"})

        subscription['fixed_cost_amount'] = fixed_cost or plan.fixed_cost_amount
        subscription[
            'fixed_order_cost_amount'] = fixed_order_cost or plan.fixed_order_cost_amount
        subscription[
            'fixed_order_percentage'] = fixed_order_percentage or plan.fixed_order_percentage

        if not instance.pk or instance.subscriptions.filter(is_active=True,
                                                            **subscription).count() == 0:
            return subscription_models.Subscription(**subscription)

        return None

    @classmethod
    def clean_instance(cls, info, instance):
        # if not instance.has_multiple_branches and instance.branches.count() > 1:
        #     raise ValidationError({
        #         "hasMultipleBranches": "can't be disabled while you have multiple branches"
        #     })
        super().clean_instance(info, instance)


class VendorUpdate(VendorCreate):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor to update.")
        input = VendorUpdateAdminInput(required=True,
                                       description="Fields required to update a vendor.")

    class Meta:
        description = "Updates an existing vendor."
        model = models.Vendor
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_admin and \
                not authorization.can_manage_vendor(info.context.user, data["id"]):
            raise PermissionDenied()

        return {}


class VendorUpdateIsIntegratedInput(graphene.InputObjectType):
    is_integrated = graphene.Boolean(description="Boolean is Integrated", required=True)


class VendorUpdateEnableVisitDetailsInput(graphene.InputObjectType):
    enable_visit_details = graphene.Boolean(description="Boolean enable Visit Details", required=True)

class VendorUpdateIsIntegrated(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor to update.")
        input = VendorUpdateIsIntegratedInput(required=True,
                                              description="Fields required to update a vendor.")

    class Meta:
        description = "Updates an existing vendor."
        model = models.Vendor
        permissions = (
            BranchPermissions.MANAGE_BRANCHES, VendorPermissions.MANAGE_VENDORS)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        for permission in cls._meta.permissions:
            if context.user.has_perm(permission):
                return True
        raise PermissionDenied()

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_admin and \
                not authorization.can_manage_vendor(info.context.user, data["id"]):
            raise PermissionDenied()

        return {}


class VendorUpdateEnableVisitDetails(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor to update.")
        input = VendorUpdateEnableVisitDetailsInput(required=True,
                                                    description="Fields required to update a vendor.")

    class Meta:
        description = "Updates an existing vendor."
        model = models.Vendor
        permissions = (
            BranchPermissions.MANAGE_BRANCHES, VendorPermissions.MANAGE_VENDORS)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        for permission in cls._meta.permissions:
            if context.user.has_perm(permission):
                return True
        raise PermissionDenied()

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_admin and \
                not authorization.can_manage_vendor(info.context.user, data["id"]):
            raise PermissionDenied()

        return {}


class RejectVendor(ModelMutation):
    class Arguments:
        input = VendorRejectionReasonInput(required=True,
                                           description="Fields required to reject a vendor.")

    class Meta:
        description = "rejects vendor."
        model = models.VendorRejectionReason
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "reject_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_superuser:
            raise PermissionDenied()

        return {}

    @classmethod
    def save(cls, info, instance, cleaned_input):
        vendor = instance.vendor
        vendor.approved = False
        vendor.is_active = False
        vendor.save(update_fields=["approved", "is_active"])
        super().save(info, instance, cleaned_input)

        vendor_admin_users = vendor.users.filter(
            app_type=AppTypes.VENDOR, app_role=AppRoleTypes.ADMIN
        ).all()
        if vendor_admin_users:
            for user in vendor_admin_users:
                sms.send_sms(str(user.mobile),
                             f"your vendor info has been rejected, "
                             f"reason: {instance.reason}",
                             user_id=user.pk)


class VendorRegister(VendorCreate):
    class Arguments:
        input = VendorInput(required=True,
                            description="Fields required to register a vendor.")

    class Meta:
        description = "registers a new vendor."
        model = models.Vendor
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        data['input']["vendor_register"] = True
        result = super().perform_mutation(_root, info, **data)

        if result.vendor:
            # notify super users
            super_users = User.objects.filter(
                app_type=AppTypes.ADMIN, app_role=AppRoleTypes.ADMIN, is_active=True)
            thread = Thread(target=BackendClient().notify_users, kwargs={"type": "INFO",
                                                                         "category": "VENDOR_REGISTERED",
                                                                         "users_pks": [
                                                                             super_user.pk
                                                                             for
                                                                             super_user
                                                                             in
                                                                             super_users],
                                                                         "key": "NewVendorRegistered",
                                                                         "title": None,
                                                                         "body": None,
                                                                         "data": {
                                                                             'vendorNameEn': result.vendor.name,
                                                                             'vendorNameAr': result.vendor.name,
                                                                         }
                                                                         },
                            daemon=False)
            thread.start()

        return result


class ApproveVendor(ModelMutation):
    class Arguments:
        id = graphene.ID(description="the id of the to be approved.", required=True)

    class Meta:
        description = "approve vendor request."
        model = models.Vendor
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_superuser:
            raise PermissionDenied()

        return {}

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        return super().clean_input(info, instance, data={}, input_cls=VendorInput)

    @classmethod
    def save(cls, info, instance, cleaned_input):
        instance.approved = True
        instance.approved_at = timezone.now()
        instance.approved_by = info.context.user
        instance.save(update_fields=["approved", "approved_at", "approved_by"])
        cls.activate_user(info, instance)

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        return result

    @classmethod
    def activate_user(cls, info, instance):
        vendor_admin_users = instance.users.filter(
            app_type=AppTypes.VENDOR, app_role=AppRoleTypes.ADMIN
        ).all()
        if vendor_admin_users:
            for user in vendor_admin_users:
                user.is_active = True
                user.save(update_fields=['is_active'])
                sms.send_sms(to=str(user.mobile),
                             body="Your Vendor Account has been approved.\n"
                                  f"your username is: {user.mobile}\n"
                                  "please reset your password to be able to login",
                             user_id=user.pk)

            # create_group(VENDOR_ADMIN_AUTH_GROUP_NAME,
            #              get_permissions(
            #                  perm.value for perm in
            #                  VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS),
            #              vendor_admin_users)


class VendorDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(description="ID of vendor to delete.", required=True)

    class Meta:
        description = "deletes an existing vendor."
        model = models.Vendor
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        raise ValidationError("Mutation is deprecated do not call")
        # if not info.context.user.is_superuser:
        #     raise PermissionDenied()

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        # vendor = result.vendor
        #
        # for user in vendor.users.all():
        #     user.delete()
        #
        # for branch in vendor.branches.all():
        #     branch.delete()

        return result


class AddVendorToFavorite(BaseMutation):
    customer_favorite_vendor = graphene.Field(
        CustomerFavoriteVendor,
        description="list of customer favorite vendors"
    )

    class Arguments:
        vendor = graphene.ID("vendor id to add to favorite", required=True)

    class Meta:
        description = "mark vendor as favorite"
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_authenticated

    @classmethod
    def clean_input(cls, root, info, data):
        vendor_id = data.get('vendor', None)
        if not vendor_id:
            raise ValidationError(
                {"vendor": ValidationError('vendor is required', code="required")}
            )
        vendor = cls.get_node_or_error(info, data['vendor'],
                                       only_type=Vendor, field="vendor")
        return {'vendor': vendor, 'customer': info.context.user}

    @classmethod
    def perform_mutation(cls, root, info, **data):
        cleaned_data = cls.clean_input(root, info, data)

        result, _ = models.CustomerFavoriteVendor.objects \
            .update_or_create(vendor=cleaned_data['vendor'],
                              customer=cleaned_data['customer'])

        return AddVendorToFavorite(customer_favorite_vendor=result)


class RemoveVendorFromFavorite(AddVendorToFavorite):
    class Meta:
        description = "remove vendor from favorite"
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, root, info, **data):
        cleaned_data = cls.clean_input(root, info, data)

        try:
            models.CustomerFavoriteVendor.objects \
                .get(vendor=cleaned_data['vendor'],
                     customer=cleaned_data['customer']).delete()

            return AddVendorToFavorite()
        except models.CustomerFavoriteVendor.DoesNotExist:
            raise ValidationError(
                {"vendor": "you don't have this vendor in your favorites"})


class TerminateVendorSubscription(BaseMutation):
    vendor = graphene.Field(Vendor, description="deactivated vendor")

    class Arguments:
        vendor = graphene.ID(description="vendor id to terminate subscription for",
                             required=True)

    class Meta:
        description = "terminate vendor subscription"
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_superuser

    @classmethod
    def clean_input(cls, root, info, data):
        vendor_id = data.get('vendor', None)
        if not vendor_id:
            raise ValidationError(
                {"vendor": ValidationError('vendor is required', code="required")}
            )
        vendor = cls.get_node_or_error(info, data['vendor'],
                                       only_type=Vendor, field="vendor")
        return {'vendor': vendor}

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, root, info, **data):
        cleaned_data = cls.clean_input(root, info, data)
        vendor = cleaned_data['vendor']
        active_subscriptions = vendor.subscriptions.filter(is_active=True).all()
        for active_subscription in active_subscriptions:
            active_subscription.is_active = False
            active_subscription.valid_till = timezone.now()
            active_subscription.save(update_fields=['is_active', 'valid_till'])

        vendor.is_active = False
        vendor.save(update_fields=['is_active'])

        return TerminateVendorSubscription(vendor=vendor)


class VendorImageCreateInput(graphene.InputObjectType):
    alt = graphene.String(description="Alt text for an image.")
    image = graphene.String(
        required=True,
        description="Represents an image file",
    )
    vendor = graphene.ID(
        required=True, description="ID of a vendor.", name="vendor"
    )


class VendorImageCreate(BaseMutation):
    vendor = graphene.Field(Vendor)
    image = graphene.Field(VendorImage)

    class Arguments:
        input = VendorImageCreateInput(
            required=True, description="Fields required to create a vendor image."
        )

    class Meta:
        description = (
            "Create a vendor image"
        )
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        data = data.get("input")
        vendor = cls.get_node_or_error(
            info, data["vendor"], field="vendor", only_type=Vendor
        )
        image = vendor.images.create(image=data["image"], alt=data.get("alt", ""))
        return VendorImageCreate(vendor=vendor, image=image)


class VendorImageUpdateInput(graphene.InputObjectType):
    alt = graphene.String(description="Alt text for an image.")


class VendorImageUpdate(BaseMutation):
    vendor = graphene.Field(Vendor)
    image = graphene.Field(VendorImage)

    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor image to update.")
        input = VendorImageUpdateInput(
            required=True, description="Fields required to update a vendor image."
        )

    class Meta:
        description = "Updates a vendor image."
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        image = cls.get_node_or_error(info, data.get("id"), only_type=VendorImage)
        vendor = image.vendor
        alt = data.get("input").get("alt")
        if alt is not None:
            image.alt = alt
            image.save(update_fields=["alt"])
        return VendorImageUpdate(vendor=vendor, image=image)


class VendorImageReorder(BaseMutation):
    vendor = graphene.Field(Vendor)
    images = graphene.List(VendorImage)

    class Arguments:
        vendor_id = graphene.ID(
            required=True,
            description="Id of vendor that images order will be altered.",
        )
        images_ids = graphene.List(
            graphene.ID,
            required=True,
            description="IDs of a vendor images in the desired order.",
        )

    class Meta:
        description = "Changes ordering of the vendor image."
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, vendor_id, images_ids):
        vendor = cls.get_node_or_error(
            info, vendor_id, field="vendor_id", only_type=Vendor
        )
        if len(images_ids) != vendor.images.count():
            raise ValidationError(
                {
                    "order": ValidationError(
                        "Incorrect number of image IDs provided.",
                        code=VendorErrorCode.INVALID,
                    )
                }
            )

        images = []
        for image_id in images_ids:
            image = cls.get_node_or_error(
                info, image_id, field="order", only_type=VendorImage
            )
            if image and image.vendor_id != vendor.id:
                raise ValidationError(
                    {
                        "order": ValidationError(
                            "Image %(image_id)s does not belong to this vendor.",
                            code=VendorErrorCode.NOT_VENDORS_IMAGE,
                            params={"image_id": image_id},
                        )
                    }
                )
            images.append(image)

        for order, image in enumerate(images):
            image.sort_order = order
            image.save(update_fields=["sort_order"])

        return VendorImageReorder(vendor=vendor, images=images)


class VendorImageDelete(BaseMutation):
    vendor = graphene.Field(Vendor)
    image = graphene.Field(VendorImage)

    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor image to delete.")

    class Meta:
        description = "Deletes a vendor image."
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        image = cls.get_node_or_error(info, data.get("id"), only_type=VendorImage)
        image_id = image.id
        # TODO IMAGE DELETION
        image.delete()
        image.id = image_id
        return VendorImageDelete(vendor=image.vendor, image=image)


class AvailabilitiesAverage(BaseMutation):
    average = graphene.Float()

    class Arguments:
        vendor_id = graphene.ID(required=True)
        from_date = graphene.Date(required=True)
        to_date = graphene.Date(required=True)

    class Meta:
        description = "Returns average of availability hours of doctors for a vendor."
        permissions = (VendorPermissions.MANAGE_VENDORS,)
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        vendor_id = from_global_id_strict_type(data.get("vendor_id"), "Vendor")
        from_date = data.get("from_date")
        to_date = data.get("to_date")

        doctors_count = Doctor.objects.filter(vendor_id=vendor_id, user__is_active=True)\
            .count()

        result = DoctorAvailability.objects \
            .filter(vendor_id=vendor_id,
                    start_time__gte=from_date, end_time__lte=to_date) \
            .aggregate(availabilities_sum=Sum("period"))

        availabilities_sum_in_hours = result.get(
            "availabilities_sum").total_seconds() / 3600

        average = availabilities_sum_in_hours / doctors_count

        return AvailabilitiesAverage(average=round(average, 2))


# mutation to create vendor review
class ReviewVendor(ModelMutation):
    class Arguments:
        input = VendorReviewCreateInput(
            required=True, description="Fields required to create a vendor review."
        )

    class Meta:
        description = "Create a vendor review"
        model = models.VendorReview
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def get_instance(cls, info, **data):
        vendor_pk = get_database_id(data['input'].get('vendor'), "Vendor")

        instance = cls._meta.model.objects.filter(
            vendor_id=vendor_pk, created_by=get_current_user().id).first()

        if instance:
            return instance

        instance = cls._meta.model()
        return instance

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_authenticated:
            raise PermissionDenied()

        vendor_pk = get_database_id(data['input'].get('vendor'), "Vendor")

        customer_has_ordered_from_vendor = BackendClient().customer_has_purchased_from_vendor(
            current_user.id, vendor_pk)
        if not customer_has_ordered_from_vendor:
            raise PermissionDenied()

        return {}

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data)
        cleaned_data['created_by'] = info.context.user
        return cleaned_data


# mutation to allow admin to update vendor review published status
class VendorReviewPublish(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor review to publish.")
        input = VendorReviewPublishInput(
            required=True, description="Fields required to update a vendor review."
        )

    class Meta:
        description = "Publish or unpublish a vendor review."
        model = models.VendorReview
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        if not info.context.user.is_superuser:
            raise PermissionDenied()

        return {}


# mutation to delete vendor review
class VendorReviewDelete(ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of a vendor review to delete.")

    class Meta:
        description = "deletes an existing vendor review."
        model = models.VendorReview
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data):

        current_user = get_current_user()
        if not current_user.is_authenticated:
            raise PermissionDenied()

        if instance.created_by != current_user:
            raise PermissionDenied()


# mutation to retrieve the current user review for a vendor
class MyReviewForVendor(BaseMutation):
    review = graphene.Field(VendorReview)

    class Arguments:
        vendor = graphene.ID(required=True, description="ID of a vendor.")

    class Meta:
        description = "Retrieve the current user review for a vendor"
        error_type_class = VendorError
        error_type_field = "vendor_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_authenticated:
            raise PermissionDenied()

        return {}

    @classmethod
    def perform_mutation(cls, _root, info, **data):
        vendor_id = from_global_id_strict_type(data.get("vendor"), "Vendor")
        current_user = get_current_user()
        review = models.VendorReview.objects.filter(vendor_id=vendor_id,
                                                    created_by=current_user).first()
        return MyReviewForVendor(review=review)


class VendorDepartmentCreate(ModelMutation):
    class Arguments:
        input = VendorDepartmentCreateInput(
            required=True, description="Fields required to create a vendor department."
        )

    class Meta:
        description = "Create a vendor department"
        model = models.Department
        permissions = (VendorPermissions.MANAGE_DEPARTMENTS,)
        error_type_class = VendorDepartmentError
        error_type_field = "vendor_department_errors"

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()

        branch_id = data['input']['branch'] if data.get("input") and data.get(
            "input").get('branch') else Node.to_global_id("Department",
                                                          instance.branch_id)

        if not authorization.can_manage_branch(current_user, branch_id):
            raise PermissionDenied()

        return {}

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data)
        cleaned_data['created_by'] = info.context.user
        return cleaned_data

    @classmethod
    def save(cls, info, instance, cleaned_input):

        super().save(info, instance, cleaned_input)
        medical_services = cleaned_input.pop('medical_services', None)
        if medical_services is not None:
            instance.medical_services.all().delete()
            for medical_service in medical_services:
                models.DepartmentMedicalService.objects.create(
                    department=instance,
                    code=medical_service
                )


class VendorDepartmentUpdate(VendorDepartmentCreate):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a vendor department to update.")
        input = VendorDepartmentUpdateInput(
            required=True, description="Fields required to update a vendor department."
        )

    class Meta:
        description = "Update a vendor department"
        model = models.Department
        permissions = (VendorPermissions.MANAGE_DEPARTMENTS,)
        error_type_class = VendorDepartmentError
        error_type_field = "vendor_department_errors"


class VendorDepartmentDelete(VendorDepartmentCreate, ModelDeleteMutation):
    class Arguments:
        id = graphene.ID(required=True,
                         description="ID of a vendor department to delete.")

    class Meta:
        description = "deletes an existing vendor department."
        model = models.Department
        permissions = (VendorPermissions.MANAGE_DEPARTMENTS,)
        error_type_class = VendorDepartmentError
        error_type_field = "vendor_department_errors"
