import django_filters
from django.contrib.gis.geos import Point
from django.db.models import Q, Min
from django.utils import timezone
from graphene_django.filter import GlobalIDFilter
from graphene_django.filter import GlobalIDMultipleChoiceFilter

from .enums import VendorTypeEnum
from ..core.filters import ObjectTypeFilter, ListObjectTypeFilter, \
    filter_entities_by_name, filter_entities_by_name__icontains, \
    filter_commercial_registration_number
from ..core.types import FilterInputObjectType
from ..core.types.common import DecimalRangeInput, DateRangeInput, DistanceFilterInput
from ..utils.filters import filter_range_field, filter_range_fields
from ..utils.request_utils import get_current_user
from ...map.postgis.functions import DistanceSphere
from ...search.backends import picker
from ...vendor import models
from ...vendor.models import Vendor
from ..utils import resolve_global_ids_to_primary_keys


def filter_search(qs, _, value):
    if value:
        search = picker.pick_backend("Vendor")
        qs &= search(value).distinct()
    return qs


def filter_vendors_by_rating(qs, _, value):
    return filter_range_field(qs, 'average_rating', value)


def filter_vendors_by_delivery_time(qs, _, value):
    return filter_range_fields(qs, 'delivery_min_from', 'delivery_min_to', value)


def filter_vendors_by_subscription_expiry(qs, _, value):
    qs = qs.filter(subscriptions__is_active=True)
    return filter_range_field(qs, 'subscriptions__valid_till', value)


def filter_by_is_live_booking_integrated(qs, _, value):
    if value is not None:
        qs = qs.filter(is_live_booking_integrated=value)
    return qs


def filter_by_support_outpatient_journey(qs, _, value):
    if value is not None:
        qs = qs.filter(support_outpatient_journey=value)
    return qs

def filter_by_support_immediate_call(qs, _, value):
    if value is not None:
        qs = qs.filter(support_immediate_call=value)
    return qs

def filter_is_integrated(qs, _, value):
    if value is not None:
        qs = qs.filter(is_integrated=value)
    return qs


def filter_by_has_health_packages(qs, _, value):
    if value is not None:
        if value:
            qs = qs.filter(id__in=
                           models.VendorHealthPackagesView.objects
                           .values_list('vendor_id', flat=True)
                           )
        else:
            qs = qs.exclude(id__in=
                            models.VendorHealthPackagesView.objects
                            .values_list('vendor_id', flat=True)
                            )
    return qs


def filter_by_has_chat_with(qs, _, value):
    current_user = get_current_user()
    if value and current_user.is_authenticated:
        qs = qs.filter(Q(branches__messages__sender=current_user)
                       | Q(branches__messages__recipient=current_user))

    return qs


def filter_vendors_is_active(qs, _, value):
    if value is not None:
        sub_query = Vendor.objects.filter(Q(is_active=True),
                                          Q(subscriptions__is_active=True),
                                          Q(subscriptions__plan__is_active=True),
                                          Q(subscriptions__plan__valid_from__lte=timezone.now()),
                                          Q(Q(subscriptions__plan__valid_till__gte=timezone.now()) | Q(
                                              subscriptions__plan__valid_till__isnull=True))
                                          ).values_list('pk', flat=True)
        if value:
            return qs.filter(pk__in=sub_query)
        else:
            return qs.filter(~Q(pk__in=sub_query))

def filter_by_favorite(qs, _, value):
    current_user = get_current_user()
    if value is not None:
        if current_user.is_authenticated:
            if value:
                qs = qs.filter(customerfavoritevendor__customer=current_user)
            else:
                qs = qs.exclude(customerfavoritevendor__customer=current_user)
        elif value:
            qs = qs.none()

    return qs


def filter_vendors_by_type(qs, _, value):
    if value:
        qs = qs.filter(type__in=value)

    return qs


def filter_by_location(qs, _, value):
    if value:
        location = Point(x=value.coordinates.lng, y=value.coordinates.lat, srid=4326)
        qs = qs.annotate(
            distance=Min(DistanceSphere("branches__address__location", location))) \
            .filter(is_active=True, distance__lte=value.distance) \
            .order_by('distance')

    return qs

def filter_by_ids(qs, _, ids):
    if ids:
        _, pks = resolve_global_ids_to_primary_keys(ids, "Vendor")
        qs = qs.filter(id__in=pks)
    return qs


class VendorFilter(django_filters.FilterSet):
    search = django_filters.CharFilter(method=filter_search)

    ids = GlobalIDMultipleChoiceFilter(method=filter_by_ids)

    rating = ObjectTypeFilter(
        input_class=DecimalRangeInput,
        method=filter_vendors_by_rating,
        field_name="average_rating",
    )

    commercial_registration_number = django_filters.CharFilter(
        method=filter_commercial_registration_number)

    delivery = ObjectTypeFilter(
        input_class=DecimalRangeInput,
        method=filter_vendors_by_delivery_time,
        field_name="average_delivery_time",
    )

    subscription_expiry = ObjectTypeFilter(
        input_class=DateRangeInput,
        method=filter_vendors_by_subscription_expiry,
    )

    has_chat_with = django_filters.BooleanFilter(method=filter_by_has_chat_with, )

    is_favorite = django_filters.BooleanFilter(method=filter_by_favorite)

    type = ListObjectTypeFilter(
        input_class=VendorTypeEnum, method=filter_vendors_by_type
    )

    is_active = django_filters.BooleanFilter(method=filter_vendors_is_active)

    location = ObjectTypeFilter(
        input_class=DistanceFilterInput,
        method=filter_by_location,
        field_name="address",
    )

    name__icontains = django_filters.CharFilter(
        method=filter_entities_by_name__icontains)
    name = django_filters.CharFilter(method=filter_entities_by_name)

    is_live_booking_integrated = django_filters. \
        BooleanFilter(method=filter_by_is_live_booking_integrated, )

    has_health_packages = django_filters.BooleanFilter(
        method=filter_by_has_health_packages, )

    support_outpatient_journey = django_filters.BooleanFilter(
        method=filter_by_support_outpatient_journey, )

    is_integrated = django_filters.BooleanFilter(
        method=filter_is_integrated, )

    support_immediate_call = django_filters.BooleanFilter(
        method=filter_by_support_immediate_call, )

    class Meta:
        model = models.Vendor
        fields = {
            "slug": ["exact", "icontains"],
            "description": ["exact", "icontains"],
            "is_active": ["exact"],
            "owner_name": ["exact", "icontains"],
            "national_id": ["exact", "icontains"],
            "contact_mobile_number": ["exact", "icontains"],
            "contact_phone_number": ["exact", "icontains"],
            "approved": ["exact"],
            "is_vip": ["exact"],
            "price_range": ["exact"],
        }


class VendorFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = VendorFilter


class VendorDepartmentFilter(django_filters.FilterSet):
    vendor = GlobalIDFilter(field_name="branch__vendor")

    class Meta:
        model = models.Department
        fields = {
            "name": ["exact", "icontains"],
            "code": ["exact"],
            "branch": ["exact"],
        }


class VendorDepartmentFilterInput(FilterInputObjectType):
    class Meta:
        filterset_class = VendorDepartmentFilter
