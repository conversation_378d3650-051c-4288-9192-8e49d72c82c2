from collections import defaultdict

from ...core.dataloaders import DataLoader
from ....account.models import Address
from ....vendor.models import VendorImage


class ImagesByVendorIdLoader(DataLoader):
    context_key = "images_by_vendor"

    def batch_load(self, keys):
        images = VendorImage.objects.filter(vendor_id__in=keys)
        image_map = defaultdict(list)
        for image in images:
            image_map[image.vendor_id].append(image)
        return [image_map[vendor_id] for vendor_id in keys]


class AddressByIdLoader(DataLoader):
    context_key = "address_by_id"

    def batch_load(self, keys):
        addresses = Address.objects.filter(pk__in=keys)
        image_map = defaultdict()
        for address in addresses:
            image_map[address.pk] = address
        return [image_map.get(address_id, None) for address_id in keys]
