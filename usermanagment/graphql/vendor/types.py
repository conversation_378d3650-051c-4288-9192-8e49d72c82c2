import graphene
from django.db.models import Q, Avg
from graphene_django import DjangoObjectType
from graphene_federation import key
from .dataloaders import ImagesByVendorIdLoader
from .dataloaders.vendors import AddressByIdLoader
from .enums import PriceRangeEnum
from ..account.sorters import UserSortingInput
from ..branch.filters import BranchFilterInput
from ..branch.sorters import BranchOrder
from ..branch.types import Branch
from ..core.connection import CountableDjangoObjectType
from ..core.fields import FilterInputConnectionField
from ..core.types.common import LocationInput
from ..subscription.utils import get_subscription_fixed_costs
from ..utils.request_utils import get_current_user
from ...auth.decorators import permission_required, one_of_permissions_required
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import VendorPermissions, AccountPermissions
from ...vendor import models


@key(fields="id")
class Vendor(CountableDjangoObjectType):
    bank_info = graphene.Field("usermanagment.graphql.vendor.types.VendorBankInfo",
                               description="vendor bank information")

    rejections = FilterInputConnectionField(
        "usermanagment.graphql.vendor.types.VendorRejectionReason",
        description="List of vendor rejection reasons."
    )

    branches = FilterInputConnectionField(
        Branch,
        user_location=LocationInput(),
        filter=BranchFilterInput(description="Filtering options for branches."),
        sort_by=BranchOrder(description="Sort branches."),
        description="list of vendor branches"
    )

    fixed_costs = graphene.Int(
        description="fixed costs from subscriptions in datetime interval",
        date_from=graphene.Argument(
            graphene.Date,
            description=(
                "fixed costs after date_from"
            ),
            required=True
        ),
        date_till=graphene.Argument(
            graphene.Date,
            description=(
                "fixed costs before date_till"
            ),
            required=True
        ),
    )

    is_favorite = graphene.Boolean(
        description="is this vendor favorite for the request user")

    images = graphene.List(
        lambda: VendorImage, description="List of Vendor images."
    )

    logo = graphene.String(description="vendor logo Url")

    back_ground_image = graphene.String(description="vendor back ground image")

    orders_count = graphene.Int(description="Total orders count")

    managers_contact_info = lambda: VendorManagersContactInfo

    price_range = graphene.Field(PriceRangeEnum,
                                 description="Price range",
                                 required=False)

    users = FilterInputConnectionField(
        "usermanagment.graphql.account.types.User",
        description="List of the shop's users.",
        sort_by=UserSortingInput(description="Sort users.")
    )

    rating = graphene.Float(description="Rating of the vendor")

    class Meta:
        description = "Represents an individual vendor"
        interfaces = [graphene.relay.Node]
        model = models.Vendor
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]

    @staticmethod
    def resolve_bank_info(root: models.Vendor, info, **kwargs):
        current_user = get_current_user()
        if current_user.has_perm(VendorPermissions.MANAGE_VENDORS) or \
                current_user.vendor_id == root.id:
            return root.bank_info if hasattr(root, "bank_info") else None

        raise PermissionDenied()

    @staticmethod
    @permission_required(VendorPermissions.MANAGE_VENDORS)
    def resolve_rejections(root: models.Vendor, info, **kwargs):
        return root.rejections.all()

    @staticmethod
    def resolve_fixed_costs(root: models.Vendor, info, date_from, date_till, **kwargs):
        subscriptions = root.subscriptions.filter(
            (Q(valid_from__lte=date_till) & Q(valid_till__gte=date_from))
            | Q(is_active=True)
        )

        return sum(
            get_subscription_fixed_costs(subscription, subscription.plan.period,
                                         date_from, date_till)
            for subscription in subscriptions
        )

    @staticmethod
    def resolve_branches(root: models.Vendor, info, **kwargs):
        return root.branches.accessible_branches()

    @staticmethod
    def resolve_is_favorite(root: models.Vendor, info, **kwargs):
        return getattr(root, 'is_favorite', False)

    @staticmethod
    def resolve_images(root: models.Vendor, info, *_args):
        return ImagesByVendorIdLoader(info.context).load(root.id)

    @staticmethod
    def resolve_address(root: models.Vendor, info, *_args):
        if not root.address_id:
            return None
        return AddressByIdLoader(info.context).load(root.address_id)

    @staticmethod
    def resolve_back_ground_image(root: models.Vendor, _info, **_kwargs):
        return root.back_ground_image

    @staticmethod
    def resolve_logo(root: models.Vendor, _info, **_kwargs):
        return root.logo

    @staticmethod
    @permission_required(VendorPermissions.MANAGE_VENDORS)
    def resolve_orders_count(root, info):
        return root.total_orders_count

    @staticmethod
    def resolve_managers_contact_info(root: models.Vendor, info, **kwargs):
        current_user = get_current_user()
        if current_user.has_perm(VendorPermissions.MANAGE_VENDORS) or \
                current_user.vendor_id == root.id:
            return root.managers_contact_info if hasattr(root,
                                                         "managers_contact_info") else None

        raise PermissionDenied()

    @staticmethod
    def resolve_rating(root: models.Vendor, info, **kwargs):

        return root.reviews.aggregate(rating=Avg("rating"))["rating"]

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Vendor)


@key(fields="id")
class VendorRejectionReason(CountableDjangoObjectType):
    class Meta:
        description = "Represents an vendor rejection reason"
        interfaces = [graphene.relay.Node]
        model = models.VendorRejectionReason


@key(fields="id")
class VendorBankInfo(DjangoObjectType):
    class Meta:
        description = "Represents a vendorBankInfo"
        interfaces = [graphene.relay.Node]
        model = models.VendorBankInfo

    @staticmethod
    def resolve_customer(root, info, **kwargs):
        current_user = get_current_user()
        if root.customer == current_user or root.vendor_id == current_user.vendor_id:
            return root.customer

        raise PermissionDenied()

    @staticmethod
    def resolve_vendor(root, info, **kwargs):
        current_user = get_current_user()
        if root.customer == current_user or root.vendor_id == current_user.vendor_id:
            return root.vendor

        raise PermissionDenied()


@key(fields="id")
class CustomerFavoriteVendor(CountableDjangoObjectType):
    class Meta:
        description = "Represents a customers favorite vendor"
        interfaces = [graphene.relay.Node]
        model = models.CustomerFavoriteVendor


@key(fields="id")
class VendorImage(CountableDjangoObjectType):
    url = graphene.String(
        required=True,
        description="The URL of the image.",
        size=graphene.Int(description="Size of the image."),
    )

    class Meta:
        description = "Represents a vendor image."
        fields = ["alt", "id", "sort_order"]
        interfaces = [graphene.relay.Node]
        model = models.VendorImage

    @staticmethod
    def resolve_url(root: models.VendorImage, _info):
        return root.image

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id)


class VendorManagersContactInfo(DjangoObjectType):
    class Meta:
        description = "Represents a VendorManagersContactInfo"
        interfaces = [graphene.relay.Node]
        model = models.VendorManagersContactInfo


class Division(DjangoObjectType):
    class Meta:
        description = "Represents a Division"
        interfaces = [graphene.relay.Node]
        model = models.Division


# graphene type for vendor reviews
class VendorReview(DjangoObjectType):
    class Meta:
        description = "Represents a VendorReview"
        interfaces = [graphene.relay.Node]
        model = models.VendorReview
        fields = [
            "id",
            "vendor",
            "rating",
            "content",
            "published",
            "created_by"
        ]


class DepartmentUser(CountableDjangoObjectType):
    class Meta:
        description = "Represents department User "
        model = models.DepartmentUser


class DepartmentMedicalService(CountableDjangoObjectType):
    class Meta:
        description = "Represents a MedicalService"
        interfaces = [graphene.relay.Node]
        model = models.DepartmentMedicalService
        fields = [
            "id",
            "code",
            "department",
        ]

# graphene type for vendor departments
@key(fields="id")
class Department(DjangoObjectType):
    users = FilterInputConnectionField(
        "usermanagment.graphql.account.types.User",
        description="List of the department's users.",
    )
    medical_services = FilterInputConnectionField(
        DepartmentMedicalService,
        description="list of medical services"
    )

    class Meta:
        description = "Represents a Department"
        interfaces = [graphene.relay.Node]
        model = models.Department
        fields = [
            "id",
            "name",
            "code",
            "description",
            "directions",
            "branch",
        ]

    @staticmethod
    @one_of_permissions_required(
        [AccountPermissions.MANAGE_USERS, AccountPermissions.VIEW_USERS])
    def resolve_users(root: models.Branch, info, **kwargs):
        return root.users.all()

    def resolve_medical_services(root: models, info, **kwargs):
        return root.medical_services.all()

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Department)
