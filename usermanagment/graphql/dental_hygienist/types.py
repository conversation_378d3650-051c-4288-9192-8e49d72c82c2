from graphene import relay

from usermanagment.graphql.core.connection import CountableDjangoObjectType
from usermanagment.dental_hygienist import models


class DentalHygienist(CountableDjangoObjectType):
    class Meta:
        description = "Represents an individual Dental Hygienist"
        model = models.DentalHygienist
        interfaces = [relay.Node]
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]
        only_fields = [
            "id",
            "user",
            "health_license_number",
            "health_license_start_date",
            "health_license_end_date",
            "created",
            "modified",
        ]

    @staticmethod
    def resolve_user(root, info, **kwargs):
        return root.user

    @staticmethod
    def resolve_health_license_number(root, info, **kwargs):
        return root.health_license_number

    @staticmethod
    def resolve_health_license_start_date(root, info, **kwargs):
        return root.health_license_start_date

    @staticmethod
    def resolve_health_license_end_date(root, info, **kwargs):
        return root.health_license_end_date
