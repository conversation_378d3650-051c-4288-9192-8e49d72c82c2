import graphene

from .filters import PatientFilterInput, UnverifiedNationalsFilterInput
from .mutations.patient import PatientCreate, PatientSendOrderOTP, CustomerCreate, \
    CustomerProfileUpdate, UploadNationalCard, VerifyNationalId, \
    CheckCustomerAccountExist, CustomerDelete, PatientUpdate,UpdatePatientHealthParameterLastSyncTime
from .types import Patient, UnverifiedNationals
from ..account.sorters import PatientSortingInput
from ..core.fields import FilterInputConnectionField
from ..utils.request_utils import get_current_user
from ...auth.decorators import permission_required, one_of_permissions_required
from ...auth.exceptions import PermissionDenied
from ...auth.permissions import PatientPermissions, HealthProgramPermissions
from ...patient import models


class PatientQueries(graphene.ObjectType):
    patient = graphene.Field(
        Patient,
        id=graphene.Argument(graphene.ID, description="ID of the Patient.", ),
        national_id=graphene.Argument(graphene.String,
                                      description="National id of the Patient.", ),
        description="Look up a Patient by ID.",
    )

    patients = FilterInputConnectionField(
        Patient,
        filter=PatientFilterInput(description="Filters for Patient."),
        sort_by=PatientSortingInput(description="Sort permission groups."),
        description="List of the Patients.",
    )

    unverified_nationals = FilterInputConnectionField(
        UnverifiedNationals,
        filter=UnverifiedNationalsFilterInput(
            description="Filters unverified nationals."),
        description="List of the unverified nationals.",
    )

    @staticmethod
    def resolve_patient(_root, info, id=None, national_id=None, **data):
        assert (
                id or national_id
        ), "No ID or nationalId provided."
        patient = None
        if id:
            patient = graphene.Node.get_node_from_global_id(info, id, Patient)

        if national_id:
            patient = models.Patient.objects.filter(
                user__national_id=national_id).first()

        current_user = get_current_user()
        if patient and patient.user.deleted:
            return None

        if patient and not (
                patient.user_id == current_user.id or current_user.has_perm(
            PatientPermissions.MANAGE_PATIENTS) or current_user.has_perm(
            PatientPermissions.VIEW_PATIENTS) or current_user.has_perm(
            HealthProgramPermissions.MANAGE_CASE_MANAGEMENT) or
                current_user.is_superuser):

            raise PermissionDenied()

        return patient

    @staticmethod
    @one_of_permissions_required(
        [PatientPermissions.MANAGE_PATIENTS, PatientPermissions.VIEW_PATIENTS,
         HealthProgramPermissions.MANAGE_CASE_MANAGEMENT])
    def resolve_patients(_root, info, **data):
        return models.Patient.objects.filter(
            user__deleted=False).distinct()

    @staticmethod
    @permission_required(PatientPermissions.VERIFY_NATIONAL_IDS)
    def resolve_unverified_nationals(_root, info, **data):
        return models.UnverifiedNationals.objects.all()


class PatientMutations(graphene.ObjectType):
    patient_create = PatientCreate.Field()
    patient_update= PatientUpdate.Field()
    patient_send_order_otp = PatientSendOrderOTP.Field()

    customer_create = CustomerCreate.Field()
    customer_profile_update = CustomerProfileUpdate.Field()
    customer_delete = CustomerDelete.Field()

    upload_national_card = UploadNationalCard.Field()
    verify_national_id = VerifyNationalId.Field()

    check_customer_account_exist = CheckCustomerAccountExist.Field()

    update_patient_health_parameter_last_sync_time = UpdatePatientHealthParameterLastSyncTime.Field()
