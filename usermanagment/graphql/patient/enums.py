import graphene

from usermanagment.graphql.core.utils import str_to_enum
from usermanagment.patient.enums import (
    PatientIdTypes,
    MaritalStatuses,
    ResidencyTypes, UnverifiedNationalsStatuses, PreferredCommunicationMethods,
)

PatientIdTypeEnum = graphene.Enum(
    "PatientIdTypeEnum",
    [(str_to_enum(item[0]), item[0]) for item in PatientIdTypes.choices]
)

PreferredCommunicationMethodsEnum = graphene.Enum(
    "PreferredCommunicationMethodsEnum",
    [(str_to_enum(item[0]), item[0]) for item in PreferredCommunicationMethods.choices]
)

ResidencyTypesEnum = graphene.Enum(
    "ResidencyTypesEnum",
    [(str_to_enum(item[0]), item[0]) for item in ResidencyTypes.choices]
)

MaritalStatusesEnum = graphene.Enum(
    "MaritalStatusesEnum",
    [(str_to_enum(item[0]), item[0]) for item in MaritalStatuses.choices]
)

UnverifiedNationalsStatusesEnum = graphene.Enum(
    "UnverifiedNationalsStatusesEnum",
    [(str_to_enum(item[0]), item[0]) for item in UnverifiedNationalsStatuses.choices]
)
