import graphene
from graphene_federation import key

from ..core.connection import CountableDjangoObjectType
from ...core.utils import datetime_utils
from ...patient import models

from datetime import date


@key(fields="id,nationality")
class Patient(CountableDjangoObjectType):
    number = graphene.String()
    age = graphene.Int()
    first_name = graphene.String()
    second_name = graphene.String()
    third_name = graphene.String()
    last_name = graphene.String()
    first_name_ar = graphene.String()
    second_name_ar = graphene.String()
    third_name_ar = graphene.String()
    last_name_ar = graphene.String()
    gender = graphene.String()
    date_of_birth = graphene.Date()
    email = graphene.String()
    national_id_number = graphene.String()

    class Meta:
        description = "Represents an individual Patient"
        interfaces = [graphene.relay.Node]
        model = models.Patient

    @staticmethod
    def resolve_number(root, *args, **kwargs):
        return str(root.pk)

    @staticmethod
    def resolve_insurance_cards(root, *args, **kwargs):
        return root.insurance_cards.all()

    @staticmethod
    def __resolve_reference(root, _info, **_kwargs):
        return graphene.Node.get_node_from_global_id(_info, root.id, Patient)

    @staticmethod
    def resolve_parent_patient(root, *args, **kwargs):
        if root.parent_patient:
            return root.parent_patient

    @staticmethod
    def resolve_age(root: models.Patient, *args, **kwargs):
        return datetime_utils.calculate_age(root.user.date_of_birth)

    @staticmethod
    def resolve_first_name(root: models.Patient, *args, **kwargs):
        return root.user.first_name

    @staticmethod
    def resolve_first_name_ar(root: models.Patient, *args, **kwargs):
        return root.user.first_name_ar

    @staticmethod
    def resolve_second_name(root: models.Patient, *args, **kwargs):
        return root.user.second_name

    @staticmethod
    def resolve_second_name_ar(root: models.Patient, *args, **kwargs):
        return root.user.second_name_ar

    @staticmethod
    def resolve_third_name(root: models.Patient, *args, **kwargs):
        return root.user.third_name

    @staticmethod
    def resolve_third_name_ar(root: models.Patient, *args, **kwargs):
        return root.user.third_name_ar

    @staticmethod
    def resolve_last_name(root: models.Patient, *args, **kwargs):
        return root.user.last_name

    @staticmethod
    def resolve_last_name_ar(root: models.Patient, *args, **kwargs):
        return root.user.last_name_ar

    @staticmethod
    def resolve_gender(root: models.Patient, *args, **kwargs):
        return root.user.gender

    @staticmethod
    def resolve_date_of_birth(root: models.Patient, *args, **kwargs):
        return root.user.date_of_birth

    @staticmethod
    def resolve_email(root: models.Patient, *args, **kwargs):
        return root.user.email

    @staticmethod
    def resolve_national_id_number(root: models.Patient, *args, **kwargs):
        return root.user.national_id


class UnverifiedNationals(CountableDjangoObjectType):
    class Meta:
        description = "Represents an unverified national card"
        interfaces = [graphene.relay.Node]
        model = models.UnverifiedNationals
