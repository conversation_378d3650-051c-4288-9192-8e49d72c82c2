import logging

import graphene
from django.contrib.auth import password_validation
from django.contrib.sites.models import Site
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils import timezone

from ..enums import PatientIdTypeEnum, ResidencyTypesEnum, MaritalStatusesEnum, \
    UnverifiedNationalsStatusesEnum, PreferredCommunicationMethodsEnum
from ...account.enums import PersonGenderEnum
from ...account.mutations.base import check_contact_number_verification, \
    check_email_verification, validate_recaptcha_token
from ...account.mutations.users import BaseAccountMutationMixin, UserCreate, UserUpdate
from ...account.types import User as UserType
from ...account.utils import fetch_language_instances_by_codes
from ...core.enums import AccountErrorCode
from ...core.mutations import ModelMutation, BaseMutation
from ...core.types.common import PatientError, AccountError
from ...core.utils import token_utils
from ...core.utils.token_utils import prepare_user_attributes
from ...utils import get_database_id
from ...utils.request_utils import get_current_user, set_current_user
from .... import settings
from ....account import sms
from ....account.email import send_email
from ....account.models import User
from ....account.sms import send_sms
from ....account.utils import get_patient_group
from ....account.validators import validate_possible_number, is_email_valid
from ....auth.enums import AppTypes, AppRoleTypes
from ....auth.exceptions import PermissionDenied
from ....auth.features import Feature
from ....auth.permissions import PatientPermissions
from ....core.utils import datetime_utils
from ....core.validaters import generate_and_validate_password
from ....graphql_client.backend_client import BackendClient
from ....keycloak.keycloak_client import KeycloakAPI
from ....patient import models
from ....patient.enums import UnverifiedNationalsStatuses
from ....patient.models import Patient
from ....settings import get_bool_from_env
from ....vendor.models import Branch

logger = logging.getLogger(__name__)

class PatientUpdateInput(graphene.InputObjectType):
    id_type = PatientIdTypeEnum()
    nationality = graphene.String()
    document_expiry_date = graphene.Date()
    national_id = graphene.String()
    first_name = graphene.String()
    second_name = graphene.String()
    third_name = graphene.String()
    last_name = graphene.String()
    first_name_ar = graphene.String()
    second_name_ar = graphene.String()
    third_name_ar = graphene.String()
    last_name_ar = graphene.String()
    gender = PersonGenderEnum()
    date_of_birth = graphene.Date()
    preferred_communication_language = graphene.String(required=False)
    preferred_communication_method = PreferredCommunicationMethodsEnum(required=False)


class PatientInput(graphene.InputObjectType):
    id_type = PatientIdTypeEnum()
    nationality = graphene.String()
    document_expiry_date = graphene.Date()
    national_id_number = graphene.String()
    first_name = graphene.String()
    second_name = graphene.String()
    third_name = graphene.String()
    last_name = graphene.String()
    first_name_ar = graphene.String()
    second_name_ar = graphene.String()
    third_name_ar = graphene.String()
    last_name_ar = graphene.String()
    gender = PersonGenderEnum()
    date_of_birth = graphene.Date()
    mobile = graphene.String()
    email = graphene.String()
    blood_group = graphene.String()
    preferred_language = graphene.String()
    preferred_communication_language = graphene.String(required=False)
    preferred_communication_method = PreferredCommunicationMethodsEnum(required=False)
    marital_status = MaritalStatusesEnum()
    parent_national_id = graphene.String(
        description="National id of the user that this patient belongs to",
        required=False)

    relation_type = graphene.String(required=False)
    generate_meeting_platform_id = graphene.Boolean()
    contact_number = graphene.String()
    send_welcome_message = graphene.Boolean()
    skip_validation = graphene.Boolean()
    password = graphene.String()


class PatientSendOrderOtpInput(graphene.InputObjectType):
    order_id = graphene.ID(required=True)
    order_otp = graphene.String(required=True)
    patient_id = graphene.ID(required=True)
    branch_id = graphene.ID(required=True)
    url = graphene.String(required=True)


class CustomerCreateInput(graphene.InputObjectType):
    first_name = graphene.String()
    last_name = graphene.String()
    email = graphene.String()
    email_verification_token = graphene.String()
    mobile = graphene.String()
    mobile_verification_token = graphene.String()
    date_of_birth = graphene.Date(required=True)
    password = graphene.String(required=False)
    gender = PersonGenderEnum(required=True)
    is_dependent = graphene.Boolean()
    relation_type = graphene.String(required=False)
    photo = graphene.String(description="photo of the user")
    contact_number = graphene.String()
    preferred_communication_language = graphene.String(required=False)
    preferred_communication_method = PreferredCommunicationMethodsEnum(required=False)
    marketing_consent = graphene.Boolean(required=False)



class CustomerDeleteInput(graphene.InputObjectType):
    email = graphene.String()
    email_verification_token = graphene.String()
    mobile = graphene.String()
    mobile_verification_token = graphene.String()
    delete_reason = graphene.String(required=True, description="reason for deletion")


class CustomerProfileUpdateInput(graphene.InputObjectType):
    first_name = graphene.String()
    last_name = graphene.String()
    first_name_ar = graphene.String()
    last_name_ar = graphene.String()
    blood_group = graphene.String()
    preferred_language = graphene.String()
    residency_type = ResidencyTypesEnum()
    marital_status = MaritalStatusesEnum()
    date_of_birth = graphene.Date()
    gender = PersonGenderEnum()
    relation_type = graphene.String(required=False)
    photo = graphene.String(description="photo of the user")
    contact_number = graphene.String()
    preferred_communication_language = graphene.String(required=False)
    preferred_communication_method = PreferredCommunicationMethodsEnum(required=False)


class UploadNationalCardInput(graphene.InputObjectType):
    nationalId = graphene.String(required=True)
    nationality = graphene.String(required=True)
    document_expiry_date = graphene.Date(required=True)
    id_type = PatientIdTypeEnum(required=True)
    front_image_file_name = graphene.String(required=True)
    rear_image_file_name = graphene.String(required=True)


class VerifyNationalIdInput(graphene.InputObjectType):
    status = UnverifiedNationalsStatusesEnum(required=True)
    rejection_reason = graphene.String(required=False)


class PatientSendOrderOTP(BaseMutation):
    ok = graphene.Boolean()

    class Arguments:
        input = PatientSendOrderOtpInput(required=True)

    class Meta:
        description = "Send OTP to patient for order"
        permissions = (PatientPermissions.MANAGE_PATIENTS,)
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):

        order_id = get_database_id(data['input'].get('order_id'), "Order")
        patient_id = get_database_id(data['input'].get('patient_id'), "Patient")
        branch_id = get_database_id(data['input'].get('branch_id'), "Branch")
        order_otp = data['input'].get('order_otp')
        url = data['input'].get('url')

        patient = models.Patient.objects.filter(pk=patient_id).first()
        if patient is None:
            raise ValidationError({"patient_id": "Patient not found"})

        branch = Branch.objects.filter(pk=branch_id).first()
        if branch is None:
            raise ValidationError({"branch_id": "Branch not found"})

        branch_name = branch.name
        user = patient.user
        phone_number = user.mobile
        patient_name = user.first_name
        sms.generate_and_send_order_mobile_otp(user, phone_number, patient_name,
                                               patient_id,
                                               order_id, branch_name, order_otp, url)
        return PatientSendOrderOTP(ok=True)

class PatientUpdate(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, description="ID of the patient to update.")
        input = PatientUpdateInput(required=True,
                             description="Fields required to update a Patient.")

    class Meta:
        description = "Updates an existing Patient."
        model = models.Patient
        permissions = (PatientPermissions.MANAGE_PATIENTS,)
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        id_type = data.get("id_type", instance.id_type)
        nationality = data.get("nationality", instance.nationality)
        document_expiry_date = data.get("document_expiry_date", instance.document_expiry_date)
        national_id = data.get("national_id",instance.user.national_id)
        if data.get("nationality"):
            res= BackendClient().validate_code_system_concept(data.get("nationality"), "NATIONALITY")
            if not res:
                raise ValidationError(
                    {
                        "nationality": ValidationError(
                            "nationality code is not valid",
                            code=AccountErrorCode.INVALID
                        )
                    }
                )

        if national_id:
            if not id_type:
                raise ValidationError({
                    "id_type": "this field is required"
                })
            if not document_expiry_date:
                raise ValidationError({
                    "document_expiry_date": "this field is required"
                })
            if not nationality:
                raise ValidationError({
                    "nationality": "this field is required"
                })
        else:
            data["id_type"] = None
            data["document_expiry_date"] = None
            data["nationality"] = None
        cleaned_input= super().clean_input(info, instance, data, input_cls)
        if data.get("preferred_communication_language"):
            languages = fetch_language_instances_by_codes(
                [data.get("preferred_communication_language")])
            cleaned_input["preferred_communication_language"] = languages[0]
        return cleaned_input

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        patient = models.Patient.objects.filter(pk=result.patient.pk).first()
        if patient is None:
            raise ValidationError({"id": "Patient not found"})

        user_input = data.get("input")
        user_id = graphene.Node.to_global_id("User",patient.user_id)

        UserUpdate().perform_mutation(
            None, info, **{
                "id": user_id,
                "input": user_input
            }
        )
        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(patient.user, keycloak_user_data["attributes"])
        KeycloakAPI().update_user(
            patient.user.sso_id,
            {
                'attributes': keycloak_user_data["attributes"]
            })
        patient = Patient.objects.get(pk=patient.pk)
        return PatientUpdate(patient=patient)


class PatientCreate(ModelMutation):
    class Arguments:
        input = PatientInput(required=True,
                             description="Fields required to create a Patient.")

    class Meta:
        description = "Creates a new Patient."
        model = models.Patient
        permissions = (PatientPermissions.MANAGE_PATIENTS,)
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def get_instance(cls, info, **data):
        national_id_number = data.get('input').get('national_id_number')

        if national_id_number:
            instance = cls._meta.model.objects.filter(
                user__national_id=national_id_number).first()
            if instance:
                return instance

        instance = cls._meta.model()
        return instance

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        id_type = data.get("id_type")
        nationality = data.get("nationality")
        document_expiry_date = data.get("document_expiry_date")
        national_id_number = data.get("national_id_number")
        if national_id_number:
            if not id_type:
                raise ValidationError({
                    "id_type": "this field is required"
                })
            if not document_expiry_date:
                raise ValidationError({
                    "document_expiry_date": "this field is required"
                })
            if document_expiry_date < timezone.now().date():
                raise ValidationError({
                    "document_expiry_date": "Expiry date must be in the future."
                })
            if not nationality:
                raise ValidationError({
                    "nationality": "this field is required"
                })
        else:
            data["id_type"] = None
            data["document_expiry_date"] = None
            data["nationality"] = None

        parent_national_id = data.get("parent_national_id")
        skip_validation = data.get("skip_validation", False)
        date_of_birth = data.get("date_of_birth")

        if not parent_national_id and date_of_birth and datetime_utils.calculate_age(
                date_of_birth) < 18:
            raise ValidationError("Patient age must be older than 18")

        if (not skip_validation and not data.get("mobile") and not data.get("email")) \
                and not parent_national_id:
            raise ValidationError({
                "mobile": "this field is required"
            })

        contact_number = data.get("contact_number")
        if contact_number:
            try:
                validate_possible_number(contact_number)
            except ValidationError as error:
                raise ValidationError({"contact_number": error})

        cleaned_input = super().clean_input(info, instance, data, input_cls)
        if data.get("preferred_communication_language"):
            languages = fetch_language_instances_by_codes(
                [data.get("preferred_communication_language")])
            cleaned_input["preferred_communication_language"] = languages[0]
        cleaned_input['validation_skipped'] = skip_validation
        return cleaned_input

    @classmethod
    def send_welcome_message(cls, user):
        site_settings = Site.objects.get_current().settings
        user_language = user.preferred_language
        project_name = settings.PROJECT_NAME
        if user_language and user_language == "ar":
            if user.mobile:
                message = site_settings.patient_create_sms_message_ar
                send_sms(to=user.mobile,
                         body=message.format(project_name=project_name,
                                             android_app_link=settings.ANDROID_APP_LINK,
                                             ios_app_link=settings.IOS_APP_LINK)
                         )
            elif user.email:
                message = site_settings.patient_create_email_message_ar
                send_email(to=user.email,
                           title=project_name,
                           body=message.format(project_name=project_name,
                                               android_app_link=settings.ANDROID_APP_LINK,
                                               ios_app_link=settings.IOS_APP_LINK)
                           )
        else:
            if user.mobile:
                message = site_settings.patient_create_sms_message
                send_sms(to=user.mobile,
                         body=message.format(project_name=project_name,
                                             android_app_link=settings.ANDROID_APP_LINK,
                                             ios_app_link=settings.IOS_APP_LINK)
                         )
            elif user.email:
                message = site_settings.patient_create_email_message
                send_email(to=user.email,
                           title=project_name,
                           body=message.format(project_name=project_name,
                                               android_app_link=settings.ANDROID_APP_LINK,
                                               ios_app_link=settings.IOS_APP_LINK)
                           )

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        is_user_exist = attach_patient_to_user(_root, info, result.patient, data)
        if not is_user_exist and  data.get('input').get("send_welcome_message", False):
            cls.send_welcome_message(result.patient.user)
        return result


def attach_patient_to_user(_root, info, patient: models.Patient, data=None):
    if not patient:
        logger.error(
            "null patient in attach_patient_to_user while creating patient")
        return True
    if patient.user:
        return True

    parent_user = None

    current_user = get_current_user()
    parent_national_id = data.get("input").get("parent_national_id")
    is_dependent = data.get("input").get("is_dependent") or parent_national_id
    relation_type = data.get("input").get("relation_type")
    photo = data.get("input").get("photo")
    mobile = data.get("input").get("mobile")
    generate_meeting_platform_id = data.get("input").get("generate_meeting_platform_id")
    national_id_number = data.get('input').get('national_id_number')
    email = data.get('input').get('email')
    first_name = data.get('input').get('first_name')
    second_name = data.get('input').get('second_name')
    third_name = data.get('input').get('third_name')
    last_name = data.get('input').get('last_name')
    first_name_ar = data.get('input').get('first_name_ar')
    second_name_ar = data.get('input').get('second_name_ar')
    third_name_ar = data.get('input').get('third_name_ar')
    last_name_ar = data.get('input').get('last_name_ar')
    gender = data.get('input').get('gender')
    date_of_birth = data.get('input').get('date_of_birth')
    nationality = data.get('input').get('nationality')
    id_type = data.get("input").get("id_type")
    document_expiry_date = data.get("input").get("document_expiry_date")
    marketing_consent = data.get("input").get("marketing_consent")
    if patient.contact_number is None and mobile:
        patient.contact_number = mobile
        patient.save(update_fields=["contact_number"])

    if not is_dependent:
        data['input']['relation_type'] = None

    if is_dependent:
        if not relation_type:
            raise ValidationError({
                "relation_type": "is required"
            })

    if current_user.is_consumer or current_user.is_anonymous:
        password = data.get("input").get("password")
        if is_dependent:
            parent_user = graphene.Node.to_global_id("User", get_current_user().pk)

    else:
        password = data.get("input").get("password") \
            if data.get("input").get("password") \
            else generate_and_validate_password()

        if parent_national_id:
            db_parent_user = User.objects.filter(app_type=AppTypes.CUSTOMER,
                                                 national_id=parent_national_id).first()

            if not db_parent_user:
                raise ValidationError({
                    "parent_national_id": "is not exist"
                })

            parent_user = graphene.Node.to_global_id("User", db_parent_user.pk)

    user = None
    is_user_exist = False
    if national_id_number:
        user = User.objects.filter(app_type=AppTypes.CUSTOMER,
                                   national_id=national_id_number).first()
        is_user_exist = True

    if not user:
        result = UserCreate().perform_mutation(
            _root, info, **{
                "input": {
                    "mobile": mobile,
                    "email": email,
                    "national_id": national_id_number,
                    "password": password,
                    "first_name": first_name,
                    "second_name": second_name,
                    "third_name": third_name,
                    "last_name": last_name,
                    "first_name_ar": first_name_ar,
                    "second_name_ar": second_name_ar,
                    "third_name_ar": third_name_ar,
                    "last_name_ar": last_name_ar,
                    "gender": gender,
                    "date_of_birth": date_of_birth,
                    "app_type": AppTypes.CUSTOMER,
                    "app_role": AppRoleTypes.USER,
                    "parent_user": parent_user,
                    "relation_type": relation_type,
                    "is_active": True,
                    "photo": photo,
                    "nationality":nationality,
                    "id_type": id_type,
                    "document_expiry_date": document_expiry_date,
                    "marketing_consent": marketing_consent,
                    "preferred_language": data.get("input").get("preferred_language"),
                }
            }
        )
        user = result.user
        is_user_exist = False

    if user:
        patient.user = user
        patient.save(update_fields=["user"])

        if generate_meeting_platform_id:
            meeting_platform_id = token_utils.generate_meeting_platform_id(user,
                                                                           str(user.id),
                                                                           password)
            if meeting_platform_id:
                user.meeting_platform_id = meeting_platform_id
                user.save(update_fields=["meeting_platform_id"])
        keycloak_user_data = {"attributes": {}}
        prepare_user_attributes(user, keycloak_user_data["attributes"])
        logger.debug(f"keycloak_user_data => {keycloak_user_data}")
        KeycloakAPI().update_user(
            user.sso_id,
            {
                'attributes': keycloak_user_data["attributes"]
            })
        logger.debug("user updated in keycloak")

        # Add the new patient to Patient group in keycloak and database

        user_groups = {"add_groups": [get_patient_group()]}
        BaseAccountMutationMixin.add_remove_user_to_keycloak_group(user_groups, user)
        user.groups.add(*user_groups.get("add_groups"))
        logger.debug("groups added to user")

    return is_user_exist


class CustomerCreate(ModelMutation):
    class Arguments:
        input = CustomerCreateInput(
            description="Fields required to create a sso user.", required=True
        )

    class Meta:
        description = "Register a new sso user."
        exclude = ["password"]
        model = models.Patient
        error_type_class = PatientError
        error_type_field = "patient_errors"
        feature = Feature.CUSTOMER_REGISTRATION

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):

        mobile = data.get("mobile")
        mobile_token = data.get("mobile_verification_token")

        email = data.get("email")
        email_token = data.get("email_verification_token")

        password = data.get("password")

        date_of_birth = data.get("date_of_birth")

        is_dependent = data.get("is_dependent", instance.user is not None and
                                instance.user.parent_user is not None)

        relation_type = data.get("relation_type", instance.user is not None and
                                 instance.user.relation_type is not None)

        ## check if user is dependent and CustomerCreate and feature is enabled
        dependent_feature_enabled = get_bool_from_env("_".join
                                                      ([Feature.DEPENDENT_CREATION.name,
                                                        "FEATURE_ENABLED"]),
                                                      False)
        if is_dependent and not dependent_feature_enabled and cls is CustomerCreate:
            raise ValidationError(
                "Dependent creation is not allowed")

        current_user = get_current_user()

        if not (mobile or email) and cls is CustomerCreate and not is_dependent:
            raise ValidationError(
                "At least, contact_number or email token must be passed")

        if is_dependent and not current_user.is_consumer:
            raise PermissionDenied()

        if is_dependent and not relation_type:
            raise ValidationError({
                "relation_type": "is required"
            })

        if not is_dependent:
            data['relation_type'] = None

        if not is_dependent and not password and cls is CustomerCreate:
            raise ValidationError(
                {"password": "is required."})

        if not is_dependent and date_of_birth and datetime_utils.calculate_age(
                date_of_birth) < 18:
            raise ValidationError("Patient age must be older than 18")

        if (current_user.is_authenticated and current_user.relation_type
                and cls is CustomerCreate):
            raise ValidationError("Dependent user can't have dependent user")

        if mobile and not mobile_token:
            raise ValidationError(
                {
                    "mobile_verification_token": "required when passing mobile."})

        if email and not email_token:
            raise ValidationError(
                {"email_verification_token": "required when passing email."})

        if mobile:
            try:
                validate_possible_number(mobile)
                check_contact_number_verification(mobile, mobile_token)
            except ValidationError as error:
                raise ValidationError(error)

        if email:
            try:
                check_email_verification(email, email_token)
            except ValidationError as error:
                raise ValidationError({"email": error})

        if password:
            try:
                password_validation.validate_password(password, instance)
            except ValidationError as error:
                raise ValidationError({"password": str(error)})
        elif cls is CustomerCreate:
            data["password"] =  generate_and_validate_password()

        cleaned_input = super().clean_input(info, instance, data, input_cls=None)
        if data.get("preferred_communication_language"):
            languages = fetch_language_instances_by_codes(
                [data.get("preferred_communication_language")])
            cleaned_input["preferred_communication_language"] = languages[0]

        return cleaned_input

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        result = super().perform_mutation(_root, info, **data)
        attach_patient_to_user(_root, info, result.patient, data)
        return result


class CustomerProfileUpdate(CustomerCreate):
    class Arguments:
        input = CustomerProfileUpdateInput(
            description="Fields required to update the account of the logged-in "
                        "customer.",
            required=True,
        )

    class Meta:
        description = "Updates the account of the logged-in customer."
        exclude = ["password"]
        model = models.Patient
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_consumer

    @classmethod
    def get_instance(cls, info, **data):
        return get_current_user().patient

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        patient_update_result = super(CustomerCreate, cls).perform_mutation(_root, info,
                                                                            **data)

        user_id = graphene.Node.to_global_id("User",
                                             patient_update_result.patient.user_id)

        user_input = data.get("input")

        UserUpdate().perform_mutation(
            None, info, **{
                "id": user_id,
                "input": user_input
            }
        )
        patient = Patient.objects.get(pk=patient_update_result.patient.pk)
        return CustomerProfileUpdate(patient=patient)


class UploadNationalCard(ModelMutation):
    class Arguments:
        input = UploadNationalCardInput(
            description="Fields required to upload the national card.",
            required=True,
        )

    class Meta:
        description = "uploads national card to verify the national."
        model = models.UnverifiedNationals
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.ADD_NATIONAL_ID

    @classmethod
    def check_authorization(cls, root, info, instance, **data):
        current_user = get_current_user()
        if not current_user.is_authenticated:
            raise PermissionDenied()
        if current_user.national_id:
            raise ValidationError("your national id is verified")

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        cleaned_data = super().clean_input(info, instance, data, input_cls=None)
        cleaned_data["user"] = get_current_user()
        if data.get("nationality"):
            res= BackendClient().validate_code_system_concept(data.get("nationality"), "NATIONALITY")
            if not res:
                raise ValidationError(
                    {
                        "nationality": ValidationError(
                            "nationality code is not valid",
                            code=AccountErrorCode.INVALID
                        )
                    }
                )

        document_expiry_date = data.get("document_expiry_date")
        if document_expiry_date and document_expiry_date < timezone.now().date():
            raise ValidationError({
                "document_expiry_date": "Expiry date must be in the future."
            })

        return cleaned_data


class VerifyNationalId(ModelMutation):
    class Arguments:
        id = graphene.ID(required=True, )
        input = VerifyNationalIdInput(
            description="Fields required to verify the national card.",
            required=True,
        )

    class Meta:
        description = "verifies the national id of the customer"
        model = models.UnverifiedNationals
        permissions = (PatientPermissions.VERIFY_NATIONAL_IDS,)
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.ADD_NATIONAL_ID

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        if bool(data.get("status") == UnverifiedNationalsStatuses.REJECTED) ^ \
                bool(data.get("rejection_reason")):
            raise ValidationError(
                {
                    "rejection_reason": "required when rejecting national id"})

        cleaned_data = super().clean_input(info, instance, data, input_cls=None)
        return cleaned_data

    @classmethod
    @transaction.atomic
    def save(cls, info, instance, cleaned_input):

        if instance.status == UnverifiedNationalsStatuses.APPROVED:
            user = instance.user
            user.national_id = instance.nationalId
            user.save(update_fields=["national_id"])

            patient = user.patient
            patient.nationality = instance.nationality
            patient.id_type = instance.id_type
            patient.document_expiry_date = instance.document_expiry_date
            patient.save(update_fields=["nationality", "id_type", "document_expiry_date"])

            keycloak_user_data = {"attributes": {}}
            prepare_user_attributes(user, keycloak_user_data["attributes"])
            KeycloakAPI().update_user(
                user.sso_id,
                {
                    'attributes': keycloak_user_data["attributes"]
                })

        super().save(info, instance, cleaned_input)


# mutation to check if customer account is exist before register
class CheckCustomerAccountExist(ModelMutation):
    is_email_exists = graphene.Boolean()
    is_contact_number_exists = graphene.Boolean()

    class Arguments:
        recaptcha_token = graphene.String(required=False)
        email = graphene.String(required=False)
        contact_number = graphene.String(required=False)

    class Meta:
        description = "check if customer account is exist"
        model = models.Patient
        error_type_class = AccountError
        error_type_field = "account_errors"

    @classmethod
    def perform_mutation(cls, _root, info, **data):

        email = data.get("email")
        contact_number = data.get("contact_number")

        email_exists = None
        contact_number_exists = None

        if settings.RECAPTCHA_ENABLED:
            recaptcha_token = data.get("recaptcha_token")
            if not recaptcha_token:
                raise ValidationError(
                    {
                        "recaptcha_token": ValidationError(
                            "This field is required.", code=AccountErrorCode.REQUIRED
                        )
                    }
                )

            validate_recaptcha_token(recaptcha_token)

        if email is not None:
            if not is_email_valid(email):
                raise ValidationError({"email": "email is not valid"})
            email_exists = models.User.objects.filter(email=email).exists()

        if contact_number is not None:
            if contact_number == '':
                raise ValidationError(
                    {"phone_number": "The contact number entered is not valid."})
            validate_possible_number(contact_number)
            contact_number_exists = models.User.objects.filter(
                mobile=contact_number,app_type=AppTypes.CUSTOMER).exists()

        return CheckCustomerAccountExist(is_email_exists=email_exists,
                                         is_contact_number_exists=contact_number_exists)


class CustomerDelete(BaseMutation):
    user = graphene.Field(UserType, description="The user that was deleted.")

    class Arguments:
        input = CustomerDeleteInput(
            description="Fields required to delete the customer.",
            required=True,
        )

    class Meta:
        description = "Deletes the customer."
        model = models.Patient
        error_type_class = AccountError
        error_type_field = "account_errors"
        feature = Feature.DELETE_CUSTOMER_ACCOUNT

    @classmethod
    def clean_input(cls, info, instance, data, input_cls=None):
        mobile = data.get("mobile")
        mobile_token = data.get("mobile_verification_token")

        email = data.get("email")
        email_token = data.get("email_verification_token")

        if not (bool(email) ^ bool(mobile)):
            raise ValidationError("email or mobile must be passed (not both)")

        if mobile and not mobile_token:
            raise ValidationError(
                {"mobile_verification_token": "required when passing mobile."})

        if email and not email_token:
            raise ValidationError(
                {"email_verification_token": "required when passing email."})

        if mobile:
            validate_possible_number(mobile)
            check_contact_number_verification(mobile, mobile_token)

        if email:
            check_email_verification(email, email_token)

    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        cls.clean_input(info, None, data.get("input"))

        email = data.get("input").get("email")
        mobile = data.get("input").get("mobile")
        delete_reason = data.get("input").get("delete_reason")

        user = None
        if email:
            user = models.User.objects.filter(email=email).first()
        if mobile:
            user = models.User.objects.filter(mobile=mobile).first()

        if not (user and user.is_consumer):
            raise ValidationError(
                { 'email'if email else 'phone_number':
                      "Patient does not exist"},
                code=AccountErrorCode.NOT_FOUND,)

        set_current_user(user)
        KeycloakAPI().delete_user(user.sso_id)
        user.delete_reason = delete_reason
        if user.mobile:
            user.mobile = str(user.mobile) + "_deleted_"+ str(user.id)
        user.save()
        user.delete()
        for child in user.dependents.all():
            KeycloakAPI().delete_user(child.sso_id)
            child.delete_reason = delete_reason
            child.save()
            child.delete()

        return CustomerDelete(user=user)


class UpdatePatientHealthParameterLastSyncTime(ModelMutation):

    class Arguments:
        last_sync_time = graphene.DateTime(required=True)

    class Meta:
        description = "Updates the account of the logged-in customer."
        exclude = ["password"]
        model = models.Patient
        error_type_class = PatientError
        error_type_field = "patient_errors"

    @classmethod
    def check_permissions(cls, context, permissions=None):
        return context.user.is_consumer


    @classmethod
    @transaction.atomic
    def perform_mutation(cls, _root, info, **data):
        patient = get_current_user().patient
        patient.health_parameter_last_sync_time = data.get("last_sync_time")
        patient.save(update_fields=["health_parameter_last_sync_time"])
        return UpdatePatientHealthParameterLastSyncTime(patient=patient)
