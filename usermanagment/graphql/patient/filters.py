import django_filters
from graphene_django.filter import Global<PERSON><PERSON>ilter
from django.db.models import Q
from usermanagment.graphql.account.enums import PersonGenderEnum
from usermanagment.graphql.core.filters import ListObjectType<PERSON>ilter, EnumFilter
from usermanagment.graphql.core.types import FilterInputObjectType
from usermanagment.graphql.patient.enums import (
    PatientIdTypeEnum,
    ResidencyTypesEnum,
    MaritalStatusesEnum, UnverifiedNationalsStatusesEnum,
)
from usermanagment.patient.models import Patient


def filter_by_id_type(qs, _, value):
    if value:
        qs = qs.filter(id_type__in=value)

    return qs

def filter_by_first_name(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__first_name=value) | Q(user__first_name_ar=value)
        )
    return qs

def filter_by_second_name(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__second_name=value) | Q(user__second_name_ar=value)
        )


    return qs

def filter_by_third_name(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__third_name=value) | Q(user__third_name_ar=value)
        )

    return qs

def filter_by_last_name(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__last_name=value) | Q(user__last_name_ar=value)
        )

    return qs

def filter_by_full_name(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__full_name=value) | Q(user__full_name_ar=value)
        )
    return qs

def filter_by_first_name__icontains(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__first_name__icontains=value) | Q(user__first_name_ar__icontains=value)
        )

    return qs

def filter_by_second_name__icontains(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__second_name__icontains=value) | Q(user__second_name_ar__icontains=value)
        )

    return qs

def filter_by_third_name__icontains(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__third_name__icontains=value) | Q(user__third_name_ar__icontains=value)
        )

    return qs

def filter_by_last_name__icontains(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__last_name__icontains=value) | Q(user__last_name_ar__icontains=value)
        )

    return qs

def filter_by_full_name__icontains(qs, _, value):
    if value:
        qs = qs.filter(
            Q(user__full_name__icontains=value) | Q(user__full_name_ar__icontains=value)
        )
    return qs

def filter_by_residency_type(qs, _, value):
    if value:
        qs = qs.filter(residency_type__in=value)

    return qs


def filter_by_marital_status(qs, _, value):
    if value:
        qs = qs.filter(marital_status__in=value)

    return qs


def filter_by_unverified_nationals_status(qs, _, value):
    if value:
        qs = qs.filter(status__in=value)

    return qs


def filter_by_gender(qs, _, value):
    if value:
        return qs.filter(user__gender=value)
    return qs


class UnverifiedNationalsFilter(django_filters.FilterSet):
    status = ListObjectTypeFilter(
        input_class=UnverifiedNationalsStatusesEnum,
        method=filter_by_unverified_nationals_status
    )


class PatientFilter(django_filters.FilterSet):
    id_type = ListObjectTypeFilter(
        input_class=PatientIdTypeEnum, method=filter_by_id_type
    )

    residency_type = ListObjectTypeFilter(
        input_class=ResidencyTypesEnum, method=filter_by_residency_type
    )

    marital_status = ListObjectTypeFilter(
        input_class=MaritalStatusesEnum, method=filter_by_marital_status
    )

    date_of_birth = django_filters.DateFilter()

    preferred_language = GlobalIDFilter(field_name="user__preferred_language")

    national_id_number = django_filters.CharFilter(
        field_name="user__national_id", lookup_expr="exact"
    )

    national_id_number__icontains = django_filters.CharFilter(
        field_name="user__national_id", lookup_expr="icontains"
    )

    first_name = django_filters.CharFilter(
        method=filter_by_first_name
    )


    first_name__icontains = django_filters.CharFilter(
        method=filter_by_first_name__icontains
    )

    second_name = django_filters.CharFilter(
        method= filter_by_second_name
    )

    second_name__icontains = django_filters.CharFilter(
        method=filter_by_second_name__icontains
    )

    third_name = django_filters.CharFilter(
        method=filter_by_third_name
    )


    third_name__icontains = django_filters.CharFilter(
        method=filter_by_third_name__icontains
    )


    last_name = django_filters.CharFilter(
        method=filter_by_last_name
    )

    last_name__icontains = django_filters.CharFilter(
        method=filter_by_last_name__icontains
    )

    full_name = django_filters.CharFilter(
        method=filter_by_full_name
    )

    full_name__icontains = django_filters.CharFilter(
        method=filter_by_full_name__icontains
    )

    email = django_filters.CharFilter(
        field_name="user__email", lookup_expr="exact"
    )

    email__icontains = django_filters.CharFilter(
        field_name="user__email", lookup_expr="icontains"
    )

    gender = EnumFilter(
        input_class=PersonGenderEnum, method=filter_by_gender
    )

    class Meta:
        model = Patient
        fields = {
            'id': ['exact', 'icontains'],
            'contact_number': ['exact', 'icontains'],
            'blood_group': ['exact', 'icontains'],
            'nationality': ['exact', 'icontains'],
        }


class PatientFilterInput(FilterInputObjectType):
    class Meta:
        name = 'PatientFilterInput'
        filterset_class = PatientFilter


class UnverifiedNationalsFilterInput(FilterInputObjectType):
    class Meta:
        name = 'UnverifiedNationalsFilterInput'
        filterset_class = UnverifiedNationalsFilter
