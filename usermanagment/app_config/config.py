from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class AuthAppConfig(AppConfig):
    name = 'usermanagment.app_config'

    def ready(self):
        from usermanagment.auth.enums import AppTypes
        from usermanagment.keycloak_permissions import models
        from django.contrib.auth import models as auth_models
        from usermanagment.core.permissions import get_permissions_codename_with_hash
        from usermanagment.keycloak.keycloak_client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
        from usermanagment import settings
        from django.db import connection
        from usermanagment.core.permissions import VENDOR_ADMIN_AUTH_GROUP_NAME, \
            VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS, \
            PHARMACIST_USER_AUTH_GROUP_NAME, \
            PHARMACIST_USER_AUTH_GROUP_PERMISSIONS, \
            DOCTOR_USER_AUTH_GROUP_NAME, \
            DOCTOR_USER_AUTH_GROUP_PERMISSIONS, \
            DENTAL_HYGIENIST_USER_AUTH_GROUP_NAME, \
            DENTAL_HYGIENIST_USER_AUTH_GROUP_PERMISSIONS, \
            DIABETES_EDUCATOR_USER_AUTH_GROUP_NAME, \
            DIABETES_EDUCATOR_USER_AUTH_GROUP_PERMISSIONS, \
            FITNESS_COACH_USER_AUTH_GROUP_NAME, \
            FITNESS_COACH_USER_AUTH_GROUP_PERMISSIONS, \
            NUTRITIONIST_USER_AUTH_GROUP_NAME, \
            NUTRITIONIST_USER_AUTH_GROUP_PERMISSIONS, \
            OPTOMETRIST_USER_AUTH_GROUP_NAME, \
            OPTOMETRIST_USER_AUTH_GROUP_PERMISSIONS, \
            PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_NAME, \
            PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_PERMISSIONS, \
            PSYCHOLOGIST_USER_AUTH_GROUP_NAME, \
            PSYCHOLOGIST_USER_AUTH_GROUP_PERMISSIONS, \
            SOCIAL_WORKER_USER_AUTH_GROUP_NAME, \
            SOCIAL_WORKER_USER_AUTH_GROUP_PERMISSIONS, \
            NURSE_USER_AUTH_GROUP_PERMISSIONS, \
            PAYER_ADMIN_AUTH_GROUP_PERMISSIONS, \
            DHIC_USER_AUTH_GROUP_NAME, \
            DHIC_USER_AUTH_GROUP_PERMISSIONS, \
            PAYER_USER_AUTH_GROUP_PERMISSIONS, \
            PATIENT_USER_AUTH_GROUP_PERMISSIONS, \
            NURSE_USER_AUTH_GROUP_NAME, \
            PAYER_USER_AUTH_GROUP_NAME, \
            PAYER_ADMIN_AUTH_GROUP_NAME, \
            PATIENT_USER_AUTH_GROUP_NAME, \
            RECEPTIONIST_USER_AUTH_GROUP_NAME, \
            RECEPTIONIST_USER_AUTH_GROUP_PERMISSIONS, \
            RCM_AUTH_GROUP_PERMISSIONS, \
            RCM_AUTH_GROUP_NAME

        if not settings.SYNC_KEYCLOAK:
            return

        all_tables = connection.introspection.table_names()
        if "keycloak_permissions_keycloakpermission" not in all_tables:
            return

        codenames, codename_permissions = get_permissions_codename_with_hash()
        new_permissions = auth_models.Permission.objects.filter(
            key_cloak_permission__isnull=True,
            codename__in=codenames
        ).all()

        keycloak_permissions = []
        permissions_id_hash = {}
        for permission in new_permissions:
            permissions_id_hash[permission.id] = permission.codename
            target_users = codename_permissions[permission.codename].target_users

            is_staff = True if AppTypes.ADMIN in target_users else False
            is_vendor = True if AppTypes.VENDOR in target_users else False
            is_client = True if AppTypes.CUSTOMER in target_users else False
            is_aggregator = True if AppTypes.AGGREGATOR in target_users else False

            keycloak_permission = models.KeyCloakPermission(
                keycloak_role_id=None,
                is_staff=is_staff,
                is_vendor=is_vendor,
                is_client=is_client,
                is_aggregator=is_aggregator,
                permission=permission
            )
            keycloak_permissions.append(keycloak_permission)

        client_name = settings.KEYCLOAK_CONFIG['CLIENT_ID']
        client_id = KeycloakAPI().get_client_id(client_name)
        if not client_id:
            return None
        if keycloak_permissions:
            for keycloak_permission in keycloak_permissions:
                payload = {
                    "name": permissions_id_hash[keycloak_permission.permission.id],
                    "composite": False,
                }
                role_id = KeycloakAPI().client_add_role(client_id, payload)
                keycloak_permission.keycloak_role_id = role_id
            models.KeyCloakPermission.objects.bulk_create(keycloak_permissions)

        self.create_sync_group_keycloak(client_id,
                                        VENDOR_ADMIN_AUTH_GROUP_NAME,
                                        VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        PHARMACIST_USER_AUTH_GROUP_NAME,
                                        PHARMACIST_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        DOCTOR_USER_AUTH_GROUP_NAME,
                                        DOCTOR_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        NURSE_USER_AUTH_GROUP_NAME,
                                        NURSE_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        RECEPTIONIST_USER_AUTH_GROUP_NAME,
                                        RECEPTIONIST_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        DENTAL_HYGIENIST_USER_AUTH_GROUP_NAME,
                                        DENTAL_HYGIENIST_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        DIABETES_EDUCATOR_USER_AUTH_GROUP_NAME,
                                        DIABETES_EDUCATOR_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        FITNESS_COACH_USER_AUTH_GROUP_NAME,
                                        FITNESS_COACH_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        NUTRITIONIST_USER_AUTH_GROUP_NAME,
                                        NUTRITIONIST_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        OPTOMETRIST_USER_AUTH_GROUP_NAME,
                                        OPTOMETRIST_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_NAME,
                                        PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        PSYCHOLOGIST_USER_AUTH_GROUP_NAME,
                                        PSYCHOLOGIST_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        SOCIAL_WORKER_USER_AUTH_GROUP_NAME,
                                        SOCIAL_WORKER_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)

        self.create_sync_group_keycloak(client_id,
                                        RCM_AUTH_GROUP_NAME,
                                        RCM_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.VENDOR)


        self.create_sync_group_keycloak(client_id,
                                        PAYER_USER_AUTH_GROUP_NAME,
                                        PAYER_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.PAYER)

        self.create_sync_group_keycloak(client_id,
                                        PAYER_ADMIN_AUTH_GROUP_NAME,
                                        PAYER_ADMIN_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.PAYER)

        self.create_sync_group_keycloak(client_id,
                                        PATIENT_USER_AUTH_GROUP_NAME,
                                        PATIENT_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.CUSTOMER)

        self.create_sync_group_keycloak(client_id,
                                        DHIC_USER_AUTH_GROUP_NAME,
                                        DHIC_USER_AUTH_GROUP_PERMISSIONS,
                                        AppTypes.ADMIN)

    def create_sync_group_keycloak(self, client_id, group_name, group_permissions,
                                   app_type):
        from usermanagment.graphql.account.mutations.permission_group import \
            PermissionGroupCreate
        from usermanagment.core.permissions import get_permissions
        from usermanagment.keycloak.keycloak_client import KeycloakAPI
        from django.contrib.auth import models as auth_models
        from usermanagment.keycloak_permissions.models import GroupConfiguration

        permissions = get_permissions(permissions=[perm.value for perm in
                                                   group_permissions])

        local_permissions_codenames = [perm.value.split(".")[1] for perm in
                                       group_permissions]
        db_permissions_codenames = [perm.codename for perm in permissions]

        if len(permissions) != len(group_permissions) or \
                local_permissions_codenames.sort() == db_permissions_codenames.sort:
            logger.error("Defined permissions are not the same as database permissions"
                         ", make sure to migrate the latest permissions update")
            raise SystemExit

        group, is_created = auth_models.Group.objects.get_or_create(
            name=group_name)

        roles = PermissionGroupCreate.prepare_keycloak_roles(permissions)
        keycloak_group = None if is_created else GroupConfiguration.objects.filter(
            group_id=group.id).first()
        keycloak_group_id = keycloak_group.keycloak_group_id if keycloak_group else None

        created_keycloak_group_id = KeycloakAPI().create_update_group(
            name=group.name,
            client_id=client_id,
            added_roles=roles,
            added_users=[],
            removed_roles=[],
            removed_users=[],
            is_create=is_created,
            group_id=keycloak_group_id)

        if is_created:
            group_configuration = GroupConfiguration()
            group_configuration.keycloak_group_id = created_keycloak_group_id
            group_configuration.group = group
            group_configuration.group_type = app_type
            group_configuration.is_editable = False
            group_configuration.is_global = True
            group_configuration.save()
            group.group_configuration = group_configuration

        group.permissions.add(*permissions)
        group.save()
