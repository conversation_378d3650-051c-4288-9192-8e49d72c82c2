from threading import Thread

from django.db.models.signals import pre_delete, post_save
from django.dispatch import receiver
from graphene import Node

from .models import Doctor
from ..open_search.open_search_service import OpenSearchService


def sync_doctor(instance):
    open_search_service = OpenSearchService()
    open_search_service.sync_doctors([instance])


@receiver(post_save, sender=Doctor)
def open_search_save_sync(sender, instance: Doctor, **kwargs):
    Thread(target=sync_doctor,
           args=(instance,), daemon=False).start()


def delete_doctor(instance):
    id = Node.to_global_id("Doctor", instance.id)
    open_search_service = OpenSearchService()
    open_search_service.delete(id)


@receiver(pre_delete, sender=Doctor)
def open_search_delete_sync(sender, instance: Doctor, **kwargs):
    Thread(target=delete_doctor,
           args=(instance,), daemon=False).start()
