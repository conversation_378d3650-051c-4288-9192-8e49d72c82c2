# Generated by Django 3.2.12 on 2022-11-10 15:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager
import usermanagment.account.models.user
import versatileimagefield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vendor', '0023_branch_accepts_delivery'),
        ('account', '0032_alter_systempermission_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='Doctor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('national_id', models.Char<PERSON><PERSON>(max_length=255, unique=True)),
                ('is_national_id_public', models.<PERSON><PERSON>anField(default=False)),
                ('license_number', models.Char<PERSON>ield(max_length=255, null=True, unique=True)),
                ('is_license_number_public', models.BooleanField(default=False)),
                ('first_name', models.CharField(max_length=255)),
                ('second_name', models.CharField(blank=True, max_length=255, null=True)),
                ('third_name', models.CharField(blank=True, max_length=255, null=True)),
                ('last_name', models.CharField(max_length=255)),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], max_length=6)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('is_date_of_birth_public', models.BooleanField(default=False)),
                ('languages', models.JSONField(blank=True, null=True)),
                ('is_languages_public', models.BooleanField(default=False)),
                ('availability_status', models.CharField(choices=[('Unavailable', 'Unavailable'), ('Available Level 1', 'Available L1'), ('Available Level 2', 'Available L2')], default='Unavailable', max_length=30)),
                ('years_of_experience', models.IntegerField()),
                ('is_years_of_experience_public', models.BooleanField(default=False)),
                ('photo', versatileimagefield.fields.VersatileImageField(blank=True, null=True, upload_to='doctor-photos')),
                ('bio', models.TextField(blank=True, null=True)),
                ('second_mobile_number', usermanagment.account.models.user.PossiblePhoneNumberField(blank=True, default='', max_length=128, region=None)),
                ('is_second_mobile_number_public', models.BooleanField(default=False)),
                ('contact_number', usermanagment.account.models.user.PossiblePhoneNumberField(max_length=128, null=True, region=None)),
                ('is_contact_number_public', models.BooleanField(default=False)),
                ('social_links', models.JSONField(blank=True, null=True)),
                ('is_social_links_public', models.BooleanField(default=False)),
                ('is_address_public', models.BooleanField(default=False)),
                ('total_ratings', models.IntegerField(default=0)),
                ('ratings_sum', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('is_active', models.BooleanField(default=False)),
                ('appointment_slot_time_period', models.IntegerField(default=30)),
                ('appointment_types', models.JSONField(blank=True, null=True)),
                ('address', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='account.address')),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='HealthCondition',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Specialization',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True)),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='doctor.specialization')),
            ],
            options={
                'abstract': False,
            },
            managers=[
                ('tree', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Qualification',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('code', models.TextField()),
                ('from_date', models.DateField(null=True)),
                ('to_date', models.DateField(null=True)),
                ('issuer', models.TextField()),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='qualifications', to='doctor.doctor')),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='HealthConditionSpecialization',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('health_condition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='doctor.healthcondition')),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('specialization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='doctor.specialization')),
            ],
            options={
                'unique_together': {('health_condition', 'specialization')},
            },
        ),
        migrations.AddField(
            model_name='healthcondition',
            name='specializations',
            field=models.ManyToManyField(related_name='health_conditions', through='doctor.HealthConditionSpecialization', to='doctor.Specialization'),
        ),
        migrations.CreateModel(
            name='DoctorSpecialization',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_specialization', to='doctor.doctor')),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('specialization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_specialization', to='doctor.specialization')),
            ],
            options={
                'unique_together': {('doctor', 'specialization')},
            },
        ),
        migrations.CreateModel(
            name='DoctorAvailabilityAudit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('Unavailable', 'Unavailable'), ('Available Level 1', 'Available L1'), ('Available Level 2', 'Available L2')], max_length=30)),
                ('period', models.PositiveIntegerField(blank=True, null=True)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_audits', to='doctor.doctor')),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='doctor',
            name='specializations',
            field=models.ManyToManyField(related_name='doctors', through='doctor.DoctorSpecialization', to='doctor.Specialization'),
        ),
        migrations.AddField(
            model_name='doctor',
            name='user',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='doctor', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='doctor',
            name='vendor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctors', to='vendor.vendor'),
        ),
        migrations.CreateModel(
            name='DoctorWorkingHour',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.CharField(choices=[('SAT', 'SAT'), ('SUN', 'SUN'), ('MON', 'MON'), ('TUE', 'TUE'), ('WED', 'WED'), ('THU', 'THU'), ('FRI', 'FRI')], max_length=3)),
                ('open_time', models.TimeField()),
                ('close_time', models.TimeField()),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_hours', to='doctor.doctor')),
            ],
            options={
                'unique_together': {('day', 'doctor', 'open_time', 'close_time')},
            },
        ),
    ]
