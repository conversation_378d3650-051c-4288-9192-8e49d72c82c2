# Generated by Django 3.2.12 on 2023-03-22 13:05

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('doctor', '0008_doctor_in_call'),
    ]

    operations = [
        migrations.RenameField(
            model_name='doctor',
            old_name='is_contact_number_public',
            new_name='is_mobile_number_public',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='address',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='contact_number',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='date_of_birth',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='first_name',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='gender',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='last_name',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='national_id',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='photo',
        ),
        migrations.RemoveField(
            model_name='doctor',
            name='second_name',
        ),
        migrations.Remove<PERSON>ield(
            model_name='doctor',
            name='third_name',
        ),
    ]
