# Generated by Django 3.2.12 on 2023-07-03 09:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('doctor', '0014_doctoravailability_vendor'),
    ]

    operations = [
        migrations.CreateModel(
            name='HealthProgramsDoctorsView',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('doctor_id', models.BigIntegerField()),
                ('program_id', models.BigIntegerField()),
                ('network_id', models.BigIntegerField()),
                ('network_name', models.Char<PERSON>ield(max_length=256)),
                ('program_name', models.<PERSON>r<PERSON><PERSON>(max_length=256)),
            ],
            options={
                'db_table': 'health_programs_doctors_view',
                'managed': False,
            },
        ),
    ]
