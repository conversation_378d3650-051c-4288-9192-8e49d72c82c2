# Generated by Django 3.2.12 on 2023-03-06 09:36

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ('doctor', '0005_remove_doctor_availability_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='DoctorAvailability',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('status', models.CharField(choices=[('Unavailable', 'Unavailable'), (
                'Available Level 1', 'Available L1'), ('Available Level 2',
                                                       'Available L2')],
                                            max_length=30)),
                ('period', models.DurationField(null=True)),
                ('start_time', models.DateTimeField(db_index=True,
                                                    default=django.utils.timezone.now)),
                ('end_time', models.DateTimeField(db_index=True, null=True)),
                ('doctor',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                   related_name='availability_audits',
                                   to='doctor.doctor')),
            ],
        ),
        migrations.DeleteModel(
            name='DoctorAvailabilityAudit',
        ),
    ]
