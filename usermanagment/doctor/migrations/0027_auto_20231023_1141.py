# Generated by Django 3.2.12 on 2023-10-23 08:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('doctor', '0026_auto_20231003_1054'),
    ]

    operations = [
        migrations.AlterField(
            model_name='doctor',
            name='average_rating',
            field=models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=3),
        ),
        migrations.AlterField(
            model_name='doctor',
            name='deleted',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='doctor',
            name='in_call',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='doctor',
            name='visit_price',
            field=models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=4),
        ),
        migrations.Alter<PERSON>ield(
            model_name='doctor',
            name='years_of_experience',
            field=models.IntegerField(db_index=True),
        ),
        migrations.AlterField(
            model_name='doctoravailability',
            name='status',
            field=models.CharField(choices=[('Unavailable', 'Unavailable'), ('Available Level 1', 'Available L1'), ('Available Level 2', 'Available L2')], db_index=True, max_length=30),
        ),
        migrations.AlterField(
            model_name='doctorexperience',
            name='job_title',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='doctorspecialization',
            name='code',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='doctorworkinghour',
            name='close_time',
            field=models.TimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='doctorworkinghour',
            name='day',
            field=models.CharField(choices=[('SAT', 'SAT'), ('SUN', 'SUN'), ('MON', 'MON'), ('TUE', 'TUE'), ('WED', 'WED'), ('THU', 'THU'), ('FRI', 'FRI')], db_index=True, max_length=3),
        ),
        migrations.AlterField(
            model_name='doctorworkinghour',
            name='open_time',
            field=models.TimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='healthconditionspecialization',
            name='code',
            field=models.CharField(db_index=True, max_length=255, null=True),
        ),
    ]
