# Generated by Django 3.2.12 on 2023-05-25 06:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0031_auto_20230525_0932'),
        ('doctor', '0010_remove_doctor_license_number'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='doctor',
            name='specializations',
        ),
        migrations.RemoveField(
            model_name='healthcondition',
            name='specializations',
        ),
        migrations.AddField(
            model_name='doctorspecialization',
            name='code',
            field=models.CharField(max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='healthconditionspecialization',
            name='code',
            field=models.CharField(max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='doctorspecialization',
            name='doctor',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='specializations', to='doctor.doctor'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='healthconditionspecialization',
            name='health_condition',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='specializations', to='doctor.healthcondition'),
        ),
        migrations.AlterUniqueTogether(
            name='doctorspecialization',
            unique_together={('code', 'doctor')},
        ),
        migrations.AlterUniqueTogether(
            name='healthconditionspecialization',
            unique_together={('code', 'health_condition')},
        ),
        migrations.RemoveField(
            model_name='doctorspecialization',
            name='created',
        ),
        migrations.RemoveField(
            model_name='doctorspecialization',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='doctorspecialization',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='doctorspecialization',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='doctorspecialization',
            name='specialization',
        ),
        migrations.RemoveField(
            model_name='healthconditionspecialization',
            name='created',
        ),
        migrations.RemoveField(
            model_name='healthconditionspecialization',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='healthconditionspecialization',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='healthconditionspecialization',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='healthconditionspecialization',
            name='specialization',
        ),
        migrations.DeleteModel(
            name='Specialization',
        ),
    ]
