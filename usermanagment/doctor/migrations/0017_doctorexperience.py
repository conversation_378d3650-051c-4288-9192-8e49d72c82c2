# Generated by Django 3.2.12 on 2023-08-06 11:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('doctor', '0016_doctor_visit_price'),
    ]

    operations = [
        migrations.CreateModel(
            name='DoctorExperience',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('job_title', models.Char<PERSON>ield(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('provider', models.CharField(max_length=255)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('doctor',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                   related_name='experiences', to='doctor.doctor')),
            ],
        ),
    ]
