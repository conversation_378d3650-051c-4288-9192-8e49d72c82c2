# Generated by Django 3.2.12 on 2023-03-05 12:54

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ('doctor', '0003_doctor_seniority'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='doctoravailabilityaudit',
            name='created',
        ),
        migrations.RemoveField(
            model_name='doctoravailabilityaudit',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='doctoravailabilityaudit',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='doctoravailabilityaudit',
            name='modified_by',
        ),
        migrations.AddField(
            model_name='doctoravailabilityaudit',
            name='end_time',
            field=models.DateTimeField(db_index=True, null=True),
        ),
        migrations.AddField(
            model_name='doctoravailabilityaudit',
            name='start_time',
            field=models.DateTimeField(db_index=True,
                                       default=django.utils.timezone.now),
        ),
        migrations.Alter<PERSON>ield(
            model_name='doctoravailabilityaudit',
            name='period',
            field=models.DurationField(blank=True, null=True),
        ),
    ]
