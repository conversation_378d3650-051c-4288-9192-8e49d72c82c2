from django.db.models import TextChoices


class DoctorAvailabilityStatuses(TextChoices):
    UNAVAILABLE = "Unavailable"
    AVAILABLE_L1 = "Available Level 1"
    AVAILABLE_L2 = "Available Level 2"


class AppointmentType(TextChoices):
    ONLINE = "Online"
    ONSITE = "Onsite"
    AT_HOME = "At_home"
    Diagnostic = "Diagnostic"


class DoctorSeniority(TextChoices):
    GP = "General Practitioner"
    SP = "Specialist Practitioner"
    CP = "Consultant Practitioner"


class RatingAction(TextChoices):
    UPDATE = "Update"
    CREATE = "Create"
