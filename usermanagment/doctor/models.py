from django.db import models
from django.utils import timezone

from usermanagment import settings
from . import DoctorAvailabilityStatuses, Doctor<PERSON><PERSON>ori<PERSON>
from ..account.models import PossiblePhoneNumber<PERSON>ield
from ..auth.permissions import AccountPermissions
from ..core.models import AuditModel, SoftDeletionModel, SoftDeletionManager
from ..graphql.utils.request_utils import get_current_user
from ..vendor.enums import DayOfWeekEnum
from ..vendor.models import Vendor


class DoctorManager(SoftDeletionManager):
    def accessible_doctors(self):
        qs = self
        current_user = get_current_user()
        if not current_user.is_superuser:
            vendor_id = current_user.vendor_id
            if vendor_id:
                qs = qs.filter(vendor_id=current_user.vendor_id)
            else:
                qs = qs.filter(vendor__is_active=True, user__is_active=True)
        return qs

    def is_accessible_property(self, user, is_public_property):
        if is_public_property:
            return True
        else:
            current_user = get_current_user()
            if current_user.is_authenticated and not current_user.is_consumer:
                return True
            else:
                return False


class Doctor(AuditModel, SoftDeletionModel):
    is_national_id_public = models.BooleanField(default=False)
    is_license_number_public = models.BooleanField(default=False)
    is_date_of_birth_public = models.BooleanField(default=False)
    languages = models.ManyToManyField(
        "account.Language",
        related_name="doctors",
        blank=True,
    )

    notification_email = models.EmailField(null=True, blank=True)

    is_languages_public = models.BooleanField(default=False)

    years_of_experience = models.IntegerField(null=True, blank=True, db_index=True)

    is_years_of_experience_public = models.BooleanField(default=False)

    bio = models.TextField(null=True, blank=True)

    second_mobile_number = PossiblePhoneNumberField(blank=True, default="")

    is_second_mobile_number_public = models.BooleanField(default=False)

    is_mobile_number_public = models.BooleanField(default=False)

    social_links = models.JSONField(null=True, blank=True, )

    is_social_links_public = models.BooleanField(default=False)

    can_accept_call = models.BooleanField(null=False, blank=False, default=True, db_index=True)

    is_address_public = models.BooleanField(default=False)

    total_ratings = models.IntegerField(default=0)
    ratings_sum = models.DecimalField(default=0, decimal_places=2, max_digits=10)
    average_rating = models.DecimalField(default=0, decimal_places=2, max_digits=6,
                                         db_index=True)

    online_visit_price = models.DecimalField(decimal_places=2, max_digits=6, null=True,
                                             blank=True)

    onsite_visit_price = models.DecimalField(decimal_places=2, max_digits=6, null=True,
                                             blank=True)

    at_home_visit_price = models.DecimalField(decimal_places=2, max_digits=6,
                                              null=True, blank=True)

    vendor = models.ForeignKey(
        "vendor.Vendor",
        related_name="doctors",
        blank=False,
        null=False,
        on_delete=models.CASCADE,
        db_index=True,
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        related_name="doctor",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    appointment_slot_time_period = models.IntegerField(default=30, null=False,
                                                       blank=False)

    appointment_types = models.JSONField(null=True)

    seniority = models.CharField(
        max_length=30,
        default=DoctorSeniority.GP,
        choices=DoctorSeniority.choices,
        null=False, blank=False
    )

    health_license_number = models.CharField(max_length=255, null=True, blank=True,
                                             db_index=True)
    health_license_start_date = models.DateField(null=True, blank=True, db_index=True)
    health_license_end_date = models.DateField(null=True, blank=True, db_index=True)

    first_available_date_time = models.DateTimeField(null=True, blank=True, db_index=True)

    is_first_available_date_time_null = models.IntegerField(null=True, blank=True,db_index=True,default=True)

    objects = DoctorManager()

    class Meta:
        ordering = ['is_first_available_date_time_null','first_available_date_time','pk']


class HealthProgramsDoctorsView(models.Model):
    doctor_id = models.BigIntegerField()
    program_id = models.BigIntegerField()
    network_id = models.BigIntegerField()
    network_name = models.CharField(max_length=256)
    program_name = models.CharField(max_length=256)

    class Meta:
        managed = False
        db_table = 'health_programs_doctors_view'


class Qualification(AuditModel):
    code = models.TextField()
    from_date = models.DateField(null=True, blank=False)
    to_date = models.DateField(null=True, blank=False)
    issuer = models.TextField()
    doctor = models.ForeignKey(
        Doctor, null=True, related_name="qualifications", on_delete=models.CASCADE,
    )


class DoctorSpecialization(models.Model):
    code = models.CharField(max_length=255, null=False, blank=False, db_index=True)
    doctor = models.ForeignKey(
        Doctor, null=True, related_name="specializations", on_delete=models.CASCADE,
    )

    class Meta:
        unique_together = (("code", "doctor"),)


class DoctorAvailability(models.Model):
    status = models.CharField(
        max_length=30,
        null=False,
        blank=False,
        choices=DoctorAvailabilityStatuses.choices,
        db_index=True
    )

    period = models.DurationField(null=True, blank=False)

    doctor = models.ForeignKey(
        Doctor,
        related_name="availabilities",
        on_delete=models.CASCADE,
        db_index=True
    )

    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, db_index=True)

    start_time = models.DateTimeField(default=timezone.now, db_index=True)
    end_time = models.DateTimeField(null=True, db_index=True)


class DoctorWorkingHour(models.Model):
    day = models.CharField(
        max_length=3,
        choices=DayOfWeekEnum.CHOICES,
        null=False, blank=False,
        db_index=True
    )
    open_time = models.TimeField(blank=False, null=False, db_index=True)
    close_time = models.TimeField(blank=False, null=False, db_index=True)

    doctor = models.ForeignKey(Doctor, related_name="working_hours", null=False,
                               blank=False, on_delete=models.CASCADE)

    class Meta:
        unique_together = (("day", "doctor", "open_time", "close_time"),)


class HealthSymptom(AuditModel):
    name = models.CharField(max_length=255, unique=True)

    class Meta:
        ordering = ("created", "pk")


class HealthSymptomSpecialization(models.Model):
    code = models.CharField(max_length=255, null=True, blank=False, db_index=True)
    health_symptom = models.ForeignKey(
        HealthSymptom, null=True, related_name="specializations",
        on_delete=models.CASCADE,
    )

    class Meta:
        unique_together = (("code", "health_symptom"),)


# model for doctor's experiences
class DoctorExperience(models.Model):
    job_title = models.CharField(max_length=255, db_index=True)
    description = models.TextField(null=True, blank=True)
    provider = models.CharField(max_length=255)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    doctor = models.ForeignKey(
        Doctor, related_name="experiences", on_delete=models.CASCADE, db_index=True
    )


class DoctorActivityTracker(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    activity_date = models.DateField()
    count = models.IntegerField(default=1)


class SearchKeyword(models.Model):
    keyword = models.CharField(max_length=255, unique=True)
    count = models.IntegerField(default=1, db_index=True)
