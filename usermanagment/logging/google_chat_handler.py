import json
import re
import traceback
from datetime import datetime
from threading import Thread

import requests
from django.utils.log import AdminEmailHandler
from graphql import GraphQLError
from jwt import ExpiredSignatureError

from usermanagment import settings
from usermanagment.settings import HIDE_IS_EXPIRED_EXCEPTION_GOOGLE, HIDE_TOKEN_GOOGLE


class GoogleChatHandler(AdminEmailHandler):
    def send_mail(self, subject, message, *args, **kwargs):
        operation = kwargs.get('operation', '') or ''
        query = operation
        variables = {}
        operation_name = ''
        if isinstance(operation, dict):
            query = operation.get('query')
            if query is not None and "password" in query:
                query = re.sub(r"password\s?:\s(\".*?\")", r'password:"********"', query)
            variables = operation.get('variables', {})
            if variables is not None and "password" in variables:
                variables["password"] = "********"
            variables = json.dumps(variables)
            operation_name = operation.get('operationName')

        try:
            result = requests.post(
                settings.GOOGLE_CHAT_SPACE_URL,
                json={
                    "text": f"```{subject}```\n```operationName={operation_name}```\n```{query}```\n```variables={variables}```\n{message[:4000 - len(str(subject)) - len(str(query)) - len(str(variables))]}"
                })
            # print(f"GoogleChatHandler result => {result.text}")
        except Exception as e:
            print(f"GoogleChatHandler exception => {e}")
            traceback.print_exc()

    def emit(self, record):
        if not settings.GOOGLE_CHAT_SPACE_URL:
            return
        try:
            except_type = record.exc_info[0]
            if HIDE_IS_EXPIRED_EXCEPTION_GOOGLE and except_type == ExpiredSignatureError:
                return
        except:
            pass

        from usermanagment.graphql.utils.request_utils import get_current_context, \
            get_current_gql_operation

        time = getattr(record, 'asctime', datetime.fromtimestamp(record.created))
        request = getattr(record, 'request', get_current_context())
        token = "n/a"
        try:
            if not HIDE_TOKEN_GOOGLE:
                token = request.headers.get('Authorization')
        except:
            pass

        try:
            user = getattr(request, 'user')
            user_id = getattr(user, 'id', None)
            subject = '%s => user(%s) => %s (%s IP: %s): %s, token :%s' % (
                time,
                user_id,
                record.levelname,
                ('internal' if request.META.get('REMOTE_ADDR') in settings.INTERNAL_IPS
                 else 'EXTERNAL'),
                request.META.get('REMOTE_ADDR'),
                record.getMessage(),
                token
            )
        except Exception:
            subject = '%s => %s: %s token :%s' % (
                time,
                record.levelname,
                record.getMessage(),
                token
            )
        subject = self.format_subject(subject)

        operation = get_current_gql_operation()
        if operation and "operationName" in operation:
            operation_name = operation["operationName"]
            if operation_name is None and "query" in operation:
                new_operation_match = re.match(r"(.*?)\(.*", operation["query"])
                if new_operation_match:
                    operation_name = new_operation_match.group(1)
                    operation["operationName"] = operation_name
        if not operation and record.exc_info and (
                isinstance(record.exc_info[0], GraphQLError) or issubclass(
            record.exc_info[0], GraphQLError)):
            try:
                source_body = record.exc_info[1].source.body
                if "password" in source_body:
                    source_body = re.sub(r"password\s?:\s(\".*?\")",
                                          r'password:"********"',
                                          source_body)
                operation = {
                    'query': source_body
                }
            except:
                pass

        if not operation:
            try:
                if request is not None:
                    request_body = request.body.decode("utf-8")
                    if "password" in request_body:
                        request_body = re.sub(r"password\s?:\s(\".*?\")", r'password:"********"',
                                       request_body)
                    operation = {
                        "query": "request.body => " + request_body
                    }
            except Exception:
                traceback.print_exc()

        message = getattr(record, 'exc_text') or getattr(record, 'message', '')

        try:
            thread = Thread(target=self.send_mail,
                            args=(subject, message),
                            kwargs={'fail_silently': True, 'operation': operation}
                            )
            thread.start()
        except:
            self.handleError(record)
