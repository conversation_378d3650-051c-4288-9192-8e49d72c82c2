import re

from request_logging.middleware import LoggingMiddleware


class HTTPLoggingMiddleware(LoggingMiddleware):
    def _log_request(self, request, response, cached_request_body):
        pass

    def _log_resp(self, level, response, logging_context):
        if re.match('^application/json', response.get('Content-Type', ''), re.I):
            if response.streaming:
                # There's a chance that if it's streaming it's because large and it might hit
                # the max_body_length very often. Not to mention that StreamingHttpResponse
                # documentation advises to iterate only once on the content.
                # So the idea here is to just _not_ log it.
                self.logger.log(level, '(data_stream)', logging_context)
            else:
                self.logger.log(level, self._chunked_to_max(response.content),
                                logging_context)
