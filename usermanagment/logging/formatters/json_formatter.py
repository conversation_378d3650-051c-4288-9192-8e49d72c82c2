from datetime import datetime

from pythonjsonlogger.jsonlogger import <PERSON><PERSON><PERSON><PERSON>att<PERSON>

sensitive_headers = ["HTTP_AUTHORIZATION"]


class CustomJsonFormatter(JsonFormatter):

    def format(self, record):
        from usermanagment.graphql.utils.request_utils import get_current_user

        record.asctime = datetime.fromtimestamp(record.created).isoformat()
        """Formats a log record and serializes to json"""
        log_record = {
            'level': record.levelname,
            'user_id': get_current_user().id,
            'time': record.asctime,
            'service': 'User Management'
        }

        request = getattr(record, "request", None)
        response = getattr(record, "response", None)

        if request or response:
            log_record['message'] = 'request/response'
        elif hasattr(record, 'sql'):
            log_record['message'] = 'sql'
            log_record['sql'] = record.sql
        elif isinstance(record.msg, dict):
            log_record['message'] = record.msg
        else:
            log_record['message'] = record.getMessage()

        if record.exc_info:
            log_record['stack_trace'] = self.formatException(record.exc_info)
            log_record['ex_name'] = record.exc_info[0]
            record.exc_text = log_record['stack_trace']

        if record.stack_info:
            log_record['stack_info'] = self.formatStack(record.stack_info)

        if request:
            headers = getattr(request, 'META', {})
            log_record['request_body'] = getattr(request, 'body', None)
            log_record['request_headers'] = {
                k: v if k not in sensitive_headers else '*****' for
                k, v in headers.items() if k.startswith('HTTP_')}

        if response:
            streaming = getattr(response, 'streaming', False)
            log_record['response_body'] = getattr(response, 'content', None) if not streaming else "streaming"
            log_record['response_status_code'] = response.status_code
            log_record['response_headers'] = getattr(response, '_headers', {})

        log_record = self.process_log_record(log_record)

        return self.serialize_log_record(log_record)
