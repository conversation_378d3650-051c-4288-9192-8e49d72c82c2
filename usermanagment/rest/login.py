import json

from django.http import JsonResponse
from django.views import View
from rest_framework import serializers

from usermanagment.auth.enums import AppTypes


class LoginRequestSerializer(serializers.Serializer):
    app = serializers.ChoiceField(choices=AppTypes.choices(), required=True)
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True)


class Login(View):

    def post(self, request):
        try:
            data = json.loads(request.body.decode("utf-8"))
        except Exception:
            return JsonResponse({
                "success": False,
                "message": "Invalid JSON"
            }, status=400)

        validation = LoginRequestSerializer(data=data)

        if not validation.is_valid():
            return JsonResponse({
                "success": False,
                "message": "Invalid data",
                "errors": validation.errors
            }, status=400)

        create_token_data = {
            'app': data.get('app', None),
            'username': data.get('username', None),
            'password': data.get('password', None),
        }

        # result = SSOCreateToken().perform_mutation(None, None, **create_token_data)
        #
        # if result.account_errors:
        #     return JsonResponse({
        #         "success": False,
        #         "message": result.account_errors[0].message
        #     }, status=400, safe=False)
        #
        # data = {
        #     "accessToken": result.token
        # }

        return JsonResponse(data, status=200)
