import logging

import channels_graphql_ws
from asgiref.sync import sync_to_async
from channels.middleware import BaseMiddleware

logger = logging.getLogger(__name__)


class GraphqlWsConsumer(channels_graphql_ws.GraphqlWsConsumer):
    send_keepalive_every = 10

    def __init__(self, *args, **kwargs):
        from .graphql.api import schema
        self.schema = schema

        from graphene_django.settings import graphene_settings
        from graphene_django.views import instantiate_middleware
        self.middleware = list(instantiate_middleware(graphene_settings.MIDDLEWARE))

        super().__init__(*args, **kwargs)

    async def receive_json(self, content):
        if 'payload' not in content:
            content['payload'] = {}
        await super().receive_json(content)

    async def connect(self):

        user = self.scope.get('user')
        if not user or user.is_anonymous:
            await self.close(4003)
            return

        await super().connect()


class WebSocketAuthMiddleware(BaseMiddleware):

    def populate_scope(self, scope):
        # Add it to the scope if it's not there already
        if "user" not in scope:
            try:
                from .auth.middleware import AppendUserInfoToRequestMixin
                AppendUserInfoToRequestMixin().append_user_info_to_request(
                    scope,
                    self.extract_header_from_scope(scope, "authorization")
                )
            except Exception as e:
                from .auth.anonymous_user import AnonymousUser
                logger.error("UnAuthorized user trying to connect to websocket",
                             exc_info=e)
                scope['user'] = AnonymousUser()

    async def __call__(self, scope, receive, send):
        scope = dict(scope)
        # Scope injection/mutation per this middleware's needs.
        await sync_to_async(self.populate_scope)(scope)

        return await super().__call__(scope, receive, send)

    def extract_header_from_scope(self, scope, header_name):
        auth_header = list(
            filter(lambda header: bytes(header_name, 'utf-8') in header[0],
                   scope['headers']))
        if auth_header:
            auth_header = auth_header[0][1].decode('utf-8')
            scope['META'] = {"HTTP_AUTHORIZATION": auth_header}
            auth_header = auth_header.split()
            if len(auth_header) == 2:
                return auth_header[1]

        return None
