# Generated by Django 3.2.25 on 2025-03-17 16:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Complaint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True,
                                           serialize=False, verbose_name='ID')),
                ('insurance_sector', models.CharField(
                    choices=[('Health', 'Health'), ('Motor', 'Motor'),
                             ('General', 'General'), ('Other', 'Other')], db_index=True,
                    max_length=20)),
                ('client_type', models.CharField(
                    choices=[('Insured', 'Insured'), ('Third Party', 'Third Party'),
                             ('Brokers', 'Brokers'), ('Agents', 'Agents'),
                             ('Loss Adjustors', 'Loss Adjustors'),
                             ('Reinsurance', 'Reinsurance'),
                             ('Companies', 'Companies')], db_index=True,
                    max_length=20)),
                ('complaint_type', models.CharField(
                    choices=[('Approvals', 'Approvals'), ('Claims', 'Claims'),
                             ('Policy Admin', 'Policy Admin'),
                             ('Customer Service', 'Customer Service'),
                             ('Sales', 'Sales'), ('Others', 'Others')], db_index=True,
                    max_length=20)),
                ('approval_number',
                 models.CharField(blank=True, max_length=50, null=True)),
                (
                'claim_number', models.CharField(blank=True, max_length=50, null=True)),
                ('policy_number',
                 models.CharField(blank=True, max_length=50, null=True)),
                ('fact_of_complaint', models.TextField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                           to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
