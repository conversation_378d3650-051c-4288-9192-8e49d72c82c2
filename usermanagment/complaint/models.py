from django.db import models

from usermanagment import settings
from usermanagment.complaint.enums import InsuranceSector, ComplaintType


class Complaint(models.Model):
    insurance_sector = models.CharField(max_length=20, choices=InsuranceSector.choices,
                                        db_index=True, null=False, blank=False)
    complaint_type = models.CharField(max_length=20, choices=ComplaintType.choices,
                                      db_index=True, null=False, blank=False)
    approval_number = models.CharField(max_length=50, null=True, blank=True)
    claim_number = models.CharField(max_length=50, null=True, blank=True)
    policy_number = models.CharField(max_length=50, null=True, blank=True)
    fact_of_complaint = models.TextField(null=False, blank=False)
    upload_files = models.JSONField(null=True, blank=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        blank=False,
        null=False,
        on_delete=models.CASCADE, )
