# Generated by Django 3.2.12 on 2023-12-03 10:23

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0016_auto_20231023_1141'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='patientinsurancecarddetails',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='patientinsurancecarddetails',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='patientinsurancecarddetails',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='patientinsurancecarddetails',
            name='patient',
        ),
        migrations.RemoveField(
            model_name='patientmedicalhistory',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='patientmedicalhistory',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='medical_history',
        ),
        migrations.DeleteModel(
            name='PatientDisability',
        ),
        migrations.DeleteModel(
            name='PatientInsuranceCardDetails',
        ),
        migrations.DeleteModel(
            name='PatientMedicalHistory',
        ),
    ]
