# Generated by Django 3.2.25 on 2025-02-02 07:30

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0027_auto_20241218_1227'),
    ]

    operations = [
        migrations.AlterField(
            model_name='patient',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Resident Card', 'Resident Card'), ('Passport', 'Passport'), ('National Card', 'National Card'), ('Border Number', 'Border Number')], db_index=True, max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='unverifiednationals',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Resident Card', 'Resident Card'), ('Passport', 'Passport'), ('National Card', 'National Card'), ('Border Number', 'Border Number')], db_index=True, max_length=15, null=True),
        ),
    ]
