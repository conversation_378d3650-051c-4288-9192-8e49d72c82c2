# Generated by Django 3.0.6 on 2022-01-04 16:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import usermanagment.account.models
import versatileimagefield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientMedicalHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('has_asthma', models.BooleanField(default=False)),
                ('has_anemia', models.BooleanField(default=False)),
                ('has_birth_defects', models.BooleanField(default=False)),
                ('has_epilepsy', models.BooleanField(default=False)),
                ('has_high_blood_pressure', models.Bo<PERSON>anField(default=False)),
                ('has_heart_disease', models.BooleanField(default=False)),
                ('has_hepatitis_b', models.BooleanField(default=False)),
                ('has_high_cholesterol', models.BooleanField(default=False)),
                ('has_stroke', models.BooleanField(default=False)),
                ('has_TIA', models.BooleanField(default=False)),
                ('has_arrhythmia', models.BooleanField(default=False)),
                ('has_cancer', models.BooleanField(default=False)),
                ('has_clotting_disorder', models.BooleanField(default=False)),
                ('has_diabetes', models.BooleanField(default=False)),
                ('has_kidney_disease', models.BooleanField(default=False)),
                ('has_liver_disease', models.BooleanField(default=False)),
                ('has_thyroid_disease', models.BooleanField(default=False)),
                ('has_any_medical_problem', models.BooleanField(default=False)),
                ('ever_received_care', models.BooleanField(default=False)),
                ('was_care_continuous', models.BooleanField(default=False)),
                ('ever_hospitalized', models.BooleanField(default=False)),
                ('has_other_disease', models.BooleanField(default=False)),
                ('ever_operated_on', models.BooleanField(default=False)),
                ('operation_year', models.IntegerField(blank=True, default=0)),
                ('has_operation_complications', models.BooleanField(default=False)),
                ('take_prescription_medicines', models.BooleanField(default=False)),
                ('take_non_traditional_medications', models.BooleanField(default=False)),
                ('medication_dose', models.IntegerField(blank=True, default=0)),
                ('medication_freq', models.IntegerField(blank=True, default=0)),
                ('has_allergies', models.BooleanField(default=False)),
                ('allergy_type', models.CharField(blank=True, choices=[('Drug', 'Drug'), ('Food', 'Food'), ('Insect', 'Insect'), ('Latex', 'Latex'), ('Mold', 'Mold'), ('Pet', 'Pet'), ('Pollen', 'Pollen')], max_length=10)),
                ('reaction_type', models.CharField(blank=True, choices=[('Anaphylaxis', 'Anaphylaxis'), ('Rash', 'Rash'), ('Itching', 'Itching')], max_length=15)),
                ('drink_alcohol', models.CharField(choices=[('Current everyday', 'Current Everyday'), ('Current someday', 'Current Someday'), ('Former', 'Former'), ('Never', 'Never'), ('Unkown', 'Unknown')], max_length=20)),
                ('daily_quantity', models.IntegerField(blank=True, default=0)),
                ('drink_type', models.CharField(max_length=255)),
                ('drug_use', models.CharField(choices=[('Current everyday', 'Current Everyday'), ('Current someday', 'Current Someday'), ('Former', 'Former'), ('Never', 'Never'), ('Unkown', 'Unknown')], max_length=20)),
                ('smoking_freq', models.CharField(blank=True, choices=[('Current everyday', 'Current Everyday'), ('Current someday', 'Current Someday'), ('Former', 'Former'), ('Never', 'Never'), ('Unkown', 'Unknown')], max_length=20)),
                ('packs_no', models.CharField(blank=True, choices=[('less half pack', 'Less Half Pack'), ('half pack', 'Half Pack'), ('one pack', 'One Pack'), ('one and half pack', 'One And Half Pack'), ('two packs', 'Two Packs'), ('three packs', 'Three Packs')], max_length=20)),
                ('no_years_smoked', models.IntegerField(blank=True, default=0)),
                ('mother_bleeding_disorder', models.BooleanField(default=False)),
                ('mother_blood_clots', models.BooleanField(default=False)),
                ('mother_cancer', models.BooleanField(default=False)),
                ('mother_diabetes', models.BooleanField(default=False)),
                ('mother_heart_disease', models.BooleanField(default=False)),
                ('mother_high_blood_pressure', models.BooleanField(default=False)),
                ('mother_high_cholesterol', models.BooleanField(default=False)),
                ('father_bleeding_disorder', models.BooleanField(default=False)),
                ('father_blood_clots', models.BooleanField(default=False)),
                ('father_cancer', models.BooleanField(default=False)),
                ('father_diabetes', models.BooleanField(default=False)),
                ('father_heart_disease', models.BooleanField(default=False)),
                ('father_high_blood_pressure', models.BooleanField(default=False)),
                ('father_high_cholesterol', models.BooleanField(default=False)),
                ('brother_bleeding_disorder', models.BooleanField(default=False)),
                ('brother_blood_clots', models.BooleanField(default=False)),
                ('brother_cancer', models.BooleanField(default=False)),
                ('brother_diabetes', models.BooleanField(default=False)),
                ('brother_heart_disease', models.BooleanField(default=False)),
                ('brother_high_blood_pressure', models.BooleanField(default=False)),
                ('brother_high_cholesterol', models.BooleanField(default=False)),
                ('sister_bleeding_disorder', models.BooleanField(default=False)),
                ('sister_blood_clots', models.BooleanField(default=False)),
                ('sister_cancer', models.BooleanField(default=False)),
                ('sister_diabetes', models.BooleanField(default=False)),
                ('sister_heart_disease', models.BooleanField(default=False)),
                ('sister_high_blood_pressure', models.BooleanField(default=False)),
                ('sister_high_cholesterol', models.BooleanField(default=False)),
                ('gfather_bleeding_disorder', models.BooleanField(default=False)),
                ('gfather_blood_clots', models.BooleanField(default=False)),
                ('gfather_cancer', models.BooleanField(default=False)),
                ('gfather_diabetes', models.BooleanField(default=False)),
                ('gfather_heart_disease', models.BooleanField(default=False)),
                ('gfather_high_blood_pressure', models.BooleanField(default=False)),
                ('gfather_high_cholesterol', models.BooleanField(default=False)),
                ('gmother_bleeding_disorder', models.BooleanField(default=False)),
                ('gmother_blood_clots', models.BooleanField(default=False)),
                ('gmother_cancer', models.BooleanField(default=False)),
                ('gmother_diabetes', models.BooleanField(default=False)),
                ('gmother_heart_disease', models.BooleanField(default=False)),
                ('gmother_high_blood_pressure', models.BooleanField(default=False)),
                ('gmother_high_cholesterol', models.BooleanField(default=False)),
                ('ever_pregnant', models.BooleanField(default=False)),
                ('pregnancy_count', models.IntegerField(blank=True, default=0)),
                ('pregnancy_outcome', models.CharField(blank=True, choices=[('full term', 'Full Term'), ('Preterm', 'Preterm'), ('Miscarriages', 'Miscarriages'), ('Abortions', 'Abortions')], max_length=20)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'permissions': (('manage_patient_medical_history', 'Manage Patient Medical History.'),),
            },
        ),
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('id_type', models.CharField(choices=[('D005', 'Resident Card'), ('D004', 'Passport'), ('D003', 'National Card'), ('D002', 'GCC ID'), ('D001', 'Boarder Number')], max_length=10)),
                ('member_id', models.CharField(max_length=255, unique=True)),
                ('e_health_id', models.CharField(max_length=255, unique=True)),
                ('nationality', models.CharField(max_length=255)),
                ('national_id_number', models.CharField(max_length=255, unique=True)),
                ('first_name', models.CharField(max_length=255)),
                ('second_name', models.CharField(max_length=255)),
                ('third_name', models.CharField(max_length=255)),
                ('last_name', models.CharField(max_length=255)),
                ('gender', models.CharField(max_length=255)),
                ('date_of_birth', models.DateField()),
                ('dob_hijri', models.DateField()),
                ('contact_number', usermanagment.account.models.PossiblePhoneNumberField(max_length=255, region=None)),
                ('email', models.EmailField(max_length=254)),
                ('blood_group', models.CharField(max_length=255)),
                ('preferred_language', models.CharField(max_length=255)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('medical_history', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='patient', to='patient.PatientMedicalHistory')),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='patient', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'permissions': (('manage_patients', 'Manage Patients.'),),
            },
        ),
        migrations.CreateModel(
            name='PatientInsuranceCardDetails',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member_id', models.CharField(max_length=255)),
                ('company_name', models.CharField(max_length=255)),
                ('network_name', models.CharField(blank=True, max_length=255, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('card_front_side_image', versatileimagefield.fields.VersatileImageField(upload_to='patient-insurance-cards')),
                ('card_back_side_image', versatileimagefield.fields.VersatileImageField(upload_to='patient-insurance-cards')),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instance_cards', to='patient.Patient')),
            ],
            options={
                'unique_together': {('member_id', 'company_name')},
            },
        ),
    ]
