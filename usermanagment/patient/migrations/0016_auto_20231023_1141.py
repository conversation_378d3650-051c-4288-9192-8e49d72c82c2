# Generated by Django 3.2.12 on 2023-10-23 08:41

from django.db import migrations, models
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0015_remove_patient_preferred_language'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='patient',
            name='blood_group',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='contact_number',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='patient',
            name='date_of_birth',
            field=models.DateField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='e_health_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='patient',
            name='email',
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, db_index=True, max_length=254, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='emergency_phone_number',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='first_name',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='gender',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('D005', 'Resident Card'), ('D004', 'Passport'), ('D003', 'National Card'), ('D002', 'GCC ID'), ('D001', 'Boarder Number'), ('OTHER', 'Other')], db_index=True, default='OTHER', max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='last_name',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='marital_status',
            field=models.CharField(blank=True, choices=[('Single', 'Single'), ('Married', 'Married'), ('Divorced', 'Divorced'), ('Widowed', 'Widowed')], db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='member_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='national_id_number',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='nationality',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='residency_type',
            field=models.CharField(blank=True, choices=[('Residence', 'Residence'), ('Visitor', 'Visitor')], db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='second_name',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='third_name',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='unverifiednationals',
            name='status',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], db_index=True, default='Pending', max_length=20),
        ),
    ]
