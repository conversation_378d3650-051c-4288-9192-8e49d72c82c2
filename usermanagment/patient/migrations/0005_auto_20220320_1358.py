# Generated by Django 3.0.6 on 2022-03-20 13:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0004_patientdisability'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='patient',
            name='blood_group',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='contact_number',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='patient',
            name='dob_hijri',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='e_health_id',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='patient',
            name='third_name',
            field=models.<PERSON>r<PERSON>ield(blank=True, max_length=255, null=True),
        ),
    ]
