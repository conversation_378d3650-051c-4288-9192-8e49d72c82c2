# Generated by Django 3.2.25 on 2024-08-19 07:12

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ('patient', '0022_auto_20240818_1125'),
    ]

    operations = [
        migrations.AlterField(
            model_name='patient',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[
                ('Resident Card', 'Resident Card'), ('Passport', 'Passport'),
                ('National Card', 'National Card')], db_index=True, max_length=15,
                                                         null=True),
        ),
        migrations.AlterField(
            model_name='unverifiednationals',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[
                ('Resident Card', 'Resident Card'), ('Passport', 'Passport'),
                ('National Card', 'National Card')], db_index=True, max_length=15,
                                                         null=True),
        ),
    ]
