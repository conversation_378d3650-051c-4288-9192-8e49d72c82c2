# Generated by Django 3.0.6 on 2022-03-30 16:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0005_auto_20220320_1358'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='patient',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='first_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='gender',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='id_type',
            field=models.CharField(blank=True, choices=[('D005', 'Resident Card'), ('D004', 'Passport'), ('D003', 'National Card'), ('D002', 'GCC ID'), ('D001', 'Boarder Number')], max_length=10, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='patient',
            name='last_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='member_id',
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='nationality',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='preferred_language',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='second_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
