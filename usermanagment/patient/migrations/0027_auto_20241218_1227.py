# Generated by Django 3.2.25 on 2024-12-18 10:27

from django.db import migrations, models
import django.db.models.deletion
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0104_alter_systempermission_options'),
        ('patient', '0026_alter_patient_document_expiry_date'),
    ]

    operations = [
        migrations.AddField(
            model_name='patient',
            name='preferred_communication_language',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='patients', to='account.language'),
        ),
        migrations.AddField(
            model_name='patient',
            name='preferred_communication_method',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Email', 'Email'), ('SMS', 'Sms'), ('Phone', 'Phone')], db_index=True, max_length=50, null=True),
        ),
    ]
