# Generated by Django 3.2.23 on 2024-03-24 08:46

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0018_alter_patient_contact_number'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='patient',
            name='chronic_diseases',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='date_of_birth',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='disabilities',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='e_health_id',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='email',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='emergency_phone_number',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='first_name',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='gender',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='last_name',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='member_id',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='national_id_number',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='residency_type',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='second_name',
        ),
        migrations.RemoveField(
            model_name='patient',
            name='third_name',
        ),
    ]
