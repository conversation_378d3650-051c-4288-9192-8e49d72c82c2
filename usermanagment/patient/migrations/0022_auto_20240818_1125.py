# Generated by Django 3.2.23 on 2024-08-18 08:25

from django.db import migrations, models
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0021_patient_validation_skipped'),
    ]

    operations = [
        migrations.AddField(
            model_name='unverifiednationals',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('D005', 'Resident Card'), ('D004', 'Passport'), ('D003', 'National Card'), ('D002', 'GCC ID'), ('D001', 'Boarder Number'), ('OTHER', 'Other')], db_index=True, default='OTHER', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='unverifiednationals',
            name='nationality',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
    ]
