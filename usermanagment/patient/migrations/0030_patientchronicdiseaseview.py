# Generated by Django 3.2.25 on 2025-06-02 15:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0029_patient_health_parameter_last_sync_time'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientChronicDiseaseView',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField()),
                ('chronic_disease_code', models.CharField(max_length=255)),
                ('date_of_birth', models.DateField()),
                ('gender', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'patient_chronic_disease_view',
                'managed': False,
            },
        ),
    ]
