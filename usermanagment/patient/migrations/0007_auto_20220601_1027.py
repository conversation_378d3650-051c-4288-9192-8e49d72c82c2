# Generated by Django 3.2.12 on 2022-06-01 10:27

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0006_auto_20220330_1647'),
    ]

    operations = [
        migrations.AlterField(
            model_name='patient',
            name='id_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('D005', 'Resident Card'), ('D004', 'Passport'), ('D003', 'National Card'), ('D002', 'GCC ID'), ('D001', 'Boarder Number')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='allergy_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Drug', 'Drug'), ('Food', 'Food'), ('Insect', 'Insect'), ('Latex', 'Latex'), ('Mold', 'Mold'), ('Pet', 'Pet'), ('<PERSON>en', 'Pollen')], max_length=10),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='drink_alcohol',
            field=usermanagment.core.db.fields.EnumField(choices=[('Current everyday', 'Current Everyday'), ('Current someday', 'Current Someday'), ('Former', 'Former'), ('Never', 'Never'), ('Unkown', 'Unknown')], max_length=20),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='drug_use',
            field=usermanagment.core.db.fields.EnumField(choices=[('Current everyday', 'Current Everyday'), ('Current someday', 'Current Someday'), ('Former', 'Former'), ('Never', 'Never'), ('Unkown', 'Unknown')], max_length=20),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='packs_no',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('less half pack', 'Less Half Pack'), ('half pack', 'Half Pack'), ('one pack', 'One Pack'), ('one and half pack', 'One And Half Pack'), ('two packs', 'Two Packs'), ('three packs', 'Three Packs')], max_length=20),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='pregnancy_outcome',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('full term', 'Full Term'), ('Preterm', 'Preterm'), ('Miscarriages', 'Miscarriages'), ('Abortions', 'Abortions')], max_length=20),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='reaction_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Anaphylaxis', 'Anaphylaxis'), ('Rash', 'Rash'), ('Itching', 'Itching')], max_length=15),
        ),
        migrations.AlterField(
            model_name='patientmedicalhistory',
            name='smoking_freq',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Current everyday', 'Current Everyday'), ('Current someday', 'Current Someday'), ('Former', 'Former'), ('Never', 'Never'), ('Unkown', 'Unknown')], max_length=20),
        ),
    ]
