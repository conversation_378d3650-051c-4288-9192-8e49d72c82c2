# Generated by Django 3.0.6 on 2022-03-06 14:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('patient', '0003_auto_20220214_1336'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatientDisability',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=255)),
                ('medical_history', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disabilities', to='patient.PatientMedicalHistory')),
            ],
            options={
                'unique_together': {('code', 'medical_history')},
            },
        ),
    ]
