from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _


class PatientIdTypes(TextChoices):
    RESIDENT_CARD = 'Resident Card'
    PASSPORT = 'Passport'
    NATIONAL_CARD = 'National Card'
    BORDER_NUMBER = 'Border Number'

class PreferredCommunicationMethods(TextChoices):
    EMAIL = 'Email'
    SMS = 'SMS'
    PHONE = 'Phone'

class AllergyTypes(TextChoices):
    DRUG = 'Drug'
    FOOD = 'Food'
    INSECT = 'Insect'
    LATEX = 'Latex'
    MOLD = 'Mold'
    PET = 'Pet'
    POLLEN = 'Pollen'


class ReactionTypes(TextChoices):
    ANAPHYLAXIS = 'Anaphylaxis'
    RASH = 'Rash'
    ITCHING = 'Itching'


class HabitFrequency(TextChoices):
    CURRENT_EVERYDAY = 'Current everyday'
    CURRENT_SOMEDAY = 'Current someday'
    FORMER = 'Former'
    NEVER = 'Never'
    UNKNOWN = 'Unkown'


class PackCount(TextChoices):
    LESS_HALF_PACK = 'less half pack'
    HALF_PACK = 'half pack'
    ONE_PACK = 'one pack'
    ONE_AND_HALF_PACK = 'one and half pack'
    TWO_PACKS = 'two packs'
    THREE_PACKS = 'three packs'


class PregnancyOutcome(TextChoices):
    FULL_TERM = 'full term'
    PRETERM = 'Preterm'
    MISCARRIAGES = 'Miscarriages'
    ABORTIONS = 'Abortions'


class ResidencyTypes(TextChoices):
    RESIDENCE = 'Residence'
    VISITOR = 'Visitor'


class MaritalStatuses(TextChoices):
    SINGLE = 'Single'
    MARRIED = 'Married'
    DIVORCED = 'Divorced'
    WIDOWED = 'Widowed'


class UnverifiedNationalsStatuses(TextChoices):
    PENDING = 'Pending'
    APPROVED = 'Approved'
    REJECTED = 'Rejected'
