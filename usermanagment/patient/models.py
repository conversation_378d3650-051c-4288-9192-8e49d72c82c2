from django.db import models
from versatileimagefield.fields import VersatileImageField

from .enums import (
    PregnancyOutcome,
    PatientIdTypes,
    AllergyTypes,
    ReactionTypes,
    HabitFrequency,
    PackCount,
    ResidencyTypes,
    MaritalStatuses, UnverifiedNationalsStatuses, PreferredCommunicationMethods,
)
from .. import settings
from ..account.models import User
from ..core.db.fields import EnumField
from ..core.models import AuditModel

from usermanagment.account.models.possible_phone_number import PossiblePhoneNumberField


class Patient(AuditModel):
    id_type = EnumField(
        max_length=15,
        choices=PatientIdTypes.choices,
        null=True, blank=True,
        db_index=True
    )
    preferred_communication_method = EnumField(
        max_length=50,
        choices=PreferredCommunicationMethods.choices,
        null=True, blank=True,
        db_index=True
    )
    preferred_communication_language = models.ForeignKey(
        "account.Language", null=True, blank=True, on_delete=models.SET_NULL,
        related_name="patients", db_index=True
    )
    nationality = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    document_expiry_date = models.DateField(null=True, blank=True)
    contact_number = PossiblePhoneNumberField(null=True, blank=True, db_index=True)
    blood_group = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    marital_status = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        choices=MaritalStatuses.choices,
        db_index=True
    )
    validation_skipped = models.BooleanField(default=False)

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        related_name="patient",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    health_parameter_last_sync_time = models.DateTimeField(null=True, blank=True)


class UnverifiedNationals(models.Model):
    nationalId = models.CharField(max_length=255, null=False, blank=False)
    id_type = EnumField(
        max_length=15,
        choices=PatientIdTypes.choices,
        null=True, blank=True,
        db_index=True
    )
    nationality = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    document_expiry_date = models.DateField(null=False, blank=False)
    front_image_file_name = models.CharField(max_length=255, null=False, blank=False)
    rear_image_file_name = models.CharField(max_length=255, null=False, blank=False)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
    status = EnumField(max_length=20, choices=UnverifiedNationalsStatuses.choices,
                       blank=True, default=UnverifiedNationalsStatuses.PENDING,
                       db_index=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    rejection_reason = models.CharField(max_length=255, null=True, blank=True)


class PatientChronicDiseaseView(models.Model):
    user_id = models.BigIntegerField()
    chronic_disease_code = models.CharField(max_length=255)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'patient_chronic_disease_view'
