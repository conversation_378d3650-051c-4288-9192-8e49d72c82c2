import typing
from dataclasses import dataclass

from dataclasses_json import dataclass_json, LetterCase

from usermanagment.kafka.data_class.notification_channels import NotificationChannelEnum


@dataclass_json(letter_case=LetterCase.CAMEL)
@dataclass
class SmsNotification:
    recipient_mobile_number: str
    body: str


@dataclass_json(letter_case=LetterCase.CAMEL)
@dataclass
class EmailNotification:
    recipient_email: str
    title: str
    body: str


@dataclass_json(letter_case=LetterCase.CAMEL)
@dataclass
class Notification:
    recipient_user_id: int
    channels: typing.Dict[NotificationChannelEnum, object]
