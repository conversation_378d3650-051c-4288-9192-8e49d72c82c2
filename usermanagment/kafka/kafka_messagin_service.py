import logging
import json

from kafka import KafkaProducer
from usermanagment import settings
from .data_class.notification import Notification, SmsNotification, EmailNotification
from .data_class.notification_channels import NotificationChannelEnum
from .topics import KafkaTopics

logger = logging.getLogger(__name__)


class KafkaMessageService:
    initialized = False
    def __new__(cls):
        if not hasattr(cls, 'instance'):
            cls.instance = super(KafkaMessageService, cls).__new__(cls)
        return cls.instance
    def __init__(self):
        if not self.initialized:
            try:
                self.producer = KafkaProducer(
                    bootstrap_servers=settings.KAFKA.get("BOOTSTRAP_SERVERS"),
                )
                self.initialized = True
            except Exception as e:
                logger.error("error while initializing kafka producer", exc_info=e)

    def send_notification_message(self, message: Notification):
        try:
            self.producer.send(KafkaTopics.INTERNAL_NOTIFICATIONS,
                               message.to_json().encode('utf-8'))

        except Exception as exception:
            logger.error("error while sending message to kafka server",
                         exc_info=exception)

    def send_sms_notification_message(self,
                                      recipient_user_id: int,
                                      message: SmsNotification):
        self.send_notification_message(Notification(
            recipient_user_id=recipient_user_id,
            channels={
                NotificationChannelEnum.SMS: message
            }
        ))

    def send_email_notification_message(self,
                                        recipient_user_id: int,
                                        message: EmailNotification):
        self.send_notification_message(Notification(
            recipient_user_id=recipient_user_id,
            channels={
                NotificationChannelEnum.EMAIL: message
            }
        ))

    def send_message(self, topic, message):
        try:
            self.producer.send(topic,
                               json.dumps(message).encode('utf-8'))

        except Exception as exception:
            logger.error("error while sending message to kafka server",
                         exc_info=exception)
