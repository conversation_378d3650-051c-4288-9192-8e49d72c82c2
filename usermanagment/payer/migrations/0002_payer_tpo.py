# Generated by Django 3.2.12 on 2023-08-23 07:37

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('payer', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='payer',
            name='tpo',
            field=usermanagment.core.db.fields.EnumField(choices=[('IO', 'IO'), ('EClaim', 'EClaim')], default='IO', max_length=20),
            preserve_default=False,
        ),
    ]
