# Generated by Django 3.2.12 on 2023-03-16 08:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import usermanagment.account.models.possible_phone_number


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Payer',
            fields=[
                ('id',
                 models.AutoField(auto_created=True, primary_key=True, serialize=False,
                                  verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('name_ar',
                 models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone_number',
                 usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(
                     blank=True, max_length=128, null=True, region=None)),
                ('contact_mobile_number',
                 usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(
                     blank=True, max_length=128, null=True, region=None)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('license_number', models.CharField(max_length=255, unique=True)),
                ('logo', models.CharField(blank=True, max_length=256, null=True)),
                ('background_image',
                 models.CharField(blank=True, max_length=256, null=True)),
                ('created_by', models.ForeignKey(editable=False, null=True,
                                                 on_delete=django.db.models.deletion.SET_NULL,
                                                 related_name='+',
                                                 to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(editable=False, null=True,
                                                 on_delete=django.db.models.deletion.SET_NULL,
                                                 related_name='+',
                                                 to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True,
                                                  on_delete=django.db.models.deletion.SET_NULL,
                                                  related_name='+',
                                                  to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
