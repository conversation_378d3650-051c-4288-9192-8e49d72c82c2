# Generated by Django 3.2.23 on 2024-08-14 07:46

from django.db import migrations, models
import django.db.models.deletion
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('payer', '0003_auto_20231023_1141'),
    ]

    operations = [
        migrations.AddField(
            model_name='payer',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='payer.payer'),
        ),
        migrations.AddField(
            model_name='payer',
            name='type',
            field=usermanagment.core.db.fields.EnumField(choices=[('Insurance', 'Insurance'), ('TPA', 'Tpa')], db_index=True, default='Insurance', max_length=10),
        ),
    ]
