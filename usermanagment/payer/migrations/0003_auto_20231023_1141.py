# Generated by Django 3.2.12 on 2023-10-23 08:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payer', '0002_payer_tpo'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='payer',
            name='deleted',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='payer',
            name='is_active',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='payer',
            name='license_number',
            field=models.Char<PERSON>ield(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='payer',
            name='name',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='payer',
            name='name_ar',
            field=models.Char<PERSON>ield(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
    ]
