from django.db import models

from usermanagment.account.models import PossiblePhoneNumber<PERSON>ield
from usermanagment.core.db.fields import EnumField
from usermanagment.core.models import AuditModel, SoftDeletionModel, \
    SoftDeletionManager, SoftDeletionQuerySet
from usermanagment.payer.enums import TPO, PayerType


class PayerQueryManager(SoftDeletionManager):

    def get_queryset(self):
        if self.deleted is not None:
            return SoftDeletionQuerySet(self.model).filter(deleted=self.deleted)
        return SoftDeletionQuerySet(self.model)


class Payer(AuditModel, SoftDeletionModel):
    name = models.CharField(max_length=255, unique=True, null=False, db_index=True)
    name_ar = models.CharField(max_length=255, unique=True, null=True, blank=True,
                               db_index=True)
    contact_name = models.CharField(max_length=255, null=True)
    contact_phone_number = PossiblePhoneNumberField(null=True, blank=True)
    contact_mobile_number = PossiblePhoneNumberField(null=True, blank=True)
    contact_email = models.CharField(max_length=255, null=True)
    is_active = models.BooleanField(default=False, null=False, db_index=True)
    license_number = models.CharField(max_length=255, unique=True, null=False,
                                      db_index=True)
    logo = models.CharField(max_length=256, null=True, blank=True)
    background_image = models.CharField(max_length=256, null=True, blank=True)
    tpo = EnumField(max_length=20, choices=TPO.choices(), null=False,
                    blank=False)
    type =  EnumField(
        max_length=10,
        choices=PayerType.choices,
        null=False, blank=False,
        db_index=True
    )
    insurance_card_logo = models.CharField(max_length=256, null=True, blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    objects = PayerQueryManager()
