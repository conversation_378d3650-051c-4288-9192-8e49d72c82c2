from enum import Enum

from django.db.models import TextChoices


class TPO(str, Enum):
    IO = "IO"
    ECLAIM = "EClaim"

    @staticmethod
    def choices():
        return [(member.value, member.value) for member in TPO]

    def __str__(self):
        return str(self.value)


class PayerType(TextChoices):
    INSURANCE = "Insurance"
    TPA = "TPA"

    @staticmethod
    def choices():
        return [(member.value, member.value) for member in PayerType]

    def __str__(self):
        return str(self.value)
