# Generated by Django 3.2.12 on 2023-10-23 08:41

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('subscription', '0006_alter_subscription_unique_together'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='plan',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name='plan',
            name='name',
            field=models.CharField(db_index=True, max_length=36, unique=True),
        ),
        migrations.AlterField(
            model_name='plan',
            name='valid_from',
            field=models.DateTimeField(db_index=True, default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='plan',
            name='valid_till',
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='subscription',
            name='valid_from',
            field=models.DateTimeField(db_index=True, default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='valid_till',
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
    ]
