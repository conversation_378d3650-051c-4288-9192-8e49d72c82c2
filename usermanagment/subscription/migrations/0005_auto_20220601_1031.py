# Generated by Django 3.2.12 on 2022-06-01 10:31

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('vendor', '0014_auto_20220601_1027'),
        ('subscription', '0004_alter_plan_period'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='subscription',
            name='vendor_subscription_unique_if_active',
        ),
        migrations.AlterUniqueTogether(
            name='subscription',
            unique_together={('vendor', 'is_active')},
        ),
    ]
