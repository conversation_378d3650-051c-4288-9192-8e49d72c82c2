# Generated by Django 3.0.6 on 2021-12-28 12:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Plan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('fixed_cost_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('is_active', models.BooleanField(default=True)),
                ('fixed_order_cost_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('fixed_order_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('name', models.CharField(max_length=36, unique=True)),
                ('valid_from', models.DateTimeField(default=django.utils.timezone.now)),
                ('valid_till', models.DateTimeField(blank=True, null=True)),
                ('description', models.CharField(max_length=300, null=True)),
                ('period', models.CharField(choices=[('Monthly', 'Monthly'), ('Half Year', 'Half Year'), ('Yearly', 'Yearly'), ('Weekly', 'Weekly'), ('Three Months', 'Three Months')], default='Monthly', max_length=32)),
            ],
            options={
                'permissions': (('manage_subscriptions', 'Manage Plans.'),),
            },
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('fixed_cost_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('is_active', models.BooleanField(default=True)),
                ('fixed_order_cost_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('fixed_order_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('valid_from', models.DateTimeField(default=django.utils.timezone.now)),
                ('valid_till', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('plan', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='subscription.Plan')),
            ],
            options={
                'ordering': ('-pk',),
                'permissions': (('manage_subscriptions', 'Manage Subscriptions.'),),
            },
        ),
    ]
