from django.conf import settings
from django.db import models
from django.utils.timezone import now
from django_prices.models import MoneyField

from .enums import Period
from ..core.db.fields import EnumField
from ..core.models import (AuditModel, )


class SubscriptionCost(AuditModel):
    currency = models.CharField(
        max_length=settings.DEFAULT_CURRENCY_CODE_LENGTH,
        default=settings.DEFAULT_CURRENCY,
    )
    fixed_cost_amount = models.DecimalField(
        max_digits=settings.DEFAULT_MAX_DIGITS,
        decimal_places=settings.DEFAULT_DECIMAL_PLACES,
        default=0)
    fixed_cost = MoneyField(amount_field="fixed_cost_amount",
                            currency_field="currency", blank=False, null=True)
    is_active = models.BooleanField(default=True, db_index=True)
    fixed_order_cost_amount = models.DecimalField(
        max_digits=settings.DEFAULT_MAX_DIGITS,
        decimal_places=settings.DEFAULT_DECIMAL_PLACES,
        default=0,
        blank=False)
    fixed_order_cost = MoneyField(
        amount_field="fixed_order_cost_amount", currency_field="currency")
    fixed_order_percentage = models.DecimalField(
        max_digits=settings.DEFAULT_MAX_DIGITS,
        decimal_places=settings.DEFAULT_DECIMAL_PLACES,
        default=0,
        blank=False)

    class Meta:
        abstract = True


class Plan(SubscriptionCost):
    name = models.CharField(max_length=36, unique=True, db_index=True)
    # The date the plan is introduced.
    valid_from = models.DateTimeField(default=now, db_index=True)
    # The validity of the plan. If we want to terminate the plan,
    # just set the valid till date.
    valid_till = models.DateTimeField(null=True, blank=True, db_index=True)

    description = models.CharField(max_length=300, null=True)

    period = EnumField(
        max_length=32,
        blank=False,
        default=Period.MONTHLY,
        choices=Period.CHOICES
    )


class Subscription(SubscriptionCost):
    vendor = models.ForeignKey("vendor.Vendor", blank=False, null=True,
                               related_name="subscriptions", on_delete=models.SET_NULL)
    valid_from = models.DateTimeField(default=now, db_index=True)
    valid_till = models.DateTimeField(null=True, blank=True, db_index=True)
    plan = models.ForeignKey(
        Plan, blank=False, null=True,
        related_name="subscriptions", on_delete=models.CASCADE
    )

    class Meta:
        ordering = ("-pk",)
