from decimal import Decimal

from django.contrib.gis import measure
from django.contrib.gis.db import models
from django.contrib.gis.db.models.functions import Intersection, Area
from django.contrib.gis.geos import Polygon
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MinLengthValidator
from django.db.models import F

from ..core.models import AuditModel, BaseTranslationModel
from ..core.utils.json_serializer import CustomJsonEncoder


class City(AuditModel):
    code = models.CharField(max_length=255, unique=True, db_index=True)
    name = models.CharField(max_length=255, unique=True, db_index=True,
                            validators=[MinLengthValidator(3)]
                            )

    name_ar = models.CharField(max_length=255, unique=True, null=True, blank=False,
                               db_index=True,validators=[MinLengthValidator(3)]
                               )

    max_number_of_rounds = models.PositiveIntegerField(null=True, blank=True)

    max_number_of_tries = models.PositiveIntegerField(null=True, blank=True)

    time_out_period = models.PositiveIntegerField(null=True, blank=True)

    area = models.CharField(max_length=255, null=True, blank=True)


class CityRound(models.Model):
    city = models.ForeignKey(
        City,
        on_delete=models.PROTECT,
        related_name="rounds",
        null=False, blank=False,
    )

    radius = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=False,
        blank=False,
        validators=[MinValueValidator(Decimal('0.01'))],
    )

    max_number_of_pharmacies = models.PositiveIntegerField(null=False, blank=False)

    pharmacies_types = models.JSONField(null=True, blank=True, )


class Block(AuditModel):
    code = models.CharField(max_length=255, unique=True, db_index=True)
    name = models.CharField(max_length=255, unique=False, db_index=True)

    name_ar = models.CharField(max_length=255, unique=False, null=True, blank=True,
                               db_index=True)

    city = models.ForeignKey(
        City,
        related_name="blocks",
        on_delete=models.PROTECT,
        null=False, blank=False
    )

    coordinates = models.JSONField(default=dict, encoder=CustomJsonEncoder,
                                   null=True, blank=True)

