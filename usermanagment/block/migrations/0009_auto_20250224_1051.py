# Generated by Django 3.2.25 on 2025-02-24 08:51

from django.db import migrations, models

def set_default_code(apps, schema_editor):
    City = apps.get_model('block', 'City')
    for city in City.objects.all():
        if not city.code:
            city.code = city.name
            city.save(update_fields=['code'])
    Block = apps.get_model('block', 'Block')
    for block in Block.objects.all():
        if not block.code:
            block.code = block.name
            block.save(update_fields=['code'])

class Migration(migrations.Migration):

    dependencies = [
        ('block', '0008_auto_20241202_1643'),
    ]

    operations = [
        migrations.AddField(
            model_name='block',
            name='code',
            field=models.CharField(db_index=True, max_length=255, unique=False),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='city',
            name='code',
            field=models.CharField(db_index=True, max_length=255, unique=False),
            preserve_default=False,
        ),
        migrations.RunPython(set_default_code),
        migrations.AlterField(
            model_name='block',
            name='code',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='city',
            name='code',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
    ]
