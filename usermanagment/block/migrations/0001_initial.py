# Generated by Django 3.0.6 on 2022-03-03 14:52

import django.contrib.gis.db.models.fields
import django.db.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import usermanagment.core.utils.json_serializer


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('max_number_of_rounds', models.IntegerField(blank=True, null=True)),
                ('max_number_of_tries', models.IntegerField(blank=True, null=True)),
                ('time_out_period', models.IntegerField(blank=True, null=True)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CityRound',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('radius', models.DecimalField(decimal_places=2, max_digits=6)),
                ('max_number_of_pharmacies', models.IntegerField()),
                ('pharmacies_types', models.JSONField(blank=True, null=True)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rounds', to='block.City')),
            ],
        ),
        migrations.CreateModel(
            name='Block',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('coordinates', models.JSONField(blank=True, default=dict, encoder=usermanagment.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('geo_coordinates', django.contrib.gis.db.models.fields.PolygonField(blank=True, null=True, srid=4326)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocks', to='block.City')),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CityTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=10)),
                ('name', models.CharField(max_length=255)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='block.City')),
            ],
            options={
                'unique_together': {('language_code', 'city')},
            },
        ),
        migrations.CreateModel(
            name='BlockTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=10)),
                ('name', models.CharField(max_length=255)),
                ('block', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='block.Block')),
            ],
            options={
                'unique_together': {('language_code', 'block')},
            },
        ),
    ]
