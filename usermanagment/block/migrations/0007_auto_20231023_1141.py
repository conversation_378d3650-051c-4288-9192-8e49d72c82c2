# Generated by Django 3.2.12 on 2023-10-23 08:41

import django.contrib.gis.db.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('block', '0006_auto_20230205_1142'),
    ]

    operations = [
        migrations.AlterField(
            model_name='block',
            name='name',
            field=models.CharField(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='block',
            name='name_ar',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='city',
            name='name',
            field=models.Char<PERSON>ield(db_index=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name='city',
            name='name_ar',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True, unique=True),
        ),
    ]
