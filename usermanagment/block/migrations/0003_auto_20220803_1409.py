# Generated by Django 3.2.12 on 2022-08-03 14:09

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('block', '0002_alter_block_coordinates'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='city',
            name='max_number_of_rounds',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='city',
            name='max_number_of_tries',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='city',
            name='time_out_period',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cityround',
            name='max_number_of_pharmacies',
            field=models.PositiveIntegerField(),
        ),
        migrations.AlterField(
            model_name='cityround',
            name='radius',
            field=models.DecimalField(decimal_places=2, max_digits=6, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))]),
        ),
    ]
