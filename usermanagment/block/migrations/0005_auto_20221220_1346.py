# Generated by Django 3.2.12 on 2022-12-20 13:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('block', '0004_city_area'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='citytranslation',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='citytranslation',
            name='city',
        ),
        migrations.AddField(
            model_name='block',
            name='name_ar',
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='city',
            name='name_ar',
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.DeleteModel(
            name='BlockTranslation',
        ),
        migrations.DeleteModel(
            name='CityTranslation',
        ),
    ]
