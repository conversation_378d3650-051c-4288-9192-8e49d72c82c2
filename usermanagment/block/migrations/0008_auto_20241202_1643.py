# Generated by Django 3.2.23 on 2024-12-02 14:43

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('block', '0007_auto_20231023_1141'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='city',
            name='name',
            field=models.CharField(db_index=True, max_length=255, unique=True, validators=[django.core.validators.MinLengthValidator(3)]),
        ),
        migrations.AlterField(
            model_name='city',
            name='name_ar',
            field=models.CharField(db_index=True, max_length=255, null=True, unique=True, validators=[django.core.validators.MinLengthValidator(3)]),
        ),
    ]
