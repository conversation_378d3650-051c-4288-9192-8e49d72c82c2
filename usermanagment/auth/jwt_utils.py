import logging
from urllib.parse import urljoin

import jwt
import pem
import requests
from django.conf import settings
from jwt import PyJWKClient

from usermanagment.settings import key

logger = logging.getLogger(__name__)

KEYCLOAK_CERTS_URL = "/auth/realms/{realm-name}/protocol/openid-connect/certs"
KEYCLOAK_ISSUER_URL = "/auth/realms/{realm-name}"


class RequestsPyJWKClient(PyJWKClient):
    instance = None
    initialized = False

    def __new__(cls, url):
        if cls.instance is None:
            cls.instance = super().__new__(cls)

        return cls.instance

    def __init__(self, url):
        if not self.initialized:
            super().__init__(url)
            self.initialized = True

    def fetch_data(self):
        return requests.get(self.uri).json()


def jwt_decode(token):
    jwks_url = urljoin(
        settings.KEYCLOAK_CONFIG['SERVER_URL'],
        KEYCLOAK_CERTS_URL.format(**{
            'realm-name': settings.KEYCLOAK_CONFIG['REALM']
        })
    )

    jwks_client = RequestsPyJWKClient(jwks_url)
    signing_key = jwks_client.get_signing_key_from_jwt(token)

    headers = jwt.get_unverified_header(token)
    if not headers.get('alg') or headers.get('alg').lower() == 'none':
        raise jwt.InvalidTokenError()

    issuer_url = urljoin(
        settings.KEYCLOAK_CONFIG['SERVER_URL'],
        KEYCLOAK_ISSUER_URL.format(**{
            'realm-name': settings.KEYCLOAK_CONFIG['REALM']
        })
    )

    return jwt.decode(
        token,
        signing_key.key,
        algorithms=[headers['alg']],
        options={
            'verify_exp': True,
            'verify_aud': False,
        },
        issuer=issuer_url,

    )


def verify_jwt_token(token):
    try:
        return jwt_decode(token)
    except jwt.PyJWTError as e:
        logger.error("PyJWTError while decoding user token.", exc_info=e)
        raise Exception("Invalid or expired token.")
    except Exception as e:
        logger.error("Error while decoding user token.", exc_info=e)
        raise Exception(
            "Sorry we have some issues and we are working to solve them, "
            "please try again later")


def get_token_payload(token):
    return jwt.decode(token, options={"verify_signature": False})


def encode_jwt_token(user_info, headers):
    return jwt.encode(user_info, key[0].as_text(), headers=headers)
