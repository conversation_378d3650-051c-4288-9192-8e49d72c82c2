from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from graphql import ResolveInfo

from .anonymous_user import AnonymousUser
from ..graphql.utils.request_utils import set_current_user, set_current_context, \
    set_current_gql_operation


class AppendUserInfoToRequestMixin(object):
    def append_user_info_to_request(self, request, token):
        User = get_user_model()
        user = User.objects.create_from_token(token)
        set_current_user(user)

        if isinstance(request, dict):
            request['user'] = user
        else:
            request.user = user

        return request

    def is_auth_header_missing(self, request):
        """Check if exists an authentication header in the HTTP request"""
        return 'HTTP_AUTHORIZATION' not in request.META

    def get_token(self, request):
        """Get the token from the HTTP request"""
        auth_header = request.META.get('HTTP_AUTHORIZATION').split()
        if len(auth_header) == 2:
            return auth_header[1]
        return None


class GraphqlJWTMiddleware(AppendUserInfoToRequestMixin):
    def resolve(self, next, root, info, **kwargs):
        request = info.context

        if self.is_auth_header_missing(request) or not self.get_token(request):
            """Append anonymous user and continue"""
            request.user = AnonymousUser()
            return next(root, info, **kwargs)

        if not hasattr(request, 'user'):
            token = self.get_token(request)
            if token is None:
                raise Exception("Invalid token structure. Must be 'Bearer <token>'")

            info.context = self.append_user_info_to_request(request, token)

        return next(root, info, **kwargs)


class JWTAuthMiddleware(AppendUserInfoToRequestMixin, MiddlewareMixin):
    def __call__(self, request):
        """
        To be executed before the view each request
        """
        if self.is_auth_header_missing(request) or not self.get_token(request):
            """Append anonymous user and continue"""
            request.user = AnonymousUser()
            return self.get_response(request)

        if not hasattr(request, 'user'):
            token = self.get_token(request)
            if token is None:
                return JsonResponse(
                    {"detail": "Invalid token structure. Must be 'Bearer <token>'"},
                    status=401,
                )

            try:
                request = self.append_user_info_to_request(request, token)
            except Exception as e:
                return JsonResponse(
                    {"errors": [
                        {
                            "message": str(e)
                        }
                    ]},
                    status=401,
                )

        return self.get_response(request)


class InitializeRequestDataMiddleware(MiddlewareMixin):
    def __call__(self, request):
        set_current_user(getattr(request, 'user', AnonymousUser()))
        set_current_context(request)
        set_current_gql_operation({})

        try:
            return self.get_response(request)
        finally:
            set_current_user(None)
            set_current_context(None)
            set_current_gql_operation(None)


class InitializeGraphqlRequestDataMiddleware:
    @staticmethod
    def resolve(next_, root, info: ResolveInfo, **kwargs):
        request = info.context
        if hasattr(request, 'user'):
            return next_(root, info, **kwargs)

        set_current_user(getattr(request, 'user', AnonymousUser()))
        set_current_context(request)

        return next_(root, info, **kwargs)
