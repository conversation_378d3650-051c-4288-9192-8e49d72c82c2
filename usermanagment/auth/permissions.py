from enum import Enum

from usermanagment.auth.enums import AppTypes


class BasePermissionEnum(Enum):

    @property
    def codename(self):
        return self.value.split(".")[1]

    def __new__(cls, *args, **kwds):
        obj = object.__new__(cls)
        obj._value_ = args[0]
        return obj

    # ignore the first param since it's already set by __new__
    def __init__(self, _: str, description: str = None, target_users=None):
        self._description_ = description
        if target_users is None:
            target_users = [AppTypes.ADMIN]
        self._target_users = target_users

    def __str__(self):
        return self.value

    # this makes sure that the description is read-only
    @property
    def description(self):
        return self._description_

    @property
    def target_users(self):
        return self._target_users


class AccountPermissions(BasePermissionEnum):
    MANAGE_USERS = "permissions.manage_users", "Can Manage Users", [AppTypes.ADMIN,
                                                                    AppTypes.VENDOR,
                                                                    AppTypes.PAYER]
    MANAGE_STAFF = "permissions.manage_staff", "Can Manage Staff", [AppTypes.ADMIN]
    MANAGE_PERMISSION_GROUP = "permissions.manage_permission_group", "Can Manage Permission Groups", [
        AppTypes.ADMIN]
    VIEW_CUSTOMERS = "permissions.view_customers", "Can View Customers", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_USERS = "permissions.view_users", "Can View Users", [AppTypes.ADMIN,
                                                              AppTypes.VENDOR,
                                                              AppTypes.PAYER]
    VIEW_STAFF = "permissions.view_staff", "Can View Staff", [AppTypes.ADMIN,
                                                              AppTypes.VENDOR,
                                                              AppTypes.PAYER]

    MANAGE_LANGUAGES = "permissions.manage_languages", "Can Manage Languages", [
        AppTypes.ADMIN,
        AppTypes.VENDOR,
        AppTypes.PAYER]

    SEND_BULK_MESSAGES = "permissions.send_bulk_messages", "Can Send Bulk Messages", [
        AppTypes.ADMIN,]

class VendorPermissions(BasePermissionEnum):
    MANAGE_VENDORS = "permissions.manage_vendors", "Can Manage vendors", [
        AppTypes.ADMIN]
    MANAGE_DEPARTMENTS = "permissions.manage_departments", "Can Manage departments", [
        AppTypes.ADMIN,
        AppTypes.VENDOR
    ]

class ArticlePermissions(BasePermissionEnum):
    MANAGE_ARTICLES = "permissions.manage_articles", "Can Manage articles", [
        AppTypes.ADMIN,
    ]
    VIEW_ARTICLES = "permissions.view_articles", "Can View articles", [
        AppTypes.ADMIN,
    ]
    REVIEW_ARTICLES = "permissions.review_articles", "Can Review articles", [
        AppTypes.ADMIN]

class PayerPermissions(BasePermissionEnum):
    MANAGE_PAYERS = "permissions.manage_payers", "Can Manage payers", [AppTypes.ADMIN]
    MANAGE_PAYER_SERVICES = "permissions.manage_payer_services", "Can Manage payer services", [
        AppTypes.ADMIN]


class BranchPermissions(BasePermissionEnum):
    MANAGE_BRANCHES = "permissions.manage_branches", "Can Manage branches", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class BlockPermissions(BasePermissionEnum):
    MANAGE_BLOCKS = "permissions.manage_blocks", "Can Manage blocks", [AppTypes.ADMIN]
    MANAGE_CITIES = "permissions.manage_cities", "Can Manage cities", [AppTypes.ADMIN]


class SubscriptionPermissions(BasePermissionEnum):
    MANAGE_SUBSCRIPTIONS = "permissions.manage_subscriptions", "Can Manage subscriptions", [
        AppTypes.ADMIN]
    VIEW_SUBSCRIPTIONS = "permissions.view_subscriptions", "Can View subscriptions", [
        AppTypes.ADMIN]


class WalletPermissions(BasePermissionEnum):
    MANAGE_WALLETS = "permissions.manage_wallets", "Can Manage wallets", [
        AppTypes.ADMIN]
    VIEW_WALLETS = "permissions.view_wallets", "Can View wallets", [
        AppTypes.ADMIN]


class ChatPermissions(BasePermissionEnum):
    MANAGE_CHAT = "permissions.manage_chat", "Can Manage chat", [AppTypes.ADMIN,
                                                                 AppTypes.VENDOR]


class PatientPermissions(BasePermissionEnum):
    MANAGE_PATIENTS = "permissions.manage_patients", "Can Manage patients", [
        AppTypes.ADMIN]
    VIEW_PATIENTS = "permissions.view_patients", "Can View patients", [
        AppTypes.ADMIN]
    VERIFY_NATIONAL_IDS = "permissions.verify_national_ids", "Can Verify National IDs", [
        AppTypes.ADMIN]
    VERIFY_MEMBER_IDS = "permissions.verify_member_ids", "Can Verify Member IDs", [
        AppTypes.ADMIN]


class MedicalEditsPermissions(BasePermissionEnum):
    MANAGE_MEDICAL_EDITS = "permissions.manage_medical_edits", "Can Manage medical edits", [
        AppTypes.ADMIN, AppTypes.VENDOR, AppTypes.PAYER]

    VALIDATE_PROCEDURES = "permissions.validate_procedures", "Can Validate procedures", [
        AppTypes.ADMIN, AppTypes.VENDOR, AppTypes.PAYER]

    MANAGE_AGENT_DEFINITIONS = "permissions.manage_agent_definitions", "Can Manage agent definitions", [
        AppTypes.ADMIN]

class PatientMedicalHistoryPermissions(BasePermissionEnum):
    MANAGE_PATIENT_MEDICAL_HISTORY = "permissions.manage_patient_medical_history", \
        "Can Manage patient medical history", [AppTypes.ADMIN]


class DiscountPermissions(BasePermissionEnum):
    MANAGE_DISCOUNTS = "permissions.manage_discounts", "Can Manage discounts", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class PluginsPermissions(BasePermissionEnum):
    MANAGE_PLUGINS = "permissions.manage_plugins", "Can Manage plugins", [
        AppTypes.ADMIN]


class CheckoutPermissions(BasePermissionEnum):
    MANAGE_CHECKOUTS = "permissions.manage_checkouts", "Can Manage checkouts", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class OrderPermissions(BasePermissionEnum):
    MANAGE_ORDERS = "permissions.manage_orders", "Can Manage orders", [AppTypes.ADMIN,
                                                                       AppTypes.VENDOR]
    MANAGE_HEALTH_PACKAGE_ORDERS = "permissions.manage_health_package_orders", "Can Manage health package orders", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_ORDERS = "permissions.view_orders", "Can View orders", [AppTypes.ADMIN,
                                                                 AppTypes.VENDOR]
    MANAGE_DELIVERY_TIME_SLOTS = ("permissions.manage_delivery_time_slots",
                                  "Can Manage for delivery time slots")
    CUSTOMER_SUPPORT_MANAGE_ORDERS = ("permissions.customer_support_manage_orders",
                                      "Customer Support Manage orders", [
        AppTypes.ADMIN])
    MANAGE_MARKETPLACE_ORDERS = "permissions.manage_marketplace_orders", "Can Manage marketplace orders", [
        AppTypes.VENDOR]



class ProductPermissions(BasePermissionEnum):
    MANAGE_PRODUCTS = "permissions.manage_products", "Can Manage products", [
        AppTypes.VENDOR]
    APPROVE_PRODUCTS = "permissions.approve_products", "Can Approve products", [
        AppTypes.ADMIN]
    MANAGE_PRODUCT_CATEGORIES = "permissions.manage_product_categories", "Can Manage product categories", [
        AppTypes.ADMIN]
    MANAGE_BRANDS = "permissions.manage_brands", "Can Manage brands", [
        AppTypes.ADMIN]


class ShippingPermissions(BasePermissionEnum):
    MANAGE_SHIPPING = "permissions.manage_shipping", "Can Manage shipping", [
        AppTypes.ADMIN]


class SitePermissions(BasePermissionEnum):
    MANAGE_SETTINGS = "permissions.manage_settings", "Can Manage settings", [
        AppTypes.ADMIN]

    MANAGE_TRANSLATIONS = "permissions.manage_translations", "Can Manage translations", [
        AppTypes.ADMIN]
    MANAGE_DASHBOARD = "permissions.manage_dashboard", "Can Manage dashboard", [
        AppTypes.ADMIN,AppTypes.VENDOR]


class PricingRulePermissions(BasePermissionEnum):
    MANAGE_PRICING_RULES = "permissions.manage_pricing_rules", "Can Manage pricing rules", [
        AppTypes.ADMIN]


class OrderRejectionReasonPermissions(BasePermissionEnum):
    MANAGE_REJECTION_REASONS = "permissions.manage_rejection_reasons", "Can Manage order rejection reasons", [
        AppTypes.ADMIN]


class InvoicePermissions(BasePermissionEnum):
    MANAGE_INVOICES = "permissions.manage_invoices", "Can Manage invoices.", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class PrescriptionPermissions(BasePermissionEnum):
    MANAGE_PRESCRIPTIONS = "permissions.manage_prescriptions", "Can Manage prescriptions", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class PrescriptionOrderPermissions(BasePermissionEnum):
    ASSIGN_PRESCRIPTION_ORDER_TO_VENDORS = "permissions.assign_prescription_order_to_vendors", "Can assign prescription order to vendors", [
        AppTypes.ADMIN]
    CONVERT_VIRTUAL_ORDER_TO_REAL_ONE = "permissions.convert_virtual_order_to_real_one", "Can convert virtual order to real one", [
        AppTypes.ADMIN]


class EarlyRefillReasonPermissions(BasePermissionEnum):
    MANAGE_EARLY_REFILL_REASONS = "permissions.manage_early_refill_reasons", "Can Manage early refill reasons", [
        AppTypes.ADMIN]


class PaymentPermissions(BasePermissionEnum):
    MANAGE_PAYMENTS = "permissions.manage_payments", "Can Manage payments", [
        AppTypes.ADMIN]

class ActivityTrackerPermissions(BasePermissionEnum):
    MANGE_ACTIVITY_TRACKER = "permissions.manage_activity_tracker", "Can Manage Activity Tracker", [
        AppTypes.ADMIN]


class TerminologyPermissions(BasePermissionEnum):
    MANAGE_CODE_SYSTEM_REVIEWERS = "permissions.manage_code_system_reviewers", "Can Manage code system reviewers", [
        AppTypes.ADMIN]
    PUBLISH_CODE_SYSTEM = "permissions.publish_code_system", "Can Publish code system", [
        AppTypes.ADMIN]
    UPLOAD_CODE_SYSTEM_FILE = "permissions.upload_code_system_file", "Can Upload code system file", [
        AppTypes.ADMIN]
    MANAGE_CODE_SYSTEM_EDITOR_REVIEWERS = "permissions.manage_code_system_editor_reviewers", "Can Manage code system editor/reviewer", [
        AppTypes.ADMIN]
    MANAGE_CODE_SYSTEM_LISTS = "permissions.manage_code_system_lists", "Can Manage code system lists", [
        AppTypes.ADMIN]
    VIEW_CODE_SYSTEM_LISTS = "permissions.view_code_system_lists", "Can View code system lists", [
        AppTypes.ADMIN]


class RuleEnginePermissions(BasePermissionEnum):
    MANAGE_RULE_ENGINE_RULES = "permissions.manage_rule_engine_rules", "Can Manage rule engine rules", [
        AppTypes.ADMIN]
    MANAGE_RISK_STRATIFICATION = "permissions.manage_risk_stratification", "Can Manage risk stratification", [
        AppTypes.ADMIN]
    MANAGE_LABS = "permissions.manage_labs", "Can Manage labs", [
        AppTypes.ADMIN]
    MANAGE_PARAMETERS = "permissions.manage_parameters", "Can Manage parameters", [
        AppTypes.ADMIN]


class WorkflowPermissions(BasePermissionEnum):
    WORKFLOW_MANAGE_ORDERS = "permissions.workflow_manage_orders", "Can Manage Orders lifecycle", [
        AppTypes.ADMIN]


class DeliveryPermissions(BasePermissionEnum):
    MANAGE_MEDICAL_DELIVERY_REQUESTS = "permissions.manage_medical_delivery_requests", "Can Manage medical delivery requests", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class IntegrationPermissions(BasePermissionEnum):
    MANAGE_PHARMACY_CREDENTIALS = "permissions.manage_pharmacy_credentials", "Can Manage pharmacy credentials", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class SurveyPermissions(BasePermissionEnum):
    MANAGE_SURVEYS = "permissions.manage_surveys", "Can Manage surveys", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_SURVEYS = "permissions.view_surveys", "Can View surveys", [
        AppTypes.ADMIN, AppTypes.VENDOR]

class OptimaPermissions(BasePermissionEnum):
    MANAGE_EDITS_AND_ACTIONS = "permissions.manage_edits_and_actions", "Can Manage Edits and Actions", [
        AppTypes.ADMIN]
    VIEW_TRANSACTION = "permissions.view_transaction", "Can View Transaction", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_VALIDATION_REQUESTS = "permissions.manage_validation_requests", "Can Manage Validation Requests", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_MEDICAL_NECESSITY = "permissions.manage_medical_necessity", "Can Manage Medical Necessity", [
        AppTypes.ADMIN]
    MANAGE_OPTIMA_PAYER_CREDENTIALS = "permissions.manage_optima_payer_credentials", "Can Manage Optima Payer Credentials", [
        AppTypes.VENDOR]

class HealthProgramPermissions(BasePermissionEnum):
    MANAGE_HEALTH_PROGRAMS = "permissions.manage_health_programs", "Can Manage health programs", [
        AppTypes.ADMIN]
    MANAGE_HEALTH_PROGRAMS_CARE_FOR_FIELDS = "permissions.manage_health_programs_care_for_fields", "Can Manage health programs care for fields", [
        AppTypes.ADMIN]

    MANAGE_VISITS = "permissions.manage_visits", "Can Manage visits", [AppTypes.ADMIN]
    MANAGE_OUT_PATIENT_JOURNEYS = "permissions.manage_out_patient_journeys", "Can Manage out patient journeys", [AppTypes.ADMIN,AppTypes.VENDOR]
    MANAGE_VISIT_REJECTION_REASONS = "permissions.manage_visit_rejection_reasons", "Can Manage visit rejection reasons", [
        AppTypes.ADMIN]
    MANAGE_VISIT_CANCEL_REASONS = "permissions.manage_visit_cancel_reasons", "Can Manage visit cancel reasons", [
        AppTypes.ADMIN]
    MANAGE_MEDICATIONS = "permissions.manage_medications", "Can Manage medications", [
        AppTypes.ADMIN]
    MANAGE_DIAGNOSIS = "permissions.manage_diagnosis", "Can Manage diagnosis", [
        AppTypes.ADMIN]
    MEDICATION_SCIENTIFIC_DETAILS = "permissions.manage_scientific_details", "Can Manage scientific details", [
        AppTypes.ADMIN]
    MANAGE_VISIT_SUMMARY = "permissions.manage_visit_summary", "Can Manage visit summary", [
        AppTypes.ADMIN]
    MANAGE_HEALTH_CHANNELS = "permissions.manage_health_channels", "Can Manage health channels", [
        AppTypes.ADMIN]
    MANAGE_HEALTH_CHANNELS_CATEGORIES = "permissions.manage_health_channels_categories", "Can Manage health channels categories", [
        AppTypes.ADMIN]
    MANAGE_MEDICATION_SCIENTIFIC_DETAILS = "permissions.medication_scientific_details", "Can Manage Medication Scientific Details", [
        AppTypes.ADMIN, AppTypes.PAYER]
    MANAGE_PROGRAM_TEMPLATES = "permissions.manage_program_templates", "Can Manage Program Templates", [
        AppTypes.ADMIN]
    MANAGE_PROGRAMS = "permissions.manage_programs", "Can Manage Programs", [
        AppTypes.PAYER, AppTypes.ADMIN]
    MANAGE_PROGRAM_TEAMS = "permissions.manage_program_teams", "Can Manage Program Teams", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_PROGRAM_TEMPLATES = "permissions.view_program_templates", "Can View Program Templates", [
        AppTypes.ADMIN]
    VIEW_PROGRAMS = "permissions.view_programs", "Can View Programs", [
        AppTypes.PAYER, AppTypes.ADMIN]
    VIEW_PROGRAM_TEAMS = "permissions.view_program_teams", "Can View Program Teams", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_CALLBACKS_REQUESTS = "permissions.manage_callbacks_requests", "Can Manage Callbacks Requests", [
        AppTypes.ADMIN]
    MANAGE_MEDICAL_FORMS = "permissions.manage_medical_forms", "Can Manage Medical Forms", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_MEDICAL_FORMS = "permissions.view_medical_forms", "Can View Medical Forms", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_HEALTH_MESSAGES = "permissions.manage_health_messages", "Can Manage Health Messages", [
        AppTypes.ADMIN]
    REVIEW_HEALTH_MESSAGES = "permissions.review_health_messages", "Can Review Health Messages", [
        AppTypes.ADMIN]
    VIEW_HEALTH_MESSAGES = "permissions.view_health_messages", "Can View Health Messages", [
        AppTypes.ADMIN]
    VIEW_VISITS = "permissions.view_visits", "Can View Visits", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_APPOINTMENTS = "permissions.view_appointments", "Can View Appointments", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    VIEW_HEALTH_PROGRAMS = "permissions.view_health_programs", "Can View Health Programs", [
        AppTypes.ADMIN]
    MANAGE_SOCIAL_AND_STREAMING = "permissions.manage_social_and_streaming", "Can Manage Social and Streaming", [
        AppTypes.ADMIN]
    VIEW_PATIENT_ENROLLMENT_REQUEST = "permissions.view_patient_enrollment_request", "Can View Patient Enrollment Request", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_GUIDED_CARE_PATIENTS = "permissions.manage_guided_care_patients", "Can Manage Guided Care Patients", [
        AppTypes.ADMIN,AppTypes.VENDOR]
    MANAGE_CONFERENCE_CONFIGURATION = "permissions.manage_conference_configuration", "Can Manage Conference Configuration", [
        AppTypes.VENDOR]
    CUSTOMER_TEAM_CANCEL_ITEM = "permissions.customer_team_cancel_item", "Can Cancel Item", [
        AppTypes.ADMIN]
    MANAGE_CASE_MANAGEMENT = "permissions.manage_case_management", "Can Manage Case Management", [
        AppTypes.ADMIN]

class InsurancePermissions(BasePermissionEnum):
    MANAGE_INSURANCE = "permissions.manage_insurance", "Can Manage insurances", [
        AppTypes.ADMIN]
    MANAGE_INSURANCE_NETWORKS = "permissions.manage_insurance_networks", "Can Manage insurance networks", [
        AppTypes.ADMIN]


class AppointmentPermissions(BasePermissionEnum):
    MANAGE_APPOINTMENTS = "permissions.manage_appointments", "Can Manage appointments", [
        AppTypes.ADMIN, AppTypes.VENDOR]


class DoctorPermissions(BasePermissionEnum):
    MANAGE_DOCTORS = "permissions.manage_doctors", "Can Manage doctors", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_HEALTH_CONDITIONS = "permissions.manage_health_conditions", [
        "Can manage health conditions", AppTypes.ADMIN]
    MANAGE_HEALTH_SYMPTOMS = "permissions.manage_health_symptoms", [
        "Can manage health symptoms", AppTypes.ADMIN]

class CortexPermissions(BasePermissionEnum):
    MANAGE_CORTEX = "permissions.manage_cortex", "Can Manage cortex", [
        AppTypes.ADMIN]
    VIEW_CORTEX = "permissions.view_cortex", "Can View cortex", [
        AppTypes.ADMIN]

class SpecializationPermissions(BasePermissionEnum):
    MANAGE_SPECIALIZATIONS = "permissions.manage_specializations", "Can Manage subscriptions", [
        AppTypes.ADMIN]


class QualificationPermissions(BasePermissionEnum):
    MANAGE_QUALIFICATIONS = "permissions.manage_qualifications", "Can Manage qualifications", [
        AppTypes.ADMIN]


class HealthPackagePermissions(BasePermissionEnum):
    MANAGE_HEALTH_PACKAGES = "permissions.manage_health_packages", "Can Manage health packages", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    APPROVE_HEALTH_PACKAGES = "permissions.approve_health_packages", "Can Approve health packages", [
        AppTypes.ADMIN]
    MANAGE_HEALTH_PACKAGE_CATEGORIES = "permissions.manage_health_package_categories", "Can Manage health package categories", [
        AppTypes.ADMIN]

class PromotionPermissions(BasePermissionEnum):
    MANAGE_PROMOTIONS = "permissions.manage_promotions", "Can Manage promotions", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_ADMIN_PROMOTIONS = "permissions.manage_admin_promotions", "Can Manage admin promotions", [
        AppTypes.ADMIN]

class ChatFlowPermissions(BasePermissionEnum):
    MANAGE_CHAT_FLOW = "permissions.manage_chat_flow", "Can Manage chat flow", [
        AppTypes.ADMIN, AppTypes.VENDOR]
    MANAGE_CHAT_FLOW_CONTRIBUTOR = "permissions.manage_chat_flow_contributer", "Can Manage chat flow contributer", [
        AppTypes.ADMIN]


class FeatureRatingPermissions(BasePermissionEnum):
    VIEW_FEATURE_RATING = "permissions.view_feature_rating", "Can View Feature Rating", [
        AppTypes.ADMIN]


class ComplaintPermissions(BasePermissionEnum):
    VIEW_COMPLAINTS = "permissions.view_complaints", "Can View Complaints", [
        AppTypes.ADMIN]


PERMISSIONS_ENUMS = [
    # User Management
    AccountPermissions,
    VendorPermissions,
    BranchPermissions,
    BlockPermissions,
    SubscriptionPermissions,
    WalletPermissions,
    ChatPermissions,
    PatientPermissions,
    MedicalEditsPermissions,
    PatientMedicalHistoryPermissions,
    DoctorPermissions,
    SpecializationPermissions,
    QualificationPermissions,
    # Core
    DiscountPermissions,
    PluginsPermissions,
    SitePermissions,
    CheckoutPermissions,
    OrderPermissions,
    ProductPermissions,
    ShippingPermissions,
    PricingRulePermissions,
    OrderRejectionReasonPermissions,
    InvoicePermissions,
    PrescriptionPermissions,
    PrescriptionOrderPermissions,
    EarlyRefillReasonPermissions,
    ActivityTrackerPermissions,
    DeliveryPermissions,
    HealthPackagePermissions,
    # Payment
    PaymentPermissions,
    # Terminology
    TerminologyPermissions,
    # Rule Engine
    RuleEnginePermissions,
    # Workflow
    WorkflowPermissions,
    # Integration Service
    IntegrationPermissions,
    # Health Programs
    HealthProgramPermissions,
    InsurancePermissions,
    SurveyPermissions,
    AppointmentPermissions,
    PromotionPermissions,
    ChatFlowPermissions,
    ArticlePermissions,
    OptimaPermissions,
    PayerPermissions,
    FeatureRatingPermissions,
    ComplaintPermissions,
    CortexPermissions
]
