from django.contrib.auth import models


class AnonymousUser(models.AnonymousUser):
    def get_user_permissions(self, obj=None):
        return set()

    def get_group_permissions(self, obj=None):
        return set()

    def get_all_permissions(self, obj=None):
        return set()

    def has_perm(self, perm, obj=None):
        return False

    def has_perms(self, perm_list, obj=None):
        return False

    def has_module_perms(self, module):
        return False

    def has_one_of_permissions(self, perms):
        return False

    @property
    def is_superuser(self):
        return False

    @property
    def is_staff(self):
        return False

    @property
    def is_admin(self):
        return False

    @property
    def is_vendor_admin(self):
        return False

    @property
    def is_vendor(self):
        return False

    @property
    def is_consumer(self):
        return False

    @property
    def vendor_id(self):
        return None

    @property
    def doctor_id(self):
        return None

    @property
    def driver_id(self):
        return None
