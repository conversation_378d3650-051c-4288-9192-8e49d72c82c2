from .constants import ADMIN_ALLOWED_APPS, VENDOR_ALLOWED_APPS, \
    AGGREGATOR_ALLOWED_APPS, CUSTOMER_ALLOWED_APPS, PAYER_ALLOWED_APPS
from .enums import AppTypes
from .exceptions import PermissionDenied
from .permissions import PERMISSIONS_ENUMS


def validate_user_auth_app(user, app):
    is_admin = (user.is_superuser or user.is_staff) and user.app_type == AppTypes.ADMIN
    is_vendor = user.vendor_id is not None and user.app_type == AppTypes.VENDOR
    is_payer = user.payer_id is not None and user.app_type == AppTypes.PAYER
    is_aggregator = user.app_type == AppTypes.AGGREGATOR
    is_customer = user.app_type == AppTypes.CUSTOMER

    if (is_admin and app in ADMIN_ALLOWED_APPS) or \
            (is_vendor and app in VENDOR_ALLOWED_APPS) or \
            (is_payer and app in PAYER_ALLOWED_APPS) or \
            (is_aggregator and app in AGGREGATOR_ALLOWED_APPS) or \
            (is_customer and app in CUSTOMER_ALLOWED_APPS):
        return True
    raise PermissionDenied()


def allow_login_vendor_if_active(vendor_user):
    if not vendor_user.vendor_id:
        return
    if not vendor_user.vendor.is_active or vendor_user.vendor.deleted:
        raise PermissionDenied()


def get_permissions_enum_list():
    permissions_list = [
        (enum.name, enum.value)
        for permission_enum in PERMISSIONS_ENUMS
        for enum in permission_enum
    ]
    return permissions_list
