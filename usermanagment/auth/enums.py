from enum import Enum
from django.db.models import TextChoices


class AppTypes(str, Enum):
    ADMIN = "Admin"
    VENDOR = "Vendor"
    CUSTOMER = "Customer"
    AGGREGATOR = "Aggregator"
    PAYER = "Payer"

    @staticmethod
    def choices():
        return [(member.value, member.value) for member in AppTypes]

    def __str__(self):
        return str(self.value)


class VendorUserTypes(str, Enum):
    MANAGER = "Manager"
    PHARMACIST = "Pharmacist"
    NURSE = "Nurse"
    DOCTOR = "Doctor"
    RECEPTIONIST = "Receptionist"
    DENTAL_HYGIENIST = "Dental Hygienist"
    DIABETES_EDUCATOR = "Diabetes Educator"
    FITNESS_COACH = "Fitness Coach"
    NUTRITIONIST = "Nutritionist"
    OPTOMETRIST = "Optometrist"
    PODIATRIC_MEDICAL_ASSISTANT = "Podiatric Medical Assistant"
    PSYCHOLOGIST = "Psychologist"
    SOCIAL_WORKER = "Social Worker"
    RCM = "RCM"


    @staticmethod
    def choices():
        return [(member.value, member.value) for member in VendorUserTypes]

    def __str__(self):
        return str(self.value)


class AppRoleTypes(str, Enum):
    ADMIN = "Admin"
    USER = "User"

    @staticmethod
    def choices():
        return [(member.value, member.value) for member in AppRoleTypes]

    def __str__(self):
        return str(self.value)


CUSTOMER_NATIONAL_ID_LOGIN_SUFFIX = "@sehatuk.ae"
VENDOR_NATIONAL_ID_LOGIN_SUFFIX = "@sehatuk.com"


class BiometricLoginType(TextChoices):
    TOUCH_ID = "Touch ID"
    FACE_ID = "Face ID"
