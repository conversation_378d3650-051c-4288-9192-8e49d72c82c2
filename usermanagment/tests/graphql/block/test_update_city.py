from graphene import Node

from ....block.models import City

CITY_UPDATE_BY_ID_MUTATION = """
  mutation cityUpdate($id: ID!, $input: CityInput!) {
    cityUpdate(id: $id, input: $input) {
      city {
        id
        name
        rounds {
          id
         }
      }
      cityErrors {
        code
        message
        field
      }
    }
  }
"""


def test_city_update_by_id(
        superuser_api_client,
        valid_db_city,
        add_site_settings
):
    city_id = Node.to_global_id("City", valid_db_city.pk)
    variables = {
        "id": city_id,
        "input": {
            "name": "updated_name"
        }
    }
    superuser_api_client.execute(CITY_UPDATE_BY_ID_MUTATION, variables=variables)

    assert City.objects.get(pk=valid_db_city.pk).name != valid_db_city.name, \
        "city is not updated in the db"
