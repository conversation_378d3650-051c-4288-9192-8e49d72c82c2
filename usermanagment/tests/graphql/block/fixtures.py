from decimal import Decimal

import pytest
from django.contrib.auth.models import Permission

from ....block.models import City
from ....vendor.enums import VendorTypes


@pytest.fixture
def permission_manage_cities(transactional_db):
    return Permission.objects.get(codename="manage_cities")


@pytest.fixture
def valid_db_city(transactional_db):
    return City.objects.create(name="valid city")


@pytest.fixture
def city_data_without_range_expansion():
    return {
        "name": "cityName",
        "translations": [{
            "languageCode": "AR",
            "name": "اسم المدينة",
        }]
    }


@pytest.fixture
def non_default_range_expansion_data():
    return {
        "maxNumberOfRounds": 11,
        "rounds": [
            {"radius": Decimal('3.33'),
             "maxNumberOfPharmacies": 10,
             "pharmaciesTypes": [VendorTypes.HOSPITAL]
             }
        ],
        "maxNumberOfTries": 100,
        "timeOutPeriod": 55,
    }
