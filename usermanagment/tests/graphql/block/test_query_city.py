from graphene import Node

CITY_BY_ID_QUERY = """
    query cityById($id: ID!) {
      city(id: $id) {
        id
        name
      }
    }
"""

CITIES_QUERY = """
    query cities($first: Int) {
      cities(first: $first) {
        edges{
          node {
            id
            name
          }
        }
      }
    }
"""


def test_query_city_by_id(
        superuser_api_client,
        valid_db_city,
):
    city_id = Node.to_global_id("City", valid_db_city.pk)
    variables = {
        "id": city_id
    }
    data = superuser_api_client.query(CITY_BY_ID_QUERY, variables=variables)

    query_city_id = data["city"]["id"]
    assert query_city_id == city_id


def test_query_cities(
        superuser_api_client,
        valid_db_city,
):
    city_id = Node.to_global_id("City", valid_db_city.pk)
    variables = {
        "first": 10
    }
    data = superuser_api_client.query(CITIES_QUERY, variables=variables)

    query_city_id = data["cities"]["edges"][0]['node']['id']
    assert query_city_id == city_id
