from graphene import Node

from ....block.models import City

CITY_DELETE_BY_ID_MUTATION = """
  mutation cityDelete($id: ID!) {
    cityDelete(id: $id) {
      city {
        id
        name
        rounds {
          id
         }
      }
      cityErrors {
        code
        message
        field
      }
    }
  }
"""


def test_CITY_delete_by_id(
        superuser_api_client,
        valid_db_city,
):
    CITY_id = Node.to_global_id("City", valid_db_city.pk)
    variables = {
        "id": CITY_id
    }
    superuser_api_client.execute(CITY_DELETE_BY_ID_MUTATION, variables=variables)

    assert not City.objects.filter(
        pk=valid_db_city.pk).exists(), "deleted city still exists on database"
