from deepdiff import DeepDiff
from django.contrib.sites.models import Site

from ..utils import assert_no_permission
from ....block.models import City
from ....graphql.utils import get_database_id

CITY_CREATE_MUTATION = """
  mutation cityCreate($input: CityInput!) {
    cityCreate(input: $input) {
      city {
        id
        name
        rounds{
          id
         }
      }
      cityErrors{
        code
        message
        field
      }
    }
  }
"""


def test_city_create_permission_denied(
        consumer_api_client,
        add_site_settings,
        city_data_without_range_expansion
):
    variables = {
        "input": city_data_without_range_expansion
    }
    response = consumer_api_client.execute(CITY_CREATE_MUTATION, variables=variables)

    assert_no_permission(response)


def test_city_create_with_default_expansion_rules(
        superuser_api_client,
        add_site_settings,
        city_data_without_range_expansion
):
    variables = {
        "input": city_data_without_range_expansion

    }
    response = superuser_api_client.execute(CITY_CREATE_MUTATION, variables=variables)

    created_city_id = response["data"]["cityCreate"]["city"]["id"]
    created_city_pk = get_database_id(created_city_id, "City")

    assert City.objects.filter(
        pk=created_city_pk).exists(), "created city doesn't exist on database"

    city = City.objects.get(pk=created_city_pk)

    city_range_expansion_settings = {
        'max_number_of_rounds': city.max_number_of_rounds,
        'rounds': [{
            'radius': city_round.radius,
            'max_number_of_pharmacies': city_round.max_number_of_pharmacies,
            'pharmacies_types': city_round.pharmacies_types
        } for city_round in city.rounds.all()],
        'max_number_of_tries': city.max_number_of_tries,
        'time_out_period': city.time_out_period
    }

    site_settings = Site.objects.get_current().settings
    default_rounds = []
    for i in range(site_settings.range_expansion_max_number_of_rounds):
        default_rounds.append({
            'radius': site_settings.range_expansion_round_radius,
            'max_number_of_pharmacies': site_settings.range_expansion_round_max_number_of_pharmacies,
            'pharmacies_types': site_settings.range_expansion_round_pharmacies_types
        })
    default_range_expansion_settings = {
        'max_number_of_rounds': site_settings.range_expansion_max_number_of_rounds,
        'rounds': default_rounds,
        'max_number_of_tries': site_settings.range_expansion_max_number_of_tries,
        'time_out_period': site_settings.range_expansion_time_out_period
    }

    assert not DeepDiff(default_range_expansion_settings,
                        city_range_expansion_settings,
                        ignore_order=True), \
        "created city has different settings than default one"


def test_city_create_with_non_default_expansion_rules(
        superuser_api_client,
        add_site_settings,
        city_data_without_range_expansion,
        non_default_range_expansion_data
):
    variables = {
        "input": {
            **city_data_without_range_expansion,
            **non_default_range_expansion_data
        }

    }
    response = superuser_api_client.execute(CITY_CREATE_MUTATION, variables=variables)

    created_city_id = response["data"]["cityCreate"]["city"]["id"]
    created_city_pk = get_database_id(created_city_id, "City")

    assert City.objects.filter(
        pk=created_city_pk).exists(), "created city doesn't exist on database"

    city = City.objects.get(pk=created_city_pk)

    city_range_expansion_settings = {
        'maxNumberOfRounds': city.max_number_of_rounds,
        'rounds': [{
            'radius': city_round.radius,
            'maxNumberOfPharmacies': city_round.max_number_of_pharmacies,
            'pharmaciesTypes': city_round.pharmacies_types
        } for city_round in city.rounds.all()],
        'maxNumberOfTries': city.max_number_of_tries,
        'timeOutPeriod': city.time_out_period
    }

    assert not DeepDiff(non_default_range_expansion_data,
                        city_range_expansion_settings, ignore_order=True), \
        "created city has different settings than provided one one"
