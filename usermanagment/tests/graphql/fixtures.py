import logging

import graphene
import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from graphene.test import Client

from .utils import get_graphql_query_data
from ..utils import flush_post_commit_hooks
from ...auth.enums import AppTypes, AppRoleTypes
from ...graphql.api import schema

logger = logging.getLogger(__name__)


class ApiClient(Client):
    """GraphQL API client."""

    def __init__(self, *args, **kwargs):
        self.context = kwargs.pop("context", schema_context(AnonymousUser()))
        super().__init__(schema, *args, **kwargs)

    def execute(
            self,
            query,
            variables=None,
    ):
        result = super().execute(query, context=self.context, variables=variables)
        logger.debug(f"api response => {result}")
        flush_post_commit_hooks()
        return result

    def query(
            self,
            query,
            variables=None,
    ):
        result = super().execute(query, context=self.context, variables=variables)
        logger.debug(f"query response => {result}")
        flush_post_commit_hooks()
        return get_graphql_query_data(result)


@pytest.fixture
def superuser_api_client(superuser):
    return ApiClient(context=schema_context(superuser))


@pytest.fixture
def consumer_api_client(consumer_user):
    return ApiClient(context=schema_context(consumer_user))


@pytest.fixture
def api_client():
    return ApiClient(context=schema_context(AnonymousUser()))


@pytest.fixture
def superuser(transactional_db):
    superuser = get_user_model().objects.create_superuser(
        "<EMAIL>",
        "pass",
        **{
            'mobile': "+970569270096"
        }
    )
    return superuser


@pytest.fixture
def consumer_user(transactional_db):
    superuser = get_user_model().objects.create_user(
        "<EMAIL>",
        "pass",
        **{
            'mobile': "+970569270097",
            'app_type': AppTypes.CUSTOMER,
            'app_role': AppRoleTypes.USER,
        }
    )
    return superuser


def schema_context(user):
    params = {"user": user}
    return graphene.types.Context(**params)
