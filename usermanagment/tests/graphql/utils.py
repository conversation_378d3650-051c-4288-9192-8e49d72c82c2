from ...auth.exceptions import PermissionDenied


def assert_no_errors(response):
    assert "errors" not in response, response


def assert_graphql_error_with_message(response, message):
    assert "errors" in response, response
    assert message in response["errors"][0]["message"], response["errors"]


def assert_no_permission(response):
    assert_graphql_error_with_message(response, str(PermissionDenied()))


def assert_negative_positive_decimal_value(response):
    assert_graphql_error_with_message(response, "Value cannot be lower than 0.")


def assert_typed_graphql_error_with_message(response, error_type, mutation_name,
                                            message):
    assert "data" in response, response
    assert mutation_name in response['data'], response['data']
    assert error_type in response["data"][mutation_name], response["data"][
        mutation_name]
    assert message in response["data"][mutation_name][error_type][0]['message'], \
        response["data"][mutation_name][error_type]


def get_graphql_query_data(response):
    assert_no_errors(response)
    return response['data']
