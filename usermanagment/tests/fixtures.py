import pytest
from django.conf import settings
from django.contrib.sites.models import Site

from ..site.models import SiteSettings


@pytest.fixture
def add_site_settings(transactional_db):
    site_id = getattr(settings, "SITE_ID", 1)
    if site_id:
        Site.objects.get_or_create(
            id=site_id
        )
        SiteSettings.objects.get_or_create(
            site_id=site_id,
        )
