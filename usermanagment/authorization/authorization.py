import graphene
from django.core.exceptions import ValidationError

from ..account.models import User
from ..auth.enums import AppRoleTypes, AppTypes
from ..auth.exceptions import PermissionDenied
from ..auth.permissions import AccountPermissions
from ..graphql.core.utils import from_global_id_strict_type
from ..vendor.models import Branch, BranchUser


def can_manage_vendor(user, vendor_id) -> bool:
    _type, vendor_pk = graphene.Node.from_global_id(vendor_id)
    if not user or str(user.vendor_id) != str(vendor_pk):
        return False

    return True


def can_manage_branch(user, branch_id) -> bool:
    if user.is_admin:
        return True

    _type, branch_pk = graphene.Node.from_global_id(branch_id)
    branch = Branch.objects.filter(id=branch_pk).first()
    if not branch or \
            branch.vendor_id != user.vendor_id or \
            not (user.is_vendor_admin or branch in user.branches.all()):
        return False

    return True


class AuthorizationDto:
    def __init__(self, user, data, user_create=False):
        input_data = data.get('input')

        if user_create:
            self.app_type = input_data.get('app_type')
            self.app_role = input_data.get('app_role')

            self.vendor_id = None
            vendor = input_data.get('vendor')
            if vendor:
                self.vendor_id = int(from_global_id_strict_type(vendor, 'Vendor'))

            self.payer_id = None
            payer = input_data.get('payer')
            if payer:
                self.payer_id = int(from_global_id_strict_type(payer, 'Payer'))

            self.branches_ids = None
            branches = input_data.get('branches')
            if branches:
                self.branches_ids = [int(from_global_id_strict_type(branch, 'Branch'))
                                     for branch in branches]

        else:
            self.app_type = user.app_type
            self.app_role = user.app_role
            self.payer = user.payer
            self.vendor_id = user.vendor_id
            self.payer_id = user.payer_id
            self.branches_ids = [branch_user.branch_id for branch_user in
                                 BranchUser.objects.filter(user_id=user.id)]


def can_manage_user(current_user, authorization_dto):
    # anonymous user is used in customerCreate
    if current_user.is_superuser or \
            (current_user.is_anonymous and
             authorization_dto.app_type == AppTypes.CUSTOMER):
        return True

    app_type = authorization_dto.app_type
    app_role = authorization_dto.app_role
    payer_id = authorization_dto.payer_id
    vendor_id = authorization_dto.vendor_id
    input_branches = authorization_dto.branches_ids

    # user of the same type cannot create/update users of higher app_role
    if not current_user.is_admin and \
            (current_user.app_role == AppRoleTypes.USER
             and app_role == AppRoleTypes.ADMIN):
        return False

    if not current_user.is_staff and app_role == AppRoleTypes.ADMIN \
            and app_type == AppTypes.ADMIN:
        return False

    if current_user.app_type == AppTypes.VENDOR:

        # users cannot create users of other types
        if app_type != AppTypes.VENDOR:
            return False

        # vendor user cannot create vendors outside his branch
        if not vendor_id:
            raise ValidationError({"vendor": "this field is required"})

        if current_user.vendor_id != vendor_id:
            return False

        # vendor user cannot create vendors outside his branch
        if current_user.is_vendor_staff:
            if not input_branches:
                raise ValidationError(
                    {"branches": "must not be empty when appRole is USER"})

            for branch in input_branches:
                if branch not in current_user.branches:
                    return False

    if current_user.app_type == AppTypes.PAYER:

        # users cannot create/update users of other types
        if app_type != AppTypes.PAYER:
            return False
        if not payer_id:
            raise ValidationError({"payer": "this field id required"})
        if current_user.payer_id != payer_id:
            return False

    return True
