from django.core.exceptions import ValidationError

from ..auth.exceptions import PermissionDenied
from ..graphql.vendor.types import Vendor
from ..vendor.error_codes import VendorErrorCode


class VendorAdminAuthMixin:
    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        current_user = info.context.user
        vendor = current_user.vendor

        is_own_entity = True
        if isinstance(instance, list):
            for entity in instance:
                if entity.vendor_id != vendor.id:
                    is_own_entity = False
                    break
        else:
            is_own_entity = instance.vendor_id == vendor.id if instance.pk else True

        if not (vendor and current_user.is_vendor_admin and is_own_entity):
            raise PermissionDenied()

        return {"vendor": vendor}


class SuperuserOrVendorAdminMixin(VendorAdminAuthMixin):
    @classmethod
    def check_authorization(cls, root, info, instance, **data) -> dict:
        if not info.context.user.is_admin:
            return super().check_authorization(root, info, instance, **data)
        else:
            vendor_id = data.get("vendor_id")
            if not vendor_id and not instance.pk:
                raise ValidationError(
                    {"vendorId": ValidationError("This field is required.",
                                                 code=VendorErrorCode.REQUIRED.value)
                     }
                )
            vendor = instance.vendor if instance.pk else cls.get_node_or_error(info,
                                                                               vendor_id,
                                                                               field="vendor",
                                                                               only_type=Vendor)

            return {"vendor": vendor}
