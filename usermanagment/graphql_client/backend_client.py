import json
import logging

from django.conf import settings
from graphene import Node
from sgqlc.endpoint.http import HTTPEndpoint
from sgqlc.operation import Operation
from usermanagment.graphql_client.schema.federated_schema import \
    federated_schema as schema
from django.core.exceptions import ValidationError

from ..account.models import User
from ..auth.enums import AppTypes, AppRoleTypes
from ..graphql.utils.request_utils import get_current_context
from ..keycloak.keycloak_client import KeycloakAPI
from ..vendor import models

logger = logging.getLogger(__name__)


class BackendClient:
    instance = None
    initialized = False
    admin_token = None

    def __new__(cls):
        if cls.instance is None:
            cls.instance = super().__new__(cls)

        return cls.instance

    def __init__(self):
        if not self.initialized:
            self.client = HTTPEndpoint(settings.FEDERATED_BACKEND.get("URL"))
            self.initialized = True

    def execute(self, gq_operation, extra_headers=None):
        try:
            result = self.client(gq_operation,
                                 operation_name=gq_operation._Operation__name,
                                 extra_headers=extra_headers)

            if isinstance(result, dict) and any(
                    'error' in key.lower() for key in result.keys()):
                logger.error(
                    f"error returned from federated backed while executing operation\n{gq_operation}")
                logger.error(f"error ===>>> {result}")

            return result
        except Exception as e:
            logger.error(
                f"error while executing operation on federated backed\n {str(gq_operation)}.",
                exc_info=e)

    def get_site_settings(self):
        query = Operation(schema.Query, name="siteSettings")
        site_settings_query = query.site_settings()
        site_settings_query.admin_two_fa_email_only()

        result = self.execute(query)

        if result is None or not result.get('data'):
            return None

        return (query + result).site_settings

    def get_vendor_by_id(self, vendor_pk):
        query = Operation(schema.Query, name="getVendorById")
        vendor_query = query.vendor(id=Node.to_global_id("Vendor", vendor_pk))
        vendor_query.id()
        vendor_query.is_vip()

        result = self.execute(query)
        vendor = (query + result).vendor
        return vendor

    def notify_users(self, type, category, users_pks,
                           key=None, title=None, body=None, data=None,
                           with_push_notification=False, app=None):

        if users_pks is None:
            super_users = User.objects.filter(
                app_type=AppTypes.ADMIN, app_role=AppRoleTypes.ADMIN, is_active=True)
            users_pks = list(super_users.values_list('pk', flat=True))

        input_data = {
            "type": type,
            "category": category,
            "notified_users": [Node.to_global_id("User", pk) for pk in users_pks],
            "with_push_notification": with_push_notification,
            "app": app
        }
        if key:
            input_data['key'] = key

        if title:
            input_data['title'] = title

        if body:
            input_data['body'] = body

        if isinstance(data, dict):
            input_data['data'] = json.dumps(data)

        operation = Operation(schema.Mutation, name="notifyUsers")
        notification_mutation = operation.notification_create(input=input_data)
        notification_mutation.account_errors.code()
        notification_mutation.account_errors.message()
        notification_mutation.account_errors.field()
        notification_mutation.notification.id()

        try:
            result = self.execute(operation, extra_headers={
                "Authorization": f"JWT {self.__get_admin_token()}"
            })
            data = (operation + result).notification_create

            if hasattr(data, 'notification') and hasattr(data.notification, 'id'):
                return data.notification.id

            if hasattr(data, 'account_errors') and len(data.account_errors):
                logger.error(data.account_errors)
            else:
                logger.error(result)
        except Exception as e:
            logger.error("error while executing notification create.", exc_info=e)

    def __get_admin_token(self):
        self.admin_token = KeycloakAPI().public_keycloak.get_integration_access_token()
        return self.admin_token

    def get_customer_orders_count_by_vendor(self, vendor_pk):
        branch_pks = models.Branch.objects.filter(vendor_id=vendor_pk) \
            .values_list('id', flat=True)
        query = Operation(schema.Query, name="getCustomerOrdersCountByVendor")
        orders = query.orders(filter={
            "branches": [Node.to_global_id("Branch", pk) for pk in branch_pks]
        })
        orders.total_count()

        result = self.execute(query, extra_headers={
            "Authorization": f"JWT {self.get_logged_in_user_token()}"
        })
        count = (query + result).orders.total_count
        return count


    def customer_has_purchased_from_vendor(self, user_id, vendor_id):
        operation = Operation(schema.Mutation, name="customerHasPurchasedFromVendor")
        has_purchased_mutation = operation.customer_has_purchased_from_vendor(
            user_id=Node.to_global_id("User", user_id),
            vendor_id=Node.to_global_id("Vendor", vendor_id)
        )
        has_purchased_mutation.has_purchased()
        has_purchased_mutation.order_errors.code()
        has_purchased_mutation.order_errors.message()
        has_purchased_mutation.order_errors.field()

        try:
            result = self.execute(operation, extra_headers={
                "Authorization": f"JWT {self.__get_admin_token()}"
            })
            data = (operation + result).customer_has_purchased_from_vendor

            if hasattr(data, 'has_purchased'):
                return data.has_purchased

            if hasattr(data, 'order_errors') and len(data.order_errors):
                logger.error(data.order_errors)
            else:
                logger.error(result)
        except Exception as e:
            logger.error("error while executing customerHasPurchasedFromVendor.",
                         exc_info=e)

    @classmethod
    def get_logged_in_user_token(cls):
        context = get_current_context()
        if not context or not context.headers or \
                not context.headers.get('Authorization'):
            return None

        splits = context.headers.get("Authorization").split(" ")
        if len(splits) != 2:
            return None

        return splits[1]


    def get_specialities(self, codes):
        query = Operation(schema.Query, name="specialties")
        specialities_query = query.get_specialties(first=10000, filter={
            "codes": codes})
        specialities_query.edges.node.code()
        specialities_query.edges.node.display()
        specialities_query.edges.node.arabic_display()

        result = self.execute(query, extra_headers={
            "Authorization": f"JWT {self.__get_admin_token()}"
        })
        if result is None or not result['data']:
            return None

        edges = (query + result).get_specialties.edges
        dicts = dict()
        for edge in edges:
            dicts[edge.node.code] = {'display':edge.node.display,'arabic_display': edge.node.arabic_display}
        return dicts


    def validate_code_system_concept(self, code, code_system_code):
        query = Operation(schema.Query, name="getCodeSystemConceptsByCode")

        code_system_concept_query_query = query.get_code_system_concepts_by_code(
            first= 1,
            code_system_code=code_system_code,
            search_parameters=[
                {
                    "code": "code",
                    "valueString": code,
                    "type":"STRING"
                }
            ]
        )
        code_system_concept_query_query.total_count()

        result = self.execute(query, extra_headers={
            "Authorization": f"JWT {self.__get_admin_token()}"
        })
        if result is None or not result['data']:
            return None
        if (query + result).get_code_system_concepts_by_code:
            return (query + result).get_code_system_concepts_by_code.total_count
        else:
            return 0

    def get_national_id_and_payer_license_from_user(self, authorization_header):
        query = Operation(schema.Query, name="me")
        me_query = query.me()
        me_query.national_id()
        me_query.patient.active_health_program_members.insurance_company_license_number()

        result = self.execute(query, extra_headers={
            "Authorization": authorization_header
        })

        response = (query + result).me

        if not response.national_id:
            raise ValidationError("National ID is missing for this user")

        if not response.patient.active_health_program_members or not \
                response.patient.active_health_program_members[0]:
            raise ValidationError(
                "Active health program members are missing for this user")

        return {
            "national_id": response.national_id,
            "insurance_company_license_number":
                response.patient.active_health_program_members[
                    0].insurance_company_license_number
        }
