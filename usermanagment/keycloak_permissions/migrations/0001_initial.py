# Generated by Django 3.2.12 on 2023-01-08 19:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='KeyCloakPermission',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keycloak_role_id', models.CharField(blank=True, max_length=256, null=True, unique=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_vendor', models.BooleanField(default=False)),
                ('is_client', models.BooleanField(default=False)),
                ('is_aggregator', models.BooleanField(default=False)),
                ('permission', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='key_cloak_permission', to='auth.permission')),
            ],
        ),
    ]
