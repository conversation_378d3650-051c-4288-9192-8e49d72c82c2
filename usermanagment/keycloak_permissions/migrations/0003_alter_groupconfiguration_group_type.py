# Generated by Django 3.2.12 on 2023-03-19 12:26

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ('keycloak_permissions', '0002_groupconfiguration'),
    ]

    operations = [
        migrations.AlterField(
            model_name='groupconfiguration',
            name='group_type',
            field=usermanagment.core.db.fields.EnumField(
                choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'),
                         ('Customer', 'Customer'), ('Aggregator', 'Aggregator'),
                         ('Payer', 'Payer')], max_length=20),
        ),
    ]
