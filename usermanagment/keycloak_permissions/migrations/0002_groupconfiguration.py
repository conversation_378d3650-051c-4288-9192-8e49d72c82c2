# Generated by Django 3.2.12 on 2023-01-10 19:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vendor', '0023_branch_accepts_delivery'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('keycloak_permissions', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupConfiguration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keycloak_group_id', models.CharField(blank=True, max_length=256, null=True, unique=True)),
                ('group_type', usermanagment.core.db.fields.EnumField(choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'), ('Customer', 'Customer'), ('Aggregator', 'Aggregator')], max_length=20)),
                ('is_editable', models.BooleanField(default=True)),
                ('is_global', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('group', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='group_configuration', to='auth.group')),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='vendor.vendor')),
            ],
        ),
    ]
