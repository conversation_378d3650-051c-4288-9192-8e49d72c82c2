

from django.db import models

from usermanagment import settings
from usermanagment.auth.enums import AppTypes
from usermanagment.core.db.fields import EnumField


class KeyCloakPermission(models.Model):
    keycloak_role_id = models.CharField(max_length=256, null=True, blank=True,
                                        unique=True)

    is_staff = models.BooleanField(default=False)

    is_vendor = models.BooleanField(default=False)

    is_client = models.BooleanField(default=False)

    is_aggregator = models.BooleanField(default=False)

    permission = models.OneToOneField(
        "auth.Permission",
        null=True, blank=True,
        related_name="key_cloak_permission",
        on_delete=models.CASCADE)


class GroupConfiguration(models.Model):
    keycloak_group_id = models.CharField(max_length=256,
                                         null=True, blank=True,
                                         unique=True)

    group_type = EnumField(max_length=20,
                           choices=AppTypes.choices(),
                           null=False, blank=False)

    is_editable = models.<PERSON>oleanField(default=True)

    is_global = models.<PERSON>oleanField(default=False)

    vendor = models.ForeignKey(
        "vendor.Vendor",
        null=True, blank=True,
        related_name="+",
        on_delete=models.CASCADE)

    payer = models.ForeignKey(
        "payer.Payer",
        null=True, blank=True,
        related_name="+",
        on_delete=models.CASCADE)

    group = models.OneToOneField(
        "auth.Group",
        null=True, blank=True,
        related_name="group_configuration",
        on_delete=models.CASCADE)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=False, null=True, editable=False,
        related_name="+", on_delete=models.SET_NULL
    )
    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=False, null=True, editable=False,
        related_name="+", on_delete=models.SET_NULL
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
