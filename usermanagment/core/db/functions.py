from django.db.models import Aggregate


class ListAgg(Aggregate):
    function = 'GROUP_CONCAT'
    template = '%(function)s(%(distinct)s%(expressions)s, \'%(delimiter)s\')'
    allow_distinct = True
    delimiter = ','

    class Meta:
        abstract = True

    def as_sql(self, compiler, connection, **extra_context):
        extra_context['delimiter'] = self.delimiter
        return super().as_sql(compiler, connection, **extra_context)

    def convert_value(self, value, expression, connection):
        if not value:
            return []
        return list(map(self.map_func, list(filter(None, value.split(self.delimiter)))))


class ListAggInt(ListAgg):
    map_func = int


class ListAggStr(ListAgg):
    map_func = str
