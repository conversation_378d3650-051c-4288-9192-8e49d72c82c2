import json
from typing import Callable

from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Char<PERSON>ield


class SanitizedJ<PERSON><PERSON>ield(JSONField):
    description = "A JSON field that runs a given sanitization method "
    "before saving into the database."

    def __init__(self, *args, sanitizer: Callable[[dict], dict], **kwargs):
        super(Sanitized<PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self._sanitizer_method = sanitizer

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        kwargs["sanitizer"] = self._sanitizer_method
        return name, path, args, kwargs

    def get_db_prep_save(self, value: dict, connection):
        """Sanitize the value for saving using the passed sanitizer."""
        return json.dumps(self._sanitizer_method(value))


class EnumField(CharField):

    def __init__(self, *args, **kwargs):
        self.empty_strings_allowed = False
        super().__init__(*args, **kwargs)
