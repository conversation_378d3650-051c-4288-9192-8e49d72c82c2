from django.core.validators import MaxLengthValidator
from django.db import models
from django.db.models import QuerySet, Max, F
from django.utils import timezone

from .. import settings
from ..account import models as account_models
from ..graphql.utils.request_utils import get_current_user


class AuditModel(models.Model):
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=False, null=True, editable=False,
        related_name="+", on_delete=models.SET_NULL
    )
    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=False, null=True, editable=False,
        related_name="+", on_delete=models.SET_NULL
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        if self.pk and kwargs.get('update_fields'):
            update_fields = list(
                set(kwargs.get('update_fields') + ['modified', 'modified_by']))

            kwargs['update_fields'] = update_fields

        user = get_current_user()
        if isinstance(user, account_models.User):
            if not self.pk:
                self.created_by = user
            else:
                self.modified_by = user
        super().save(*args, **kwargs)


class SoftDeletionQuerySet(QuerySet):
    def delete(self):
        data = {}
        if hasattr(self.model, "is_active"):
            data["is_active"] = False

        for f in self.model._meta.get_fields():
            if not f.is_relation and not f.primary_key and f._unique:
                if getattr(self, f.name, None) is not None:
                    data[f.name] = f"{getattr(self, f.name)}_deleted_{self.pk}"

        return super(SoftDeletionQuerySet, self).update(
            deleted=True,
            deleted_at=timezone.now(),
            deleted_by=get_current_user(),
            **data
        )


class SoftDeletionManager(models.Manager):
    def __init__(self, *args, **kwargs):
        self.deleted = kwargs.pop('deleted', False)
        super(SoftDeletionManager, self).__init__(*args, **kwargs)

    def get_queryset(self):
        if self.deleted is not None:
            return SoftDeletionQuerySet(self.model).filter(deleted=self.deleted)
        return SoftDeletionQuerySet(self.model)


class SoftDeletionModel(models.Model):
    deleted = models.BooleanField(default=False,db_index=True)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=False, null=True, editable=False,
        related_name="+", on_delete=models.SET_NULL
    )

    objects = SoftDeletionManager()

    class Meta:
        abstract = True

    def delete(self, *args, **kwargs):
        updated_fields = ['deleted', 'deleted_at', 'deleted_by']
        self.deleted = True
        self.deleted_at = timezone.now()
        self.deleted_by = get_current_user()

        if hasattr(self, "is_active"):
            setattr(self, "is_active", False)
            updated_fields.append("is_active")

        for f in self._meta.get_fields():
            if not f.is_relation and not f.primary_key and f._unique:
                if getattr(self, f.name, None) is not None:
                    setattr(self, f.name, f"{getattr(self, f.name)}_deleted_{self.pk}")
                    updated_fields.append(f.name)

        self.save(update_fields=updated_fields)

    def __repr__(self):
        return str(self.pk)


class UserAccessibleQueryset(models.QuerySet):

    def current_user_accessible_objects(self, current_user):
        if not current_user.is_superuser:
            qs = self.filter(vendor_id=current_user.vendor_id)
            return qs
        return self


class SeoModel(models.Model):
    seo_title = models.CharField(
        max_length=70, blank=True, null=True, validators=[MaxLengthValidator(70)]
    )
    seo_description = models.CharField(
        max_length=300, blank=True, null=True, validators=[MaxLengthValidator(300)]
    )

    class Meta:
        abstract = True


class SortableModel(models.Model):
    sort_order = models.IntegerField(editable=False, db_index=True, null=True)

    class Meta:
        abstract = True

    def get_ordering_queryset(self):
        raise NotImplementedError("Unknown ordering queryset")

    def get_max_sort_order(self, qs):
        existing_max = qs.aggregate(Max("sort_order"))
        existing_max = existing_max.get("sort_order__max")
        return existing_max

    def save(self, *args, **kwargs):
        if self.pk is None:
            qs = self.get_ordering_queryset()
            existing_max = self.get_max_sort_order(qs)
            self.sort_order = 0 if existing_max is None else existing_max + 1
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if self.sort_order is not None:
            qs = self.get_ordering_queryset()
            qs.filter(sort_order__gt=self.sort_order).update(
                sort_order=F("sort_order") - 1
            )
        super().delete(*args, **kwargs)


class BaseTranslationModel(models.Model):
    language_code = models.CharField(max_length=10)

    class Meta:
        abstract = True
