from dataclasses import dataclass
from decimal import Decimal
from typing import Union

from babel.numbers import get_currency_precision
from django.conf import settings
from prices import Money, TaxedMoney, TaxedMoneyRange


class TaxError(Exception):
    """Default tax error."""


def zero_money(currency: str = settings.DEFAULT_CURRENCY) -> Money:
    """Return a money object set to zero.

    This is a function used as a model's default.
    """
    return Money(0, currency)


def zero_taxed_money(currency: str = settings.DEFAULT_CURRENCY) -> TaxedMoney:
    zero = zero_money(currency)
    return TaxedMoney(net=zero, gross=zero)


def quantize_price(
        price: Union["TaxedMoney", "Money", "Decimal", "TaxedMoneyRange"], currency: str
) -> Union["TaxedMoney", "Money", "Decimal", "TaxedMoneyRange"]:
    precision = get_currency_precision(currency)
    number_places = Decimal(10) ** -precision
    return price.quantize(number_places)


@dataclass(frozen=True)
class TaxType:
    """Dataclass for unifying tax type object that comes from tax gateway."""

    code: str
    description: str
