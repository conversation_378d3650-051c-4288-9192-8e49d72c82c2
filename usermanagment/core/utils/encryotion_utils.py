from Crypto.Cipher import AES
from Crypto.Hash import SHA256
import base64

password = ("AabXANMr")
hash_obj = SHA256.new(password.encode('utf-8'))
hkey = hash_obj.digest()
class EncryptionUtils:
    @staticmethod
    def encrypt(text):
        if text is None:
            return None
        BLOCK_SIZE = 16
        PAD = "{"
        padding = lambda s: s + (BLOCK_SIZE - len(s) % BLOCK_SIZE) * PAD
        cipher = AES.new(hkey, AES.MODE_ECB)
        return  base64.b64encode(cipher.encrypt(padding(text).encode('utf-8'))).decode('utf-8')

    @staticmethod
    def decrypt(encrypted):
        if encrypted is None:
            return None
        PAD = "{"
        decipher = AES.new(hkey, AES.MODE_ECB)
        encrypted_bytes = base64.b64decode(encrypted)
        pt = decipher.decrypt(encrypted_bytes).decode('utf-8')
        pad_index = pt.find(PAD)
        return pt[: pad_index]
