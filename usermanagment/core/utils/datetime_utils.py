from datetime import datetime, date

from datetimerange import DateT<PERSON><PERSON><PERSON><PERSON>


def time_ranges_has_intersection(time_ranges):
    for i, i_range in enumerate(time_ranges):
        sub_ranges = time_ranges[:i] + time_ranges[i + 1:]
        start = datetime.combine(date.today(), i_range['open_time'])
        end = datetime.combine(date.today(), i_range['close_time'])
        reference_range = DateTimeRange(start, end)
        for j_range in sub_ranges:
            start = datetime.combine(date.today(), j_range['open_time'])
            end = datetime.combine(date.today(), j_range['close_time'])
            target_range = DateTimeRange(start, end)
            if reference_range.is_intersection(target_range):
                return True

    return False


def calculate_age(birth_date):
    if birth_date:
        age = int((date.today() - birth_date).days / 365.2425)
        return age
