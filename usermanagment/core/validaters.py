from decimal import Decimal
from django.core.exceptions import ValidationError
from django.core.validators import BaseValidator
from django.utils.deconstruct import deconstructible
from django.utils.translation import gettext_lazy as _

import re
import logging

from usermanagment.account.models import User

logger = logging.getLogger(__name__)


def validate_password(password):
    if len(password) < 8:
        raise ValidationError(
            _("Password must contain at least 8 characters."),
            code='password_too_short',
        )
    if not re.search(r'[A-Z]', password):
        raise ValidationError(
            _("Password must contain at least one uppercase letter."),
            code='password_no_uppercase',
        )
    if not re.search(r'[a-z]', password):
        raise ValidationError(
            _("Password must contain at least one lowercase letter."),
            code='password_no_lowercase',
        )
    if not re.search(r'\d', password):
        raise ValidationError(
            _("Password must contain at least one digit."),
            code='password_no_digit',
        )
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise ValidationError(
            _("Password must contain at least one special character."),
            code='password_no_special_char',
        )

def generate_and_validate_password():
    return 'Aahnqz#=CPKyWv]gFR.8K#+'
    # password = None
    # while password is None:
    #     try:
    #         temp_password = User.objects.make_random_password(
    #             length=8,
    #             allowed_chars='ABCDEFGHJKLMNPQRSTUVWXYZ'
    #                           'abcdefghjkmnpqrstuvwxyz'
    #                           '23456789'
    #                           '!@#$%^&*(),.?":{}|<>'
    #         )
    #         validate_password(temp_password)
    #         password = temp_password
    #     except ValidationError as e:
    #         logger.error(f"password validation error: {e}")
    #         continue
    # return password


@deconstructible
class ProhibitZeroDecimalValidator(BaseValidator):
    message = _('Ensure this value is not Zero.')
    code = 'not_zero_value'

    def __init__(self):
        super().__init__(Decimal(0))

    def compare(self, a, b):
        return a == b
