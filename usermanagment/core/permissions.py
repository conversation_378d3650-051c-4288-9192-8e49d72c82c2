from django.contrib.auth.models import Permission

from .filters import permission_keycloak_filters
from ..auth.permissions import (
    PERMISSIONS_ENUMS,
    BranchPermissions,
    CheckoutPermissions,
    OrderPermissions,
    ProductPermissions,
    DiscountPermissions,
    AccountPermissions,
    ChatPermissions,
    InvoicePermissions,
    DeliveryPermissions,
    IntegrationPermissions,
    PrescriptionPermissions,
    HealthProgramPermissions,
    PatientPermissions,
    AppointmentPermissions, HealthPackagePermissions, PromotionPermissions,
    MedicalEditsPermissions,
    VendorPermissions, SitePermissions, DoctorPermissions, ChatFlowPermissions,
    OptimaPermissions

)


def split_permission_codename(permissions):
    return [permission.split(".")[1] for permission in permissions]


def get_permissions_codename():
    permissions_values = [
        enum.codename
        for permission_enum in PERMISSIONS_ENUMS
        for enum in permission_enum
    ]
    return permissions_values


def get_permissions_codename_with_hash():
    permissions_values = []
    codename_permissions = {}
    for permission_enum in PERMISSIONS_ENUMS:
        for enum in permission_enum:
            permissions_values.append(enum.codename)
            codename_permissions[enum.codename] = enum
    return permissions_values, codename_permissions


def get_static_group_permissions_codename(permissions):
    permissions_values = [
        enum.codename
        for enum in permissions
    ]
    return permissions_values


def get_permissions(permissions=None, app_type=None):
    if permissions is None:
        codenames = get_permissions_codename()
    else:
        codenames = split_permission_codename(permissions)

    if app_type is None:
        return (
            Permission.objects.select_related("key_cloak_permission",
                                              "content_type").filter(
                codename__in=codenames)
        )

    return (
        Permission.objects.select_related("key_cloak_permission",
                                          "content_type").filter(
            codename__in=codenames,
            **{permission_keycloak_filters[app_type]: True}
        )
    )
    # if app_type == AppTypes.ADMIN:
    #     return (
    #         Permission.objects.select_related("key_cloak_permission").filter(
    #             codename__in=codenames,
    #             key_cloak_permission__is_staff=True
    #         )
    #     )
    # if app_type == AppTypes.VENDOR:
    #     return (
    #         Permission.objects.select_related("key_cloak_permission").filter(
    #             codename__in=codenames,
    #             key_cloak_permission__is_vendor=True
    #         )
    #     )
    # if app_type == AppTypes.CUSTOMER:
    #     return (
    #         Permission.objects.select_related("key_cloak_permission").filter(
    #             codename__in=codenames,
    #             key_cloak_permission__is_client=True
    #         )
    #     )
    # if app_type == AppTypes.AGGREGATOR:
    #     return (
    #         Permission.objects.select_related("key_cloak_permission").filter(
    #             codename__in=codenames,
    #             key_cloak_permission__is_aggregator=True
    #         )
    #     )


RCM_AUTH_GROUP_NAME = "rcm"
RCM_AUTH_GROUP_PERMISSIONS = [
    OptimaPermissions.MANAGE_VALIDATION_REQUESTS,
    OptimaPermissions.VIEW_TRANSACTION,
]

VENDOR_ADMIN_AUTH_GROUP_NAME = "vendor super admin"
VENDOR_ADMIN_AUTH_GROUP_PERMISSIONS = [
    BranchPermissions.MANAGE_BRANCHES,
    CheckoutPermissions.MANAGE_CHECKOUTS,
    OrderPermissions.VIEW_ORDERS,
    DiscountPermissions.MANAGE_DISCOUNTS,
    AccountPermissions.MANAGE_USERS,
    DoctorPermissions.MANAGE_DOCTORS,
    AccountPermissions.VIEW_CUSTOMERS,
    ChatPermissions.MANAGE_CHAT,
    InvoicePermissions.MANAGE_INVOICES,
    DeliveryPermissions.MANAGE_MEDICAL_DELIVERY_REQUESTS,
    IntegrationPermissions.MANAGE_PHARMACY_CREDENTIALS,
    PrescriptionPermissions.MANAGE_PRESCRIPTIONS,
    HealthProgramPermissions.MANAGE_PROGRAM_TEAMS,
    HealthProgramPermissions.VIEW_PROGRAMS,
    AppointmentPermissions.MANAGE_APPOINTMENTS,
    HealthPackagePermissions.MANAGE_HEALTH_PACKAGES,
    PromotionPermissions.MANAGE_PROMOTIONS,
    VendorPermissions.MANAGE_DEPARTMENTS,
    OrderPermissions.MANAGE_DELIVERY_TIME_SLOTS,
    SitePermissions.MANAGE_SETTINGS,
    HealthProgramPermissions.MANAGE_VISITS,
    HealthProgramPermissions.MANAGE_MEDICAL_FORMS,
    OrderPermissions.MANAGE_HEALTH_PACKAGE_ORDERS,
    ChatFlowPermissions.MANAGE_CHAT_FLOW,
    ProductPermissions.MANAGE_PRODUCTS,
    OrderPermissions.MANAGE_MARKETPLACE_ORDERS,
    MedicalEditsPermissions.VALIDATE_PROCEDURES,
    OptimaPermissions.VIEW_TRANSACTION,
    OptimaPermissions.MANAGE_VALIDATION_REQUESTS,
    OptimaPermissions.MANAGE_OPTIMA_PAYER_CREDENTIALS,
    SitePermissions.MANAGE_DASHBOARD,
    HealthProgramPermissions.MANAGE_GUIDED_CARE_PATIENTS,
    HealthProgramPermissions.MANAGE_CONFERENCE_CONFIGURATION
]

PHARMACIST_USER_AUTH_GROUP_NAME = "pharmacist"
PHARMACIST_USER_AUTH_GROUP_PERMISSIONS = [
    OrderPermissions.MANAGE_ORDERS,
    AccountPermissions.VIEW_CUSTOMERS,
    PrescriptionPermissions.MANAGE_PRESCRIPTIONS,
    DeliveryPermissions.MANAGE_MEDICAL_DELIVERY_REQUESTS,
    OrderPermissions.MANAGE_MARKETPLACE_ORDERS
]

DOCTOR_USER_AUTH_GROUP_NAME = "doctor"
DOCTOR_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    PatientPermissions.VERIFY_MEMBER_IDS,
    HealthProgramPermissions.VIEW_PROGRAMS,
    MedicalEditsPermissions.VALIDATE_PROCEDURES,
    HealthProgramPermissions.VIEW_PATIENT_ENROLLMENT_REQUEST,
    SitePermissions.MANAGE_DASHBOARD,
    HealthProgramPermissions.MANAGE_GUIDED_CARE_PATIENTS
]

DENTAL_HYGIENIST_USER_AUTH_GROUP_NAME = "dental_hygienist"
DENTAL_HYGIENIST_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

DIABETES_EDUCATOR_USER_AUTH_GROUP_NAME = "diabetes_educator"
DIABETES_EDUCATOR_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

FITNESS_COACH_USER_AUTH_GROUP_NAME = "fitness_coach"
FITNESS_COACH_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

NUTRITIONIST_USER_AUTH_GROUP_NAME = "nutritionist"
NUTRITIONIST_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

OPTOMETRIST_USER_AUTH_GROUP_NAME = "optometrist"
OPTOMETRIST_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_NAME = "podiatric_medical_assistant"
PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

PSYCHOLOGIST_USER_AUTH_GROUP_NAME = "psychologist"
PSYCHOLOGIST_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

SOCIAL_WORKER_USER_AUTH_GROUP_NAME = "social_worker"
SOCIAL_WORKER_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    HealthProgramPermissions.VIEW_PROGRAMS,
]

NURSE_USER_AUTH_GROUP_NAME = "nurse"
NURSE_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    PatientPermissions.VIEW_PATIENTS,
    PrescriptionPermissions.MANAGE_PRESCRIPTIONS,
    AppointmentPermissions.MANAGE_APPOINTMENTS,
    HealthProgramPermissions.MANAGE_VISITS,
    SitePermissions.MANAGE_DASHBOARD,
    HealthProgramPermissions.MANAGE_GUIDED_CARE_PATIENTS
]

RECEPTIONIST_USER_AUTH_GROUP_NAME = "receptionist"
RECEPTIONIST_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    OrderPermissions.MANAGE_HEALTH_PACKAGE_ORDERS,
    AppointmentPermissions.MANAGE_APPOINTMENTS,
    HealthProgramPermissions.MANAGE_VISITS,
]

PATIENT_USER_AUTH_GROUP_NAME = "patient"
PATIENT_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_USERS,
]

PAYER_USER_AUTH_GROUP_NAME = "payer user"
PAYER_USER_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.VIEW_CUSTOMERS,
    HealthProgramPermissions.MANAGE_HEALTH_PROGRAMS,
    HealthProgramPermissions.VIEW_HEALTH_PROGRAMS,
    HealthProgramPermissions.MANAGE_HEALTH_PROGRAMS_CARE_FOR_FIELDS,
    HealthProgramPermissions.MANAGE_VISIT_SUMMARY,
    HealthProgramPermissions.MANAGE_HEALTH_CHANNELS,
    HealthProgramPermissions.MANAGE_HEALTH_CHANNELS_CATEGORIES,
    MedicalEditsPermissions.VALIDATE_PROCEDURES,
]

PAYER_ADMIN_AUTH_GROUP_NAME = "payer super admin"
PAYER_ADMIN_AUTH_GROUP_PERMISSIONS = [
    AccountPermissions.MANAGE_USERS,
    AccountPermissions.VIEW_CUSTOMERS,
    ChatPermissions.MANAGE_CHAT,
    InvoicePermissions.MANAGE_INVOICES,
    HealthProgramPermissions.MANAGE_HEALTH_PROGRAMS,
    HealthProgramPermissions.VIEW_HEALTH_PROGRAMS,
    HealthProgramPermissions.MANAGE_HEALTH_PROGRAMS_CARE_FOR_FIELDS,
    HealthProgramPermissions.MANAGE_VISIT_REJECTION_REASONS,
    HealthProgramPermissions.MANAGE_VISIT_CANCEL_REASONS,
    HealthProgramPermissions.MANAGE_MEDICATIONS,
    HealthProgramPermissions.MANAGE_DIAGNOSIS,
    HealthProgramPermissions.MANAGE_MEDICATION_SCIENTIFIC_DETAILS,
    HealthProgramPermissions.MANAGE_VISIT_SUMMARY,
    HealthProgramPermissions.MANAGE_HEALTH_CHANNELS,
    HealthProgramPermissions.MANAGE_HEALTH_CHANNELS_CATEGORIES,
    HealthProgramPermissions.MANAGE_PROGRAMS,
    HealthProgramPermissions.VIEW_PROGRAM_TEMPLATES,
    HealthProgramPermissions.VIEW_PROGRAM_TEAMS,
    MedicalEditsPermissions.VALIDATE_PROCEDURES,
]

DHIC_USER_AUTH_GROUP_NAME = "dhic user"
DHIC_USER_AUTH_GROUP_PERMISSIONS = [
    HealthProgramPermissions.MANAGE_PROGRAM_TEMPLATES,
    HealthProgramPermissions.VIEW_PROGRAMS,
    HealthProgramPermissions.VIEW_PROGRAM_TEAMS,
    AccountPermissions.VIEW_USERS
]

NOT_EDITABLE_GROUPS = [VENDOR_ADMIN_AUTH_GROUP_NAME,
                       PHARMACIST_USER_AUTH_GROUP_NAME,
                       DOCTOR_USER_AUTH_GROUP_NAME,
                       DENTAL_HYGIENIST_USER_AUTH_GROUP_NAME,
                       DIABETES_EDUCATOR_USER_AUTH_GROUP_NAME,
                       FITNESS_COACH_USER_AUTH_GROUP_NAME,
                       NUTRITIONIST_USER_AUTH_GROUP_NAME,
                       OPTOMETRIST_USER_AUTH_GROUP_NAME,
                       PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_NAME,
                       PSYCHOLOGIST_USER_AUTH_GROUP_NAME,
                       SOCIAL_WORKER_USER_AUTH_GROUP_NAME,
                       NURSE_USER_AUTH_GROUP_NAME
                       ]
