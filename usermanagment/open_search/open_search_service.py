import logging

from graphene import Node
from opensearchpy import OpenSearch
from opensearchpy.helpers import bulk
from opensearchpy.exceptions import (
    OpenSearchException,
    ConnectionError,
    NotFoundError,
    RequestError,
    TransportError
)

from usermanagment import settings

logger = logging.getLogger(__name__)


class OpenSearchService:
    initialized = False

    def __new__(cls):
        if not hasattr(cls, 'instance'):
            cls.instance = super(OpenSearchService, cls).__new__(cls)
        return cls.instance

    def __init__(self):
        if not self.initialized:
            self.client = OpenSearch(
                hosts=[{"host": settings.OPEN_SEARCH.get("HOST"),
                        "port": settings.OPEN_SEARCH.get("PORT")}],
                http_auth=(settings.OPEN_SEARCH.get("USER_NAME"),
                           settings.OPEN_SEARCH.get("PASSWORD")),
                use_ssl=settings.OPEN_SEARCH.get("USE_SSL"),
                verify_certs=False,
                ssl_assert_hostname=False,
                ssl_show_warn=False,
            )
            self.initialized = True

    def delete(self, entity_id):
        try:
            self.client.delete(index="full_text_search_entities", id=entity_id)
        except NotFoundError:
            logger.warning(f"Entity {entity_id} not found in OpenSearch")
        except ConnectionError as e:
            logger.error(f"OpenSearch connection error during delete: {e}")
        except OpenSearchException as e:
            logger.error(f"OpenSearch error during delete: {e}")
        except Exception as e:
            logger.exception(f"Unexpected error during delete: {e}")

    def is_full_text_search_entities_index_exist(self):
        try:
            return self.client.indices.exists(index="full_text_search_entities")
        except ConnectionError as e:
            logger.error(f"OpenSearch connection error checking index existence: {e}")
            return False
        except OpenSearchException as e:
            logger.error(f"OpenSearch error checking index existence: {e}")
            return False
        except Exception as e:
            logger.exception(f"Unexpected error checking index existence: {e}")
            return False

    def create_full_text_search_entities_index(self):
        try:
            body = {
                "mappings": {
                    "properties": {
                        "entity_type": {"type": "text", "analyzer": "standard"},
                        "entity_id": {"type": "text", "analyzer": "standard"},
                        "keywords": {"type": "nested",
                                     "properties": {
                                         "text": {"type": "text",
                                                  "analyzer": "standard"}
                                     }
                                     },
                    }
                }
            }
            self.client.indices.create(index="full_text_search_entities", body=body)
            return True
        except RequestError as e:
            if "resource_already_exists_exception" in str(e):
                logger.info("Index 'full_text_search_entities' already exists")
                return True
            logger.error(f"OpenSearch request error creating index: {e}")
            return False
        except ConnectionError as e:
            logger.error(f"OpenSearch connection error creating index: {e}")
            return False
        except OpenSearchException as e:
            logger.error(f"OpenSearch error creating index: {e}")
            return False
        except Exception as e:
            logger.exception(f"Unexpected error creating index: {e}")
            return False

    def save(self, entity_type, entities):
        try:
            bulk_data = []
            for entity in entities:
                keywords = [{"text": entity.get('name')}]
                if entity.get('bio'):
                    keywords.append({"text": entity.get('bio')})
                if entity.get('name_ar'):
                    keywords.append({"text": entity.get('name_ar')})
                if entity.get('vendor_name'):
                    keywords.append({"text": entity.get('vendor_name')})
                if entity.get('vendor_name_ar'):
                    keywords.append({"text": entity.get('vendor_name_ar')})
                for speciality in entity.get('specialities', []):
                    keywords.append({"text": speciality})

                data = {
                    "_index": "full_text_search_entities",
                    "_id": entity.get('id'),
                    "_source": {
                        "entity_id": entity.get('id'),
                        "entity_type": entity_type,
                        "keywords": keywords,
                    }
                }
                bulk_data.append(data)
            success, failed = bulk(self.client, bulk_data, raise_on_error=False)
            if failed:
                logger.error(f"Failed to index {len(failed)} documents: {failed}")
            if success:
                logger.debug(f"Successfully indexed {success} documents")
        except ConnectionError as e:
            logger.error(f"OpenSearch connection error during bulk save: {e}")
        except TransportError as e:
            logger.error(f"OpenSearch transport error during bulk save: {e}")
        except OpenSearchException as e:
            logger.error(f"OpenSearch error during bulk save: {e}")
        except Exception as e:
            logger.exception(f"Unexpected error during bulk save: {e}")

    def count_by_entity_type(self, entity_type):
        try:
            resp = self.client.search(
                index="full_text_search_entities",
                body={
                    "query": {
                        "match": {
                            "entity_type": entity_type
                        }
                    },
                    "size": 1
                }
            )
            return resp['hits']['total']['value']
        except ConnectionError as e:
            logger.error(f"OpenSearch connection error counting entities: {e}")
            return 0
        except OpenSearchException as e:
            logger.error(f"OpenSearch error counting entities: {e}")
            return 0
        except Exception as e:
            logger.exception(f"Unexpected error counting entities: {e}")
            return 0

    def search_by_keyword(self, keyword, entity_type, page, size):
        try:
            entity_type_query = {
                "match_phrase": {
                    "entity_type": entity_type
                }
            }
            body = {
                "_source": ["entity_id"],
                "query": {
                    "bool": {
                        "must": [
                            {
                                "nested": {
                                    "path": "keywords",
                                    "query": {
                                        "wildcard": {
                                            "keywords.text": {
                                                "value": "*" + k + "*",
                                                "case_insensitive": True
                                            }
                                        }
                                    }
                                }
                            } for k in keyword.split()

                        ]
                    }
                },
                "size": size,
                "from": page * size,
                "sort": [
                    {
                        "_score": {
                            "order": "desc"
                        }
                    }
                ]
            }

            if entity_type:
                body.get('query').get('bool').get('must').append(entity_type_query)

            resp = self.client.search(
                index="full_text_search_entities",
                body=body
            )
            res = []
            for hit in resp['hits']['hits']:
                res.append(hit['_source'])
            return res
        except ConnectionError as e:
            logger.error(f"OpenSearch connection error during search: {e}")
            return []
        except RequestError as e:
            logger.error(f"OpenSearch request error during search: {e}")
            return []
        except OpenSearchException as e:
            logger.error(f"OpenSearch error during search: {e}")
            return []
        except Exception as e:
            logger.exception(f"Unexpected error during search: {e}")
            return []


    def sync_doctors(self, doctors):
        try:
            from usermanagment.graphql_client.backend_client import BackendClient
            entities = []
            specializations=[]
            for row in doctors:
                specializations.extend([speciality.code for speciality in row.specializations.all()])
            specializations = BackendClient().get_specialities(
                set(specializations))

            for row in doctors:
                doctor_specialities = []
                for speciality in row.specializations.all():
                    doctor_speciality=specializations.get(speciality.code,None)
                    if doctor_speciality:
                        if doctor_speciality.get('display'):
                            doctor_specialities.append(doctor_speciality.get('display'))
                        if doctor_speciality.get('arabic_display'):
                            doctor_specialities.append(doctor_speciality.get('arabic_display'))
                entity = {
                    "id": Node.to_global_id("Doctor", row.id),
                    "name": row.user.full_name,
                    "name_ar": row.user.full_name_ar,
                    "bio": row.bio,
                    "vendor_id": row.vendor_id,
                    "vendor_name": row.vendor.name,
                    "vendor_name_ar": row.vendor.name_ar,
                    "specialities": doctor_specialities,
                }
                entities.append(entity)
            self.save("Doctor", entities)
        except Exception as e:
            logger.exception(f"Unexpected error syncing doctors: {e}")
