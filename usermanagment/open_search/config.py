from django.apps import AppConfig
from django.core.paginator import Paginator

from usermanagment.open_search.open_search_service import OpenSearchService


class OpenSearchConfig(AppConfig):
    name = 'usermanagment.open_search'

    def ready(self):
        import usermanagment.doctor.signals  # noqa
        import usermanagment.account.signals  # noqa
        import usermanagment.vendor.signals  # noqa
        open_search_service = OpenSearchService()

        if not open_search_service.is_full_text_search_entities_index_exist():
            response = open_search_service.create_full_text_search_entities_index()
            if response:
                self.sync_data(open_search_service)
        else:
            self.sync_data(open_search_service)

    def sync_data(self, open_search_service: OpenSearchService):
        pass

    # def sync_doctors(self, open_search_service: OpenSearchService):
    #     if open_search_service.count_by_entity_type("Doctor") == 0:
    #         from usermanagment.doctor.models import Doctor
    #         doctors = Doctor.objects.all()
    #         paginator = Paginator(doctors, 100)
    #         for page in paginator.page_range:
    #             open_search_service.sync_doctors(
    #                 paginator.page(page).object_list)
