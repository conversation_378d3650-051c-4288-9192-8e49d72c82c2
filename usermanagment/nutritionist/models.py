from django.db import models

from usermanagment.core.models import AuditModel, SoftDeletionModel
from usermanagment import settings


class Nutritionist(AuditModel, SoftDeletionModel):
    health_license_number = models.CharField(max_length=255, null=True, blank=True,
                                             db_index=True)
    health_license_start_date = models.DateField(null=True, blank=True, db_index=True)
    health_license_end_date = models.DateField(null=True, blank=True, db_index=True)
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        related_name="nutritionist",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
