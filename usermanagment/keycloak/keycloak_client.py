import json
import logging
from urllib.parse import urljoin

import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from keycloak.exceptions import KeycloakError, raise_error_from_response, \
    KeycloakGetError

from usermanagment.graphql.utils.request_utils import get_current_context
from usermanagment.keycloak.CustomConnect import CustomConnect
from usermanagment.keycloak.CustomKeycloakAdminI import CustomKeycloakAdmin

logger = logging.getLogger(__name__)

KEYCLOAK_ACCOUNT_URL = "/auth/realms/{realm-name}/account"


class KeycloakAPI(object):
    instance = None
    is_admin_client_initialized = False
    is_auth_client_initialized = False

    def __new__(cls):
        if cls.instance is None:
            cls.instance = super().__new__(cls)

        return cls.instance

    def __init__(self):
        if not self.is_admin_client_initialized:
            try:
                self.keycloak_admin = CustomKeycloakAdmin(
                    server_url=urljoin(settings.KEYCLOAK_CONFIG['INTERNAL_SERVER_URL'],
                                       '/auth/'),
                    username=settings.KEYCLOAK_CONFIG['ADMIN_USERNAME'],
                    password=settings.KEYCLOAK_CONFIG['ADMIN_PASSWORD'],
                    realm_name=settings.KEYCLOAK_CONFIG['REALM'],
                    client_id=settings.KEYCLOAK_CONFIG['ADMIN_CLIENT_ID'],
                    auto_refresh_token=['get', 'put', 'post', 'delete'],
                    verify=True
                )
                self.is_admin_client_initialized = True
            except Exception as e:
                logger.error("error while creating KeycloakAdmin client", exc_info=e)

        if not self.is_auth_client_initialized:
            try:
                server_url = settings.KEYCLOAK_CONFIG['INTERNAL_SERVER_URL']
                public_server_url = settings.KEYCLOAK_CONFIG['SERVER_URL']
                if server_url:
                    server_url = server_url.strip("/")
                self.keycloak = CustomConnect(server_url=server_url)
                self.public_keycloak = CustomConnect(server_url=public_server_url)
                self.is_auth_client_initialized = True
            except Exception as e:
                logger.error("error while creating KeycloakAuthorization client",
                             exc_info=e)

    def login(self, data):
        return self.public_keycloak.get_token_from_credentials(**data)

    def create_user(self, data):
        try:
            return self.keycloak_admin.create_user(data, exist_ok=False)
        except KeycloakError as keycloak_error:
            raise ValidationError(self.__get_keycloak_error_message(keycloak_error))
        except Exception as e:
            logger.error("Error while creating user from admin", exc_info=e)
            raise ValidationError(str(e))

    def update_user(self, sso_id, data):
        try:
            return self.keycloak_admin.update_user(sso_id, data)
        except KeycloakError as keycloak_error:
            raise ValidationError(self.__get_keycloak_error_message(keycloak_error))
        except Exception as e:
            logger.error("Error while updating user from admin", exc_info=e)
            raise ValidationError(str(e))

    def delete_user(self, user_id):
        try:
            return self.keycloak_admin.delete_user(user_id)
        except KeycloakError as keycloak_error:
            raise ValidationError(self.__get_keycloak_error_message(keycloak_error))
        except Exception as e:
            logger.error("Error while deleting user from admin", exc_info=e)
            raise ValidationError(str(e))

    def set_user_password(self, user_id, new_password):
        try:
            return self.keycloak_admin.set_user_password(user_id, new_password,
                                                         temporary=False)
        except KeycloakError as keycloak_error:
            raise ValidationError(self.__get_keycloak_error_message(keycloak_error))
        except Exception as e:
            logger.error("Error while updating user password from admin", exc_info=e)
            raise ValidationError(str(e))

    def update_account(self, data):
        url = urljoin(settings.KEYCLOAK_CONFIG['INTERNAL_SERVER_URL'],
                      KEYCLOAK_ACCOUNT_URL.format(
                          **{"realm-name": settings.KEYCLOAK_CONFIG['REALM']}
                      ))

        try:
            context = get_current_context()
            response = requests.request(
                "POST",
                url,
                data=json.dumps(data),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " +
                                     context.headers.get("Authorization").split(" ")[1]
                },
            )

            return raise_error_from_response(response, KeycloakGetError,
                                             expected_codes=[204])
        except KeycloakError as keycloak_error:
            raise ValidationError(self.__get_keycloak_error_message(keycloak_error))
        except Exception as e:
            logger.error("Error while updating user account", exc_info=e)
            raise ValidationError(str(e))

    def __get_keycloak_error_message(self, keycloak_error):
        message = keycloak_error.error_message
        try:
            message_json = json.loads(keycloak_error.error_message)
            if message_json.get('error_description'):
                return message_json['error_description']
            if message_json.get('error'):
                return message_json['error']
            if message_json.get('errorMessage'):
                return message_json['errorMessage']
        except Exception:
            pass

        return message

    def get_client_role_id(self, client_id, role_name):
        return self.keycloak_admin.get_client_role_id(client_id, role_name)

    # this one get client_id not client-id
    def get_client_id(self, client_name):
        return self.keycloak_admin.get_client_id(client_name)

    def client_add_role(self, client_id, payload):
        self.keycloak_admin.create_client_role(client_id, payload=payload,
                                               skip_exists=True)

        role_id = self.keycloak_admin.get_client_role_id(client_id, payload["name"])
        return role_id

    def create_update_group(self, name, client_id, added_roles, added_users,
                            removed_roles, removed_users, is_create, group_id=None):
        if group_id is None or is_create:
            group_id = self.create_group(name)

        self.add_group_roles_users(added_roles, added_users,
                                   client_id, group_id,
                                   is_create, name)

        self.remove_group_roles_users(client_id, group_id, name,
                                      removed_roles,
                                      removed_users)

        return group_id

    def add_group_roles_users(self, added_roles, added_users, client_id, group_id,
                              is_create, name):
        try:
            if added_roles:
                self.keycloak_admin.assign_group_client_roles(group_id, client_id,
                                                              added_roles)
        except KeycloakGetError as e:
            if is_create:
                self.keycloak_admin.delete_group(group_id)
            logger.error(
                f"failed to assign role to a group :{name}, error {e.response_body}")
            raise ValidationError(
                f"Could not create group {name}, failed to assign roles")
        try:
            for user in added_users:
                self.keycloak_admin.group_user_add(user.sso_id, group_id)
        except KeycloakGetError as e:
            if is_create:
                self.keycloak_admin.delete_group(group_id)
            logger.error(
                f"failed to add users to  group :{name}, error {e.response_body}")
            raise ValidationError(f"Could not create group {name}, failed to add users")

    def remove_group_roles_users(self, client_id, group_id, name, removed_roles,
                                 removed_users):
        try:
            if removed_roles:
                self.keycloak_admin.delete_group_client_roles(group_id,
                                                              client_id,
                                                              removed_roles)
        except KeycloakGetError as e:
            logger.error(
                f"failed to remove role to a group :{name}, error {e.response_body}")
            raise ValidationError(
                f"Could not update group {name}, failed to remove roles")

        try:
            for user in removed_users:
                self.keycloak_admin.group_user_remove(user.sso_id, group_id)
        except KeycloakGetError as e:
            logger.error(
                f"failed to remove users to  group :{name}, error {e.response_body}")
            raise ValidationError(
                f"Could not update group {name}, failed to remove users")

    def create_group(self, name):
        payload = {
            "name": name,
        }
        self.keycloak_admin.create_group(payload=payload, skip_exists=True)
        group_id = self.get_group_id_by_name(name)
        return group_id

    def get_group_id_by_name(self, name):
        group_representation = self.keycloak_admin.get_group_by_name(name)
        if not group_representation:
            raise ValidationError(f"Could not create/get group {name}")
        group_id = group_representation.get("id")
        return group_id

    def delete_group(self, group_id, group_db_id):
        try:
            return self.keycloak_admin.delete_group(group_id)
        except KeycloakGetError as e:
            logger.error(
                f"could not find group to delete, group id {group_db_id}, keycloak "
                f"group id {group_id}, e {e.response_body}")
            raise ValidationError(f"Could not delete group {group_db_id}")

    def get_role_representation(self, role_name):
        client_id = self.get_client_id(settings.KEYCLOAK_CONFIG['CLIENT_ID'])
        return {
            client_id: [self.get_client_role_id(client_id, role_name)]
        }
