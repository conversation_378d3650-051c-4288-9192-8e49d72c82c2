import logging

from keycloak import <PERSON><PERSON>loakAdmin, KeycloakGetError, URL_ADMIN_REALM, \
    raise_error_from_response

logger = logging.getLogger(__name__)


class CustomKeycloakAdmin(KeycloakAdmin):

    def refresh_token(self):
        refresh_token = self.token.get('refresh_token')
        try:
            self.token = self.keycloak_openid.refresh_token(refresh_token)
        except KeycloakGetError as e:
            if e.response_code == 400 and (
                    b'Refresh token expired' in e.response_body or
                    b'Token is not active' in e.response_body or
                    b'invalid_grant' in e.response_body):
                self.get_token()
            else:
                raise
        self.connection.add_param_headers('Authorization',
                                          'Bearer ' + self.token.get('access_token'))

    def get_group_by_name(self, name):
        url = URL_ADMIN_REALM + "/group-by-path/{name}"
        params_path = {"realm-name": self.realm_name, "name": name}
        data_raw = self.raw_get(url.format(**params_path))
        try:
            return raise_error_from_response(data_raw, KeycloakGetError)
        except KeycloakGetError as e:
            logger.error(
                f"failed to get keycloak group by name :{name}, error {e.response_body}")
            return None
