import jwt
from django.core.exceptions import ValidationError
from django_keycloak.keycloak import Connect
from urllib.parse import urlparse
import requests

from django_keycloak.urls import (KEYCLOAK_GET_TOKEN)

from usermanagment import settings
from usermanagment.account.error_codes import AccountErrorCode
from usermanagment.auth.jwt_utils import jwt_decode


class CustomConnect(Connect):
    integration_token = None,

    def __init__(
            self,
            server_url=None,
            realm=None,
            client_id=None,
            client_secret_key=None,
            internal_url=None,
    ):

        super().__init__(server_url, realm, client_id, client_secret_key, internal_url)

    def get_integration_token(self):
        """
                Get Token for a user from credentials
                """
        payload = {
            "grant_type": "client_credentials",
            "client_id": settings.KEYCLOAK_CONFIG['INTEGRATION_CLIENT_ID'],
            "client_secret": settings.KEYCLOAK_CONFIG['INTEGRATION_SECRET_KEY'],
        }
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }

        server_url = self.server_url
        if self.internal_url:
            server_url = self.internal_url
            headers["HOST"] = urlparse(self.server_url).netloc

        response = requests.request(
            "POST",
            KEYCLOAK_GET_TOKEN.format(server_url, self.realm),
            data=payload,
            headers=headers,
        )
        return response.json()

    @staticmethod
    def is_valid_token(token):
        if token is None:
            return False
        try:
            jwt_decode(token)
        except (jwt.PyJWTError, Exception) as _:
            return False
        return True

    def get_integration_access_token(self):
        if self.integration_token is None or \
                not self.is_valid_token(self.integration_token):
            integration_response = self.get_integration_token()
            if not integration_response or "access_token" not in integration_response:
                raise ValidationError(
                    "Could not obtain Integration token",
                    code=AccountErrorCode.INTEGRATION_TOKEN_ERROR.value,
                )
            self.integration_token = integration_response["access_token"]
        return self.integration_token
