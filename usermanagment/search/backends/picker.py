from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.module_loading import import_module  # type: ignore


def pick_backend(search_model=None):
    """Return the currently configured storefront search function.

    Returns a callable that accepts the search phrase.
    """
    search_backend = import_module(settings.SEARCH_BACKEND)

    if search_model == "Vendor":
        return search_backend.search_vendors

    raise ValidationError(f"no search backend found for model {search_model}")
