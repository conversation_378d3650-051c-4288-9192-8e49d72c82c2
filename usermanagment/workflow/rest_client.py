import json
import logging

from django.conf import settings
from swagger_client import Api<PERSON>lient, Configuration

logger = logging.getLogger(__name__)


class WorkflowApi(object):

    def _get_config(self):
        config = Configuration()
        config.host = settings.WORKFLOW_CONFIG['host']
        return config

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient(configuration=self._get_config())
        self.api_client = api_client

    def call_api(self, path, method, data=None, headers=None, auth_settings=None,
                 request_timeout=None):
        header_params = {
            'Accept': 'application/json'
        }
        if headers:
            header_params.update(headers)

        try:
            return self.api_client.call_api(
                path,
                method,
                header_params=header_params,
                body=data,
                auth_settings=auth_settings,
                _preload_content=False,
                _request_timeout=request_timeout
            )
        except Exception as e:
            logger.error(
                f"Error while calling Workflow API => {path}\n {json.dumps(data)}",
                exc_info=e)

            return False

    def create_meeting_platform_id(self, token):
        header_params = {
            'Accept': 'application/json',
            'Authorization': f'JWT {token}',
        }

        return self.call_api(
            path="/messenger/authorize-by-token",
            method='POST',
            headers=header_params,
            request_timeout=3
        )

    def create_chat_link(self, participant_1, participant_2):
        result = self.call_api(
            path="/messenger/create-anonymous-chat",
            method='POST',
            data={
                "participants": [
                    {
                        "name": participant_1.first_name,
                        "userId": participant_1.meeting_platform_id
                    },
                    {
                        "name": participant_2.first_name,
                        "userId": participant_2.meeting_platform_id
                    }
                ]
            }
        )

        data = json.loads(result[0].data)
        if not data.get('anonymousChatLink'):
            logger.error(f"Error while creating chat link => {data}")
            raise Exception("Error while creating chat link")

        return data

    @staticmethod
    def is_success_response(response):
        if isinstance(response, tuple) and len(response) == 3 and \
                200 <= response[1] <= 299:
            return True

        return False
