import ast
import os.path
import warnings

import dj_database_url
from urllib.parse import quote_plus
import jaeger_client.config
import pem
from django.core.exceptions import ImproperlyConfigured
from django.core.management.utils import get_random_secret_key
from django_prices.utils.formatting import get_currency_fraction


def get_list(text):
    return [item.strip() for item in text.split(",")]


def get_bool_from_env(name, default_value):
    if name in os.environ:
        value = os.environ[name]
        try:
            return ast.literal_eval(value)
        except ValueError as e:
            raise ValueError("{} is an invalid value for {}".format(value, name)) from e
    return default_value


PROJECT_NAME = os.environ.get("PROJECT_NAME", "MEDGULF")
PROJECT_NAME_AR = os.environ.get("PROJECT_NAME_AR", "ميدغلف")

DEBUG = get_bool_from_env("DEBUG", True)
SYNC_KEYCLOAK = get_bool_from_env("SYNC_KEYCLOAK", False)
DB_DEBUG = get_bool_from_env("DB_DEBUG", False)

SITE_ID = 1

ASGI_APPLICATION = "usermanagment.asgi.application"

PROJECT_ROOT = os.path.normpath(os.path.join(os.path.dirname(__file__), ".."))

ROOT_URLCONF = "usermanagment.urls"

_DEFAULT_CLIENT_HOSTS = "localhost,127.0.0.1"

ANDROID_APP_LINK = os.environ.get("ANDROID_LINK", "https://bit.ly/sehatuk-android")

IOS_APP_LINK = os.environ.get("IOS_LINK", "https://bit.ly/sehatuk-ios")

FILTER_DOCTORS_BY_PATIENT_NETWORK = get_bool_from_env(
    "FILTER_DOCTORS_BY_PATIENT_NETWORK", True)

ALLOWED_CLIENT_HOSTS = os.environ.get("ALLOWED_CLIENT_HOSTS")
if not ALLOWED_CLIENT_HOSTS:
    if DEBUG:
        ALLOWED_CLIENT_HOSTS = _DEFAULT_CLIENT_HOSTS
    else:
        raise ImproperlyConfigured(
            "ALLOWED_CLIENT_HOSTS environment variable must be set when DEBUG=False."
        )

ALLOWED_CLIENT_HOSTS = get_list(ALLOWED_CLIENT_HOSTS)

INTERNAL_IPS = get_list(os.environ.get("INTERNAL_IPS", "127.0.0.1"))

DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

DB_TYPE = os.environ.get("DB_TYPE", "mysql")
DB_HOST = os.environ.get("DB_HOST", "localhost")
DB_USER = os.environ.get("DB_USER", "root")
DB_PASS = os.environ.get("DB_PASS", "root")
DB_ENV = os.environ.get("ENV", "dev")

encoded_user = quote_plus(DB_USER)
encoded_password = quote_plus(DB_PASS)

if DB_TYPE == "mysql":
    DATABASES = {
        "default": dj_database_url.config(
            default=f"mysqlgis://{encoded_user}:{encoded_password}@{DB_HOST}:3306/{DB_ENV}_user_management",
            conn_max_age=600,
            engine="django.contrib.gis.db.backends.mysql"
        )
    }

elif DB_TYPE == "mssql":
    DATABASES = {
        "default": dj_database_url.config(
            default=f"mssqlms://{encoded_user}:{encoded_password}@{DB_HOST}:1433/{DB_ENV}_user_management",
            conn_max_age=60,
            engine="usermanagment.mssql_backend",
            ssl_require=False
        )
    }

    # Add connection pooling settings for MSSQL
    DATABASES['default']['OPTIONS'] = {
        'driver': 'ODBC Driver 17 for SQL Server',
        'extra_params': 'Connection Timeout=30;Command Timeout=30;',
        # Connection pooling configuration
        'pool_size': 10,        # Maximum pool size (base pool)
        'pool_pre_ping': True,  # Validate connections before use
        'pool_recycle': 300,    # Recycle connections after 5 minutes
        'max_overflow': 5,      # Allow 5 additional connections beyond pool_size
        'pool_timeout': 30,     # Timeout when getting connection from pool
        # Custom pool settings for minimum connections
        'poolclass': 'QueuePool',
        'pool_reset_on_return': 'commit',
    }

    # Set minimum pool size via environment or custom setting
    DATABASES['default']['POOL_MIN_SIZE'] = 5  # Minimum 5 connections always available

else:
    DATABASES = {
        "default": dj_database_url.config(
            default="mysqlgis://usermanagement:usermanagement@127.0.0.1:3306/sehatuk_usermanagement",
            conn_max_age=600,
            engine="django.contrib.gis.db.backends.mysql"
        )
    }

# DATABASES = {
#     "default": dj_database_url.config(
#         default="mssql://sa:ccs123@127.0.0.1:1433/sehatuk_usermanagement",
#         conn_max_age=600,
#         engine="usermanagment.mssql_backend"
#     )
# }

OPEN_SEARCH = {
    'HOST': os.environ.get('OPEN_SEARCH_HOST', "127.0.0.1"),
    'PORT': os.environ.get('OPEN_SEARCH_PORT', "9200"),
    'USER_NAME': os.environ.get('OPEN_SEARCH_USER_NAME', "admin"),
    'PASSWORD': os.environ.get('OPEN_SEARCH_PASSWORD', "admin"),
    'USE_SSL': os.environ.get('OPEN_SEARCH_USE_SSL', True)

}

TIME_ZONE = "UTC"
LANGUAGE_CODE = "en"
LANGUAGES = [
    ("ar", "Arabic"),
    ("az", "Azerbaijani"),
    ("bg", "Bulgarian"),
    ("bn", "Bengali"),
    ("ca", "Catalan"),
    ("cs", "Czech"),
    ("da", "Danish"),
    ("de", "German"),
    ("el", "Greek"),
    ("en", "English"),
    ("es", "Spanish"),
    ("es-co", "Colombian Spanish"),
    ("et", "Estonian"),
    ("fa", "Persian"),
    ("fi", "Finnish"),
    ("fr", "French"),
    ("hi", "Hindi"),
    ("hu", "Hungarian"),
    ("hy", "Armenian"),
    ("id", "Indonesian"),
    ("is", "Icelandic"),
    ("it", "Italian"),
    ("ja", "Japanese"),
    ("ko", "Korean"),
    ("lt", "Lithuanian"),
    ("mn", "Mongolian"),
    ("nb", "Norwegian"),
    ("nl", "Dutch"),
    ("pl", "Polish"),
    ("pt", "Portuguese"),
    ("pt-br", "Brazilian Portuguese"),
    ("ro", "Romanian"),
    ("ru", "Russian"),
    ("sk", "Slovak"),
    ("sl", "Slovenian"),
    ("sq", "Albanian"),
    ("sr", "Serbian"),
    ("sv", "Swedish"),
    ("sw", "Swahili"),
    ("ta", "Tamil"),
    ("th", "Thai"),
    ("tr", "Turkish"),
    ("uk", "Ukrainian"),
    ("vi", "Vietnamese"),
    ("zh-hans", "Simplified Chinese"),
    ("zh-hant", "Traditional Chinese"),
]
LOCALE_PATHS = [os.path.join(PROJECT_ROOT, "locale")]
USE_I18N = True
USE_L10N = True
USE_TZ = True

FORM_RENDERER = "django.forms.renderers.TemplatesSetting"


ENABLE_SSL = get_bool_from_env("ENABLE_SSL", False)

if ENABLE_SSL:
    SECURE_SSL_REDIRECT = not DEBUG

context_processors = [
    "django.template.context_processors.debug",
    "django.template.context_processors.media",
    "django.template.context_processors.static",
]

loaders = [
    "django.template.loaders.filesystem.Loader",
    "django.template.loaders.app_directories.Loader",
]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(PROJECT_ROOT, "templates")],
        "OPTIONS": {
            "debug": DEBUG,
            "context_processors": context_processors,
            "loaders": loaders,
            "string_if_invalid": '<< MISSING VARIABLE "%s" >>' if DEBUG else "",
        },
    }
]

# Make this unique, and don't share it with anybody.
SECRET_KEY = os.environ.get("SECRET_KEY")

if not SECRET_KEY and DEBUG:
    warnings.warn("SECRET_KEY not configured, using a random temporary key.")
    SECRET_KEY = get_random_secret_key()

MIDDLEWARE = [
    "django_prometheus.middleware.PrometheusBeforeMiddleware",
    "usermanagment.auth.middleware.InitializeRequestDataMiddleware",
    "usermanagment.auth.middleware.JWTAuthMiddleware",
    "usermanagment.logging.http_logging_middleware.HTTPLoggingMiddleware",
    "django.middleware.security.SecurityMiddleware",
    'django.middleware.locale.LocaleMiddleware',
    "django.middleware.common.CommonMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "usermanagment.core.middleware.request_time",
    "usermanagment.core.middleware.country",
    "usermanagment.core.middleware.currency",
    "usermanagment.core.middleware.site",
    # Database connection management - should be last
    "usermanagment.core.middleware.db_connection",
]

INSTALLED_APPS = [
    # External apps that need to go before django's
    "storages",
    "health_check",
    # Django modules
    "django.contrib.contenttypes",
    "django.contrib.sites",
    "django.contrib.auth",
    "django.contrib.gis",
    # External apps
    "versatileimagefield",
    "graphene_django",
    "django_countries",
    "django_filters",
    "phonenumber_field",
    "phone_verify",
    "channels",
    "django_prometheus",
    # Local apps
    "usermanagment.account",
    "usermanagment.keycloak_permissions",
    "usermanagment.core",
    "usermanagment.graphql",
    "usermanagment.block",
    "usermanagment.vendor.config.VendorAppConfig",
    "usermanagment.subscription",
    "usermanagment.site",
    "usermanagment.chat",
    "usermanagment.patient",
    "usermanagment.doctor",
    "usermanagment.app_config.config.AuthAppConfig",
    "usermanagment.payer",
    "usermanagment.open_search.config.OpenSearchConfig",
    "usermanagment.nurse",
    "usermanagment.pharmacist",
    "usermanagment.receptionist",
    "usermanagment.manager",
    "usermanagment.rcm",
    "usermanagment.dental_hygienist",
    "usermanagment.diabetes_educator",
    "usermanagment.fitness_coach",
    "usermanagment.nutritionist",
    "usermanagment.optometrist",
    "usermanagment.podiatric_medical_assistant",
    "usermanagment.psychologist",
    "usermanagment.social_worker",
    "usermanagment.feature",
    "usermanagment.complaint",
]

use_json_logging = get_bool_from_env("USE_JSON_LOGGING", True)

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "root": {"level": "DEBUG" if DEBUG else "INFO", "handlers": ["console"]},
    "formatters": {
        "verbose": {
            "format": (
                "user-management => %(asctime)s %(levelname)s %(name)s %(message)s [PID:%(process)d:%(threadName)s]"
            )
        },
        "simple": {
            "format": "user-management => %(asctime)s %(levelname)s %(message)s"},
        "formatter_json": {
            "class": "usermanagment.logging.formatters.json_formatter.CustomJsonFormatter",
        }
    },
    "filters": {"require_debug_false": {"()": "django.utils.log.RequireDebugFalse"}},
    "handlers": {
        "mail_admins": {
            "level": "ERROR",
            "filters": ["require_debug_false"],
            "class": "django.utils.log.AdminEmailHandler",
        },
        "google_chat": {
            "level": "ERROR",
            "class": "usermanagment.logging.google_chat_handler.GoogleChatHandler",
        },
        "console": {
            "level": "DEBUG",
            "class": "usermanagment.logging.logging_handler.CustomLoggingHandler",
            "formatter": "formatter_json" if use_json_logging else "verbose",
        },
        "null": {"class": "logging.NullHandler"},
    },
    "loggers": {
        "django": {
            "handlers": ["console", "mail_admins", "google_chat"],
            "level": "INFO",
            "propagate": False,
        },
        "django.server": {"handlers": ["console"], "level": "INFO", "propagate": False},
        "usermanagment": {
            "handlers": ["console", "google_chat"],
            "level": "DEBUG" if DEBUG else "ERROR",
            "propagate": False
        },
        "usermanagment.graphql.errors.handled": {
            "handlers": ["console", "google_chat"],
            "level": "ERROR",
            "propagate": False,
        },
        # You can configure this logger to go to another file using a file handler.
        # Refer to https://docs.djangoproject.com/en/2.2/topics/logging/#examples.
        # This allow easier filtering from GraphQL query/permission errors that may
        # have been triggered by your frontend applications from the internal errors
        # that happen in backend
        "usermanagment.graphql.errors.unhandled": {
            "handlers": ["console", "google_chat"],
            "level": "ERROR",
            "propagate": False,
        },
        "graphql.execution.utils": {"handlers": ["null"], "propagate": False},
        "django.request": {
            "handlers": ["console"],
            "level": "INFO" if DEBUG else "ERROR",
            "propagate": False,
        },
        "django.db.backends": {
            "handlers": ["console"],
            "level": "DEBUG" if DB_DEBUG else "INFO",
            "propagate": False,
        },
        "django.channels.server": {
            "handlers": ["console"],
            "level": "INFO" if DEBUG else "ERROR",
            "propagate": False,
        },
        "daphne": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
    },
}

AUTH_USER_MODEL = "account.User"

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {"min_length": 8},
    }
]

DEFAULT_COUNTRY = os.environ.get("DEFAULT_COUNTRY", "SA")
DEFAULT_CURRENCY = os.environ.get("DEFAULT_CURRENCY", "SAR")
DEFAULT_DECIMAL_PLACES = get_currency_fraction(DEFAULT_CURRENCY)
DEFAULT_MAX_DIGITS = 12
DEFAULT_CURRENCY_CODE_LENGTH = 3

# The default max length for the display name of the
# sender email address.
# Following the recommendation of https://tools.ietf.org/html/rfc5322#section-2.1.1
DEFAULT_MAX_EMAIL_DISPLAY_NAME_LENGTH = 78

# note: having multiple currencies is not supported yet
AVAILABLE_CURRENCIES = [DEFAULT_CURRENCY]

COUNTRIES_OVERRIDE = {"EU": "European Union"}

PLAYGROUND_ENABLED = get_bool_from_env("PLAYGROUND_ENABLED", True)

ALLOWED_HOSTS = get_list(os.environ.get("ALLOWED_HOSTS", "*"))

ALLOWED_GRAPHQL_ORIGINS = os.environ.get("ALLOWED_GRAPHQL_ORIGINS", "*")

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

MEDIA_ROOT = os.path.join(PROJECT_ROOT, "media")
MEDIA_URL = os.environ.get("MEDIA_URL", "/media/")

VERSATILEIMAGEFIELD_RENDITION_KEY_SETS = {
    "user-avatars": [("default",)],
    "vendors-logos": [("default",)],
    "vendors-backgrounds": [("default",)],
    "vendor-images": [("default",)],
    "patient-insurance-cards": [("default",)],
}

VERSATILEIMAGEFIELD_SETTINGS = {
    # Images should be pre-generated on Production environment
    "create_images_on_demand": get_bool_from_env("CREATE_IMAGES_ON_DEMAND", DEBUG),
}

SEARCH_BACKEND = "usermanagment.search.backends.mysql"

AUTHENTICATION_BACKENDS = [
    "usermanagment.auth.backends.AuthenticationBackend",
]

# Change this value if your application is running behind a proxy,
# e.g. HTTP_CF_Connecting_IP for Cloudflare or X_FORWARDED_FOR
REAL_IP_ENVIRON = os.environ.get("REAL_IP_ENVIRON", "REMOTE_ADDR")

# The maximum length of a graphql query to log in tracings
OPENTRACING_MAX_QUERY_LENGTH_LOG = 2000

GRAPHENE = {
    "RELAY_CONNECTION_ENFORCE_FIRST_OR_LAST": True,
    "RELAY_CONNECTION_MAX_LIMIT": 100,
    "MIDDLEWARE": [
        "usermanagment.graphql.middleware.OpentracingGrapheneMiddleware",
    ],
}

# Initialize a simple and basic Jaeger Tracing integration
# for open-tracing if enabled.
#
# If running locally, set:
#   JAEGER_AGENT_HOST=localhost
if "JAEGER_AGENT_HOST" in os.environ:
    jaeger_client.Config(
        config={
            "sampler": {"type": "const", "param": 1},
            "local_agent": {
                "reporting_port": os.environ.get(
                    "JAEGER_AGENT_PORT", jaeger_client.config.DEFAULT_REPORTING_PORT
                ),
                "reporting_host": os.environ.get("JAEGER_AGENT_HOST"),
            },
            "logging": get_bool_from_env("JAEGER_LOGGING", False),
        },
        service_name="user-management",
        validate=True,
    ).initialize_tracer()

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    },
}

GOOGLE_APP_KEY = os.environ.get('GOOGLE_APP_KEY')

DATA_UPLOAD_MAX_MEMORY_SIZE = 2621440  # 2.5MB

PASSWORD_SET_LINK = os.environ.get('PASSWORD_SET_LINK', "https://bit.ly/pwd-wp")

EMAIL_PHONE_VERIFICATION = {
    "BACKEND": "usermanagment.account.otp_phone_verify.PhoneVerifyOTPBackend",
    "OPTIONS": {},
    "TOKEN_LENGTH": 4,
    "LIMIT_PER_MINUTE": os.environ.get("EMAIL_PHONE_VERIFICATION_LIMIT_PER_MINUTE",3),
    "APP_NAME": "Sehatuk",
    "SECURITY_CODE_EXPIRATION_TIME": int(
        os.environ.get("OTP_CODE_EXPIRATION_TIME", 300)),  # In seconds only
    "VERIFY_SECURITY_CODE_ONLY_ONCE": True,
    # If False, then a security code can be used multiple times for verification
}


KEYCLOAK_CONFIG = {
    'SERVER_URL': os.environ.get("KEYCLOAK_SERVER_URL", "localhost"),
    'INTERNAL_SERVER_URL': os.environ.get("KEYCLOAK_INTERNAL_SERVER_URL", "localhost"),
    'REALM': os.environ.get("KEYCLOAK_REALM", "localhost"),
    'CLIENT_ID': os.environ.get("KEYCLOAK_CLIENT_ID", "localhost"),
    'CLIENT_SECRET_KEY': os.environ.get("KEYCLOAK_CLIENT_SECRET_KEY", "localhost"),
    'EXEMPT_URIS': [],
    'GRAPHQL_ENDPOINT': 'graphql/',
    'ADMIN_CLIENT_ID': os.environ.get("KEYCLOAK_ADMIN_CLIENT_ID"),
    'ADMIN_USERNAME': os.environ.get("KEYCLOAK_ADMIN_USERNAME"),
    'ADMIN_PASSWORD': os.environ.get("KEYCLOAK_ADMIN_PASSWORD"),
    'INTEGRATION_CLIENT_ID': os.environ.get("KEYCLOAK_INTEGRATION_CLIENT_ID"),
    'INTEGRATION_SECRET_KEY': os.environ.get("KEYCLOAK_INTEGRATION_SECRET_KEY")
}

LOAD_CUSTOM_SETTINGS = get_bool_from_env("LOAD_CUSTOM_SETTINGS", False)

FEDERATED_BACKEND = {
    "URL": os.environ.get("FEDERATED_BACKEND_URL")
}

TEST_RUNNER = "usermanagment.tests.runner.PytestTestRunner"

TOKEN_SIG_PRIVATE_KEY_PATH = os.environ.get('TOKEN_SIG_PRIVATE_KEY_PATH', None)

KAFKA = {
    'BOOTSTRAP_SERVERS': os.environ.get('KAFKA_BOOTSTRAP_SERVERS'),
}

GOOGLE_CHAT_SPACE_URL = os.environ.get("GOOGLE_CHAT_SPACE_URL")
HIDE_IS_EXPIRED_EXCEPTION_GOOGLE = get_bool_from_env("HIDE_IS_EXPIRED_EXCEPTION", False)
HIDE_TOKEN_GOOGLE = get_bool_from_env("HIDE_TOKEN_GOOGLE", True)

REDIS_URL = os.environ.get("REDIS_URL")

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [{"address": REDIS_URL, "db": 2}],
        },

    }
}
key = pem.parse_file(TOKEN_SIG_PRIVATE_KEY_PATH) if TOKEN_SIG_PRIVATE_KEY_PATH else ""

WORKFLOW_CONFIG = {
    'host': os.environ.get("WORKFLOW_API_HOST")
}

RECAPTCHA_ENABLED = get_bool_from_env("RECAPTCHA_ENABLED", False)
RECAPTCHA_URL = os.environ.get("RECAPTCHA_URL")
RECAPTCHA_PUBLIC_KEY = os.environ.get("RECAPTCHA_PUBLIC_KEY")

# Database connection management settings
DATABASE_CONNECTION_POOLING = True
CONN_HEALTH_CHECKS = True

# Close database connections after each request to prevent connection buildup
# This is especially important with threading
DATABASES['default']['CONN_HEALTH_CHECKS'] = True
RECAPTCHA_PRIVATE_KEY = os.environ.get("RECAPTCHA_PRIVATE_KEY")
COMPLAINTS_RECEIVER_EMAIL = os.environ.get("COMPLAINTS_RECEIVER_EMAIL")

PAYER_INTEGRATION_API_URL = os.environ.get("PAYER_INTEGRATION_API_URL",
                                           "https://api.sk-dev.sehacity.com/payer-integration-api")
