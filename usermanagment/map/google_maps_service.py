import logging

import googlemaps

from usermanagment import settings

logger = logging.getLogger(__name__)


class GoogleMapService:

    def __init__(self):
        try:
            self.__client = googlemaps.Client(key=settings.GOOGLE_APP_KEY)
        except Exception as e:
            logger.error("error while initializing GoogleMapService client", exc_info=e)

    def from_address_to_long_lat(self, address):
        # Geocoding an address
        logger.debug("calling google Geocoding api")
        try:
            human_readable_address = GoogleMapService.normalize_address_object(address)
            if human_readable_address:
                geocode_result = self.__client.geocode(human_readable_address)

                if geocode_result and geocode_result[0].get('geometry') \
                        and geocode_result[0]['geometry'].get('location'):
                    return geocode_result[0]['geometry']['location']
        except Exception as error:
            logger.error("error while calling google gecode API", exc_info=error)
        return None

    @staticmethod
    def normalize_address_object(address):
        if address:
            return ', '.join(filter(None,
                                    [address.street_address_1, address.street_address_2,
                                     address.city.name,
                                     address.postal_code])
                             )
        return ''

    def from_long_lat_to_address(self, lng, lat):
        # Reverse Geocoding location
        try:
            reverse_geocode_result = self.__client.reverse_geocode(
                {'lng': lng, 'lat': lat},
                extra_params={'language': 'en'}
            )

            if reverse_geocode_result:
                administrative_area_level_1 = None
                for result in reverse_geocode_result:
                    address_components = result.get('address_components')
                    for address_component in address_components:
                        types = address_component['types']
                        if 'locality' in types:
                            return address_component['long_name']
                        if 'administrative_area_level_1' in types:
                            administrative_area_level_1 = address_component['long_name']

                return administrative_area_level_1
        except Exception as error:
            logger.error("error while calling google reverse gecode API", exc_info=error)
        return None

    def calculate_distance_matrix(self, origins, destinations, mode):
        return self.__client.distance_matrix(
            origins=origins,
            destinations=destinations,
            mode=mode
        )
