from django.contrib.gis.db.models.functions import (
    GeoFunc,
    Distance as GISDistance
)
from django.db import models


class IsEmpty(GeoFunc):
    arity = 1
    output_field = models.BooleanField()


class DistanceSphere(GISDistance):
    def as_mysql(self, compiler, connection, **extra_context):
        extra_context['function'] = connection.ops.spatial_function_name(
            'Distance_Sphere')
        return super().as_sql(compiler, connection, **extra_context)
    def as_microsoft(self, compiler, connection, **extra_context):
        self.function = 'STDistance'
        geo_col = self.source_expressions[0].as_sql(compiler, connection)[0]
        geometry = self.source_expressions[1].as_sql(compiler, connection)[0]
        return  f"{geo_col}.{self.function}({geometry})",[]
