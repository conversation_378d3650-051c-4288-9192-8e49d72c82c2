# Generated by Django 3.2.25 on 2025-01-29 08:25

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FeatureRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True,
                                           serialize=False, verbose_name='ID')),
                ('feature', models.CharField(choices=[('Call', 'Call')], db_index=True,
                                             max_length=20)),
                ('rating', models.PositiveSmallIntegerField(
                    validators=[django.core.validators.MinValueValidator(1),
                                django.core.validators.MaxValueValidator(5)])),
                ('entity_id', models.BigIntegerField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                           to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('feature', 'user', 'entity_id')},
            },
        ),
    ]
