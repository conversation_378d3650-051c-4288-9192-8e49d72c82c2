# Generated by Django 3.2.25 on 2025-06-01 08:24

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feature', '0003_alter_featurerating_feature'),
    ]

    operations = [
        migrations.AlterField(
            model_name='featurerating',
            name='feature',
            field=models.CharField(choices=[('Call', 'Call'), ('Call Doctor Now', 'Call Doctor Now'), ('Appointment', 'Appointment'), ('My Care Program', 'My Care Program'), ('Order Medications', 'Order Medications'), ('Knowledge Hub', 'Knowledge Hub'), ('Watch', 'Watch'), ('Cash Claim', 'Cash Claim'), ('Home Vaccination', 'Home Vaccination')], db_index=True, max_length=20),
        ),
        migrations.AlterField(
            model_name='featurerating',
            name='rating',
            field=models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(7)]),
        ),
    ]
