from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models

from usermanagment import settings
from usermanagment.feature.enums import FeatureType


class FeatureRating(models.Model):
    feature = models.CharField(max_length=20, choices=FeatureType.choices,
                               db_index=True, null=False, blank=False)
    rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(7)]
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        blank=False,
        null=False,
        on_delete=models.CASCADE, )
    entity_id = models.BigIntegerField(null=True, blank=True)

    class Meta:
        unique_together = ["feature", "user", "entity_id"]
