from mssql.operations import DatabaseOperations as MSSQLDatabaseOperations
from typing import Any
from .adapter import MSSQLAdapter
class DatabaseOperations(MSSQLDatabaseOperations):
    Adapter = MSSQLAdapter
    select ='%s '
    def geo_db_type(self, f):
        if f.geom_type == 'POINT':
            return 'geometry'
        elif f.geom_type == 'LINESTRING':
            return 'geometry'
        elif f.geom_type == 'POLYGON':
            return 'geometry'
        elif f.geom_type == 'MULTIPOINT':
            return 'geometry'
        elif f.geom_type == 'MULTILINESTRING':
            return 'geometry'
        elif f.geom_type == 'MULTIPOLYGON':
            return 'geometry'
        elif f.geom_type == 'GEOMETRYCOLLECTION':
            return 'geometry'
        else:
            raise NotImplementedError(f'Unsupported geometry type: {f.geom_type}')

    def spatial_function_name(self, func_name: Any):
        # Map the function name to the corresponding MSSQL spatial function
        function_mappings = {
            'Distance_Sphere': 'STDistance',
            'LengthSpheroid': 'STLength',
            'Perimeter': 'STPerimeter',
            # Add more mappings as needed
        }
        return function_mappings.get(func_name, func_name)

    def get_geom_placeholder(self, f, value, compiler):
        """
        Returns the placeholder for the geometry column.
        """
        return 'geometry::Point({},{})'.format(value, f.srid)


    def last_executed_query(self, cursor, sql, params):
        """
        Returns a string of the query last executed by the given cursor, with
        placeholders replaced with actual values.

        `sql` is the raw query containing placeholders, and `params` is the
        sequence of parameters. These are used by default, but this method
        exists for database backends to provide a better implementation
        according to their own quoting schemes.
        """
        if params:
            params = [param for param in params if not isinstance(param, MSSQLAdapter)]
            if isinstance(params, list):
                params = tuple(params)
            return sql % params
        # Just return sql when there are no parameters.
        else:
            return sql
