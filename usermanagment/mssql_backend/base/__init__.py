from mssql.base import DatabaseWrapper as MSSQLDatabaseWrapper

from usermanagment.mssql_backend.base.adapter import MSSQLAdapter
from usermanagment.mssql_backend.base.curosr import CustomCursorWrapper
from usermanagment.mssql_backend.base.operations import DatabaseOperations


class DatabaseWrapper(MSSQLDatabaseWrapper):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ops = DatabaseOperations(self)

        # Initialize connection pool settings
        self._setup_connection_pool()

    def _setup_connection_pool(self):
        """Setup connection pooling parameters"""
        pool_settings = self.settings_dict.get('OPTIONS', {})

        # Set default pool parameters if not specified
        self.pool_size = pool_settings.get('pool_size', 10)
        self.max_overflow = pool_settings.get('max_overflow', 5)
        self.pool_min_size = self.settings_dict.get('POOL_MIN_SIZE', 5)
        self.pool_recycle = pool_settings.get('pool_recycle', 300)
        self.pool_pre_ping = pool_settings.get('pool_pre_ping', True)

    def create_cursor(self, name=None):
        return CustomCursorWrapper(self.connection.cursor(), self)
