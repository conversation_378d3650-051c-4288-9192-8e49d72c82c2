from django.contrib.gis.geos import Point
from mssql.base import CursorWrapper
from django.core.exceptions import ImproperlyConfigured
import struct

try:
    import pyodbc as Database
except ImportError as e:
    raise ImproperlyConfigured("Error loading pyodbc module: %s" % e)



class CustomCursorWrapper(CursorWrapper):
    def handle_geometry(self, value):
        if value and len(value) == 22:
            longitude, latitude = struct.unpack('<dd', value[6:22])
            return Point(longitude, latitude)
        return None

    def __init__(self, cursor, connection):
        cursor.connection.add_output_converter(-151, self.handle_geometry)
        super().__init__(cursor, connection)

    def execute(self, sql, params=None):
        from usermanagment.mssql_backend.base import MSSQLAdapter
        if params:
            params = [param for param in params if not isinstance(param, MSSQLAdapter)]
        if isinstance(params, list):
            params = tuple(params)
        return super().execute(sql, params)
