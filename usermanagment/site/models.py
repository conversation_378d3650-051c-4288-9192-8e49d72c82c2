from django.contrib.sites.models import Site
from django.db import models

from .patch_sites import patch_contrib_sites
from ..vendor.enums import VendorTypes

patch_contrib_sites()


def get_pharmacies_types_default():
    return [VendorTypes.PHARMACY]


class SiteSettings(models.Model):
    site = models.OneToOneField(Site, related_name="settings", on_delete=models.CASCADE)

    range_expansion_max_number_of_rounds = models.IntegerField(null=True, blank=True,
                                                               default=3)

    range_expansion_max_number_of_tries = models.IntegerField(null=False, blank=False,
                                                              default=10)

    # in minutes
    range_expansion_time_out_period = models.IntegerField(null=False, blank=False,
                                                          default=15)

    # in Kilometers
    range_expansion_round_radius = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=False, blank=False,
        default=2
    )

    range_expansion_round_max_number_of_pharmacies = models.IntegerField(null=False,
                                                                         blank=False,
                                                                         default=5)

    range_expansion_round_pharmacies_types = models.JSONField(
        null=True, blank=True,
        default=get_pharmacies_types_default
    )

    microservice_context_path = models.CharField(max_length=1024,
                                                 default="usermanagement")

    terms_and_conditions_version = models.FloatField(null=True, blank=True,
                                                            default=1.0)

    registration_sms_message = models.CharField(max_length=1024,
        null=True,
        blank=True,
                                                default="""Welcome to {project_name}! We are thrilled to have you as a member.
To finish signing up, enter this verification code: {security_code}
Best regards,
{project_name}"""
    )

    registration_sms_message_ar = models.CharField(max_length=1024,
        null=True,
        blank=True,
        default="""
مرحبًا بك في {project_name}! نحن متحمسون لاستضافتك كعضو معنا.
لإكمال عملية التسجيل، يرجى إدخال رمز التحقق هذا: {security_code}
مع أطيب التحيات،
فريق  {project_name}
"""
    )

    registration_email_message = models.TextField(
        null=True,
        blank=True,
        default= """
Welcome to {project_name}! We are thrilled to have you as a member of our healthcare community. Your journey to better health begins now, and we're here to support your journey every step of the way.

To finish signing up, enter this verification code: {security_code}

If you have any questions or need assistance, feel free to reach out to our support team at [<EMAIL>].

Thank you for choosing {project_name}.

Best regards,
{project_name}
"""
    )

    registration_email_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""
مرحبًا بك في {project_name}! نحن متحمسون لانضمامك إلى مجتمعنا الصحي. رحلتك نحو صحة أفضل تبدأ الآن، ونحن هنا لدعمك في كل خطوة على الطريق.

لإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}

إذا كان لديك أي أسئلة أو تحتاج إلى المساعدة، لا تتردد في التواصل مع فريق الدعم لدينا على [<EMAIL>].

شكرًا لاختيارك {project_name}.

أطيب التحيات،
{project_name}
"""
    )

    simple_otp_sms_message = models.TextField(
        null=True,
        blank=True,
        default="""Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""
    )

    simple_otp_sms_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""
    )

    simple_otp_email_message = models.TextField(
        null=True,
        blank=True,
        default="""Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""
    )

    simple_otp_email_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""
    )

    otp_login_sms_message = models.TextField(
        null=True,
        blank=True,
        default="""Your One-Time Password (OTP) for this login is: {security_code}, Please do not share this OTP.
    Best regards,
    {project_name} Mobile App Team"""
    )

    otp_login_sms_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code}, لا تقم بمشاركة الرمز.
    مع أطيب التحيات،
    {project_name_ar} فريق تطبيق"""
    )

    otp_login_email_message = models.TextField(
        null=True,
        blank=True,
        default="""Your One-Time Password (OTP) for this login: {security_code}, Please do not share this OTP.
    Best regards,
    {project_name} Mobile App Team"""
    )

    otp_login_email_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code}, لا تقم بمشاركة الرمز.
    مع أطيب التحيات،
    {project_name_ar} فريق تطبيق"""
    )

    change_email_mobile_sms_message = models.TextField(
        null=True,
        blank=True,
        default="""Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

    )

    change_email_mobile_sms_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""
    )

    change_email_mobile_email_message = models.TextField(
        null=True,
        blank=True,
        default="""Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""
    )

    change_email_mobile_email_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""
    )

    password_reset_sms_message = models.CharField(max_length=1024,
        null=True,
        blank=True,
        default= """Dear {patient_name},

A request has been initiated to change the password associated with your {project_name} Mobile App account.
Your One-Time Password (OTP) for this confirmation is: {{security_code}}. Please do not share this OTP.
Best regards,
{project_name} Mobile App team
"""
    )

    password_reset_sms_message_ar = models.CharField(max_length=1024,
        null=True,
        blank=True,
        default="""عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في تطبيق {project_name_ar}.
رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}. لا تقم بمشاركة الرمز
مع أطيب التحيات،
{project_name_ar} فريق تطبيق
"""
    )

    password_reset_email_message = models.TextField(
        null=True,
        blank=True,
        default="""Dear {patient_name},

A request has been initiated to change the password associated with your {project_name} Mobile App account. To ensure the security of your account, we require you to confirm this change.

Your One-Time Password (OTP) for this confirmation is: {{security_code}}. Our team will not ask for your credentials. Please do not share this OTP.

Please enter this OTP on the password change page within the {project_name} Mobile App to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).

Best regards,
{project_name} Mobile App Team
"""
    )

    password_reset_email_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في تطبيق {project_name_ar}. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.

رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}. لا تقم بمشاركة الرمز

يرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق {project_name_ar} لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).

مع أطيب التحيات،
{project_name_ar} فريق تطبيق
"""
    )

    patient_order_otp_sms_message = models.CharField(max_length=1024,
        null=True,
        blank=True,
        default="""Hello {patient_name}٫
You can receive your order number {order_id}
from {branch_name} pharmacy
Your verification code is: {order_otp}
For more information please visit :{url}"""
    )

    patient_order_otp_sms_message_ar = models.CharField(max_length=1024,
        null=True,
        blank=True,
        default="""مرحباً {patient_name}٫
يمكنك إستلام طلبك رقم {order_id}
من صيدلية {branch_name}
رمز التحقق الخاص بك هو : {order_otp}
لمزيد من المعلومات يرجى زيارة :{url}"""
    )


    patient_order_otp_email_message = models.TextField(
        null=True,
        blank=True,
        default="""Hello {patient_name}٫
You can receive your order number {order_id}
from {branch_name} pharmacy
Your verification code is: {order_otp}
For more information please visit :{url}"""
    )

    patient_order_otp_email_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""مرحباً {patient_name}٫
يمكنك إستلام طلبك رقم {order_id}
من صيدلية {branch_name}
رمز التحقق الخاص بك هو : {order_otp}
لمزيد من المعلومات يرجى زيارة :{url}"""
    )

    patient_create_email_message = models.TextField(
            null=True,
            blank=True,
            default="""Hi٫
            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""
       )

    patient_create_email_message_ar = models.TextField(
            null=True,
            blank=True,
            default="""Hi٫
            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""
    )

    patient_create_sms_message = models.TextField(
        null=True,
        blank=True,
        default="""Hi٫
            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""
    )

    patient_create_sms_message_ar = models.TextField(
        null=True,
        blank=True,
        default="""Hi٫
            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""
    )

    delete_account_sms_message = models.CharField(max_length=1024,
                                                  null=True,
                                                  blank=True,
                                                  default= """
A request has been initiated to delete your account.
Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team
"""
    )

    delete_account_sms_message_ar = models.CharField(max_length=1024,
                                                  null=True,
                                                  blank=True,
                                                  default=
                                                     """لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}، لا تقم بمشاركة الرمز.
                                                      مع أطيب التحيات،
                                                       {project_name_ar}فريق تطبيق """)


    delete_account_email_message = models.CharField(max_length=1024,
                                                  null=True,
                                                  blank=True,
                                                  default= """
A request has been initiated to delete your account.
Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team
"""
    )

    delete_account_email_message_ar = models.CharField(max_length=1024,
                                                     null=True,
                                                     blank=True,
                                                       default=
                                                       """لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}، لا تقم بمشاركة الرمز.
                                                        مع أطيب التحيات،
                                                         {project_name_ar}فريق تطبيق """)
