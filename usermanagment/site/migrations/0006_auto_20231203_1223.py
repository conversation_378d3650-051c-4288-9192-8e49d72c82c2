# Generated by Django 3.2.12 on 2023-12-03 10:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('site', '0005_auto_20231016_1025'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='sitesettings',
            name='password_reset_message',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='password_reset_message_ar',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='patient_order_otp_message',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='patient_registration_message',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='registration_message',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='registration_message_ar',
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='password_reset_email_message',
            field=models.TextField(blank=True, default='Dear {patient_name},\n\nA request has been initiated to change the password associated with your iO Health account. To ensure the security of your account, we require you to confirm this change.\n\nYour One-Time Password (OTP) for this confirmation is: {{security_code}}\n\nPlease enter this OTP on the password change page within the iO Health app to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).\n\nBest regards,\niO Health\n', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='password_reset_email_message_ar',
            field=models.TextField(blank=True, default='عزيزي/عزيزتي {patient_name},\n\nلقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في iO Health. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.\n\nرمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}\n\nيرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق iO Health لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).\n\nمع أطيب التحيات،\niO Health\n', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='password_reset_sms_message',
            field=models.CharField(blank=True, default='Dear {patient_name},\n\nA request has been initiated to change the password associated with your iO Health account.\nYour One-Time Password (OTP) for this confirmation is: {{security_code}}\nBest regards,\niO Health\n', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='password_reset_sms_message_ar',
            field=models.CharField(blank=True, default='عزيزي/عزيزتي {patient_name},\n\nلقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في iO Health.\nرمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}\nمع أطيب التحيات،\niO Health\n', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_order_otp_email_message',
            field=models.TextField(blank=True, default='Hello {patient_name}٫\nYou can receive your order number {order_id}\nfrom {branch_name} pharmacy\nYour verification code is: {order_otp}\nFor more information please visit :{url}', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_order_otp_email_message_ar',
            field=models.TextField(blank=True, default='مرحباً {patient_name}٫\nيمكنك إستلام طلبك رقم {order_id}\nمن صيدلية {branch_name}\nرمز التحقق الخاص بك هو : {order_otp}\nلمزيد من المعلومات يرجى زيارة :{url}', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_order_otp_sms_message',
            field=models.CharField(blank=True, default='Hello {patient_name}٫\nYou can receive your order number {order_id}\nfrom {branch_name} pharmacy\nYour verification code is: {order_otp}\nFor more information please visit :{url}', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_order_otp_sms_message_ar',
            field=models.CharField(blank=True, default='مرحباً {patient_name}٫\nيمكنك إستلام طلبك رقم {order_id}\nمن صيدلية {branch_name}\nرمز التحقق الخاص بك هو : {order_otp}\nلمزيد من المعلومات يرجى زيارة :{url}', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='registration_email_message',
            field=models.TextField(blank=True, default="\nWelcome to iO Health! We are thrilled to have you as a member of our healthcare community. Your journey to better health begins now, and we're here to support your journey every step of the way.\n\nTo finish signing up, enter this verification code: {security_code}\n\nIf you have any questions or need assistance, feel free to reach out to our support team at [<EMAIL>].\n\nThank you for choosing iO Health.\n\nBest regards,\niO Health\n", null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='registration_email_message_ar',
            field=models.TextField(blank=True, default='\nمرحبًا بك في iO Health! نحن متحمسون لانضمامك إلى مجتمعنا الصحي. رحلتك نحو صحة أفضل تبدأ الآن، ونحن هنا لدعمك في كل خطوة على الطريق.\n\nلإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}\n\nإذا كان لديك أي أسئلة أو تحتاج إلى المساعدة، لا تتردد في التواصل مع فريق الدعم لدينا على [<EMAIL>].\n\nشكرًا لاختيارك iO Health.\n\nأطيب التحيات،\niO Health\n', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='registration_sms_message',
            field=models.CharField(blank=True, default='Welcome to iO Health! We are thrilled to have you as a member.\nTo finish signing up, enter this verification code: {security_code}\nBest regards,\niO Health', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='registration_sms_message_ar',
            field=models.CharField(blank=True, default='\nمرحبًا بك في iO Health! نحن متحمسون لاستضافتك كعضو معنا.\nلإكمال عملية التسجيل، يرجى إدخال رمز التحقق هذا: {security_code}\nمع أطيب التحيات،\nفريق  iO Health\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='microservice_context_path',
            field=models.CharField(default='usermanagement', max_length=1024),
        ),
    ]
