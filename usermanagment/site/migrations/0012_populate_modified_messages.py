from django.db import migrations

registration_sms_message = """Welcome to {project_name}! We are thrilled to have you as a member.
To finish signing up, enter this verification code: {security_code}
Best regards,
{project_name}"""

registration_sms_message_ar = """
مرحبًا بك في {project_name}! نحن متحمسون لاستضافتك كعضو معنا.
لإكمال عملية التسجيل، يرجى إدخال رمز التحقق هذا: {security_code}
مع أطيب التحيات،
فريق  {project_name}
"""

registration_email_message = """
Welcome to {project_name}! We are thrilled to have you as a member of our healthcare community. Your journey to better health begins now, and we're here to support your journey every step of the way.

To finish signing up, enter this verification code: {security_code}

If you have any questions or need assistance, feel free to reach out to our support team at [<EMAIL>].

Thank you for choosing {project_name}.

Best regards,
{project_name}
"""

registration_email_message_ar = """
مرحبًا بك في {project_name}! نحن متحمسون لانضمامك إلى مجتمعنا الصحي. رحلتك نحو صحة أفضل تبدأ الآن، ونحن هنا لدعمك في كل خطوة على الطريق.

لإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}

إذا كان لديك أي أسئلة أو تحتاج إلى المساعدة، لا تتردد في التواصل مع فريق الدعم لدينا على [<EMAIL>].

شكرًا لاختيارك {project_name}.

أطيب التحيات،
{project_name}
"""

password_reset_sms_message = """Dear {patient_name},

A request has been initiated to change the password associated with your {project_name} account.
Your One-Time Password (OTP) for this confirmation is: {{security_code}}
Best regards,
{project_name}
"""

password_reset_sms_message_ar = """عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في {project_name}.
رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}
مع أطيب التحيات،
{project_name}
"""

password_reset_email_message = """Dear {patient_name},

A request has been initiated to change the password associated with your {project_name} account. To ensure the security of your account, we require you to confirm this change.

Your One-Time Password (OTP) for this confirmation is: {{security_code}}

Please enter this OTP on the password change page within the {project_name} app to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).

Best regards,
{project_name}
"""

password_reset_email_message_ar = """عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في {project_name}. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.

رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}

يرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق {project_name} لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).

مع أطيب التحيات،
{project_name}
"""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        registration_sms_message=registration_sms_message,
        registration_sms_message_ar=registration_sms_message_ar,
        registration_email_message=registration_email_message,
        registration_email_message_ar=registration_email_message_ar,
        password_reset_sms_message=password_reset_sms_message,
        password_reset_sms_message_ar=password_reset_sms_message_ar,
        password_reset_email_message=password_reset_email_message,
        password_reset_email_message_ar=password_reset_email_message_ar
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0011_populate_patient_create_messages'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
