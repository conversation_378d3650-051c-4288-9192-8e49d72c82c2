from django.db import migrations

simple_otp_sms_message = """Your One-Time Password (OTP) for this confirmation is: {security_code},
Best regards,
{project_name}"""

simple_otp_sms_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code},
مع أطيب التحيات،
{project_name}"""

simple_otp_email_message = """Your One-Time Password (OTP) for this confirmation is: {security_code},
Best regards,
{project_name}"""

simple_otp_email_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code},
مع أطيب التحيات،
{project_name}"""

change_email_mobile_sms_message = """Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code},
Best regards,
{project_name}"""

change_email_mobile_sms_message_ar = """رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code},
مع أطيب التحيات،
{project_name}"""

change_email_mobile_email_message = """Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code},
Best regards,
{project_name}"""

change_email_mobile_email_message_ar = """رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code},
مع أطيب التحيات،
{project_name}"""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        simple_otp_sms_message=simple_otp_sms_message,
        simple_otp_sms_message_ar=simple_otp_sms_message_ar,
        simple_otp_email_message=simple_otp_email_message,
        simple_otp_email_message_ar=simple_otp_email_message_ar,
        change_email_mobile_sms_message=change_email_mobile_sms_message,
        change_email_mobile_sms_message_ar=change_email_mobile_sms_message_ar,
        change_email_mobile_email_message=change_email_mobile_email_message,
        change_email_mobile_email_message_ar=change_email_mobile_email_message_ar
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0017_auto_20241223_1433'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
