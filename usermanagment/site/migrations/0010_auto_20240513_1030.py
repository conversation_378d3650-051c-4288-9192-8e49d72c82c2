# Generated by Django 3.2.25 on 2024-05-13 10:30

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0009_auto_20231212_0940'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_email_message',
            field=models.TextField(blank=True,
                                   default="Hello {patient_name}٫\n            Welcome! You're now a member of our app. Download the app now to view your profile and be on the way to your journey.",
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_email_message_ar',
            field=models.TextField(blank=True,
                                   default='مرحباً {patient_name}٫\n            انت الآن عضو في تطبيقنا! حمل تطبيقنا لرؤية ملفك وبدئ رحلتك.',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_sms_message',
            field=models.TextField(blank=True,
                                   default="Hello {patient_name}٫\n            Welcome! You're now a member of our app. Download the app now to view your profile and be on the way to your journey.",
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_sms_message_ar',
            field=models.TextField(blank=True,
                                   default='مرحباً {patient_name}٫\n            انت الآن عضو في تطبيقنا! حمل تطبيقنا لرؤية ملفك وبدئ رحلتك.',
                                   null=True),
        ),
    ]
