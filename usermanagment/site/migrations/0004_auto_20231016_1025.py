# Generated by Django 3.2.12 on 2023-10-16 10:25

from django.db import migrations

patient_registration_message = """مرحبا بكم في صحتك!
    الرجاء استخدام هذا الرابط لتعيين كلمة المرور {password_set_link} """

registration_message = """Your Sehatti code

Welcome to Sehatti!,
To finish signing up, enter this verification code: {security_code}
If you didn’t make this request please ignore this email."""

registration_message_ar = """رمز التحقق الخاص بك في صحتي

مرحبا بكم في صحتي!
لإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}
إذا لم تقدم هذا الطلب الرجاء تجاهل هذه الرسالة."""

password_reset_message = """Your Sehatti code

Hi {patient_name},
To reset your password please enter this verification code: {{security_code}}.
If you didn’t make this request please ignore this email."""

password_reset_message_ar = """رمز التحقق الخاص بك في صحتي

مرحباً {patient_name}،
لإعادة تعيين كلمة المرور الخاصة بك في صحتي ، أدخل رمز التحقق هذا: {{security_code}}
إذا لم تقدم هذا الطلب الرجاء تجاهل هذه الرسالة."""

patient_order_otp_message = """مرحباً {patient_name}٫
يمكنك إستلام طلبك رقم {order_id}
من صيدلية {branch_name}
رمز التحقق الخاص بك هو : {order_otp}
لمزيد من المعلومات يرجى زيارة :{url}"""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        patient_registration_message=patient_registration_message,
        registration_message=registration_message,
        registration_message_ar=registration_message_ar,
        password_reset_message=password_reset_message,
        password_reset_message_ar=password_reset_message_ar,
        patient_order_otp_message=patient_order_otp_message
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0003_auto_20231016_0956'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
