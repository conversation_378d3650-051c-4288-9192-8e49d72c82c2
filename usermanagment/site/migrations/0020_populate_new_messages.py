from django.db import migrations

otp_login_sms_message = """Your One-Time Password (OTP) for this login is: {security_code},
Best regards,
{project_name}"""

otp_login_sms_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code},
مع أطيب التحيات،
{project_name}"""

otp_login_email_message = """Your One-Time Password (OTP) for this login: {security_code},
Best regards,
{project_name}"""

otp_login_email_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code},
مع أطيب التحيات،
{project_name}"""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        otp_login_sms_message=otp_login_sms_message,
        otp_login_sms_message_ar=otp_login_sms_message_ar,
        otp_login_email_message=otp_login_email_message,
        otp_login_email_message_ar=otp_login_email_message_ar
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0019_auto_20250129_1348'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
