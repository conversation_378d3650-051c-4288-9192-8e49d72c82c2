# Generated by Django 3.2.25 on 2024-12-23 14:33

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0016_populate_patient_create_messages'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='change_email_mobile_email_message',
            field=models.TextField(blank=True,
                                   default='Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code},\nBest regards,\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='change_email_mobile_email_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code},\nمع أطيب التحيات،\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='change_email_mobile_sms_message',
            field=models.TextField(blank=True,
                                   default='Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code},\nBest regards,\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='change_email_mobile_sms_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code},\nمع أطيب التحيات،\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='simple_otp_email_message',
            field=models.TextField(blank=True,
                                   default='Your One-Time Password (OTP) for this confirmation is: {security_code},\nBest regards,\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='simple_otp_email_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code},\nمع أطيب التحيات،\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='simple_otp_sms_message',
            field=models.TextField(blank=True,
                                   default='Your One-Time Password (OTP) for this confirmation is: {security_code},\nBest regards,\n{project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='simple_otp_sms_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code},\nمع أطيب التحيات،\n{project_name}',
                                   null=True),
        ),
    ]
