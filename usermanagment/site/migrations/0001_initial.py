# Generated by Django 3.2.12 on 2022-08-03 13:23

from django.db import migrations, models
import django.db.models.deletion
import usermanagment.site.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('range_expansion_max_number_of_rounds', models.IntegerField(blank=True, default=3, null=True)),
                ('range_expansion_max_number_of_tries', models.IntegerField(default=10)),
                ('range_expansion_time_out_period', models.IntegerField(default=15)),
                ('range_expansion_round_radius', models.DecimalField(decimal_places=2, default=2, max_digits=6)),
                ('range_expansion_round_max_number_of_pharmacies', models.IntegerField(default=5)),
                ('range_expansion_round_pharmacies_types', models.JSO<PERSON>ield(blank=True, default=usermanagment.site.models.get_pharmacies_types_default, null=True)),
                ('microservice_context_path', models.CharField(default='usermanagement', max_length=255)),
                ('site', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='sites.site')),
            ],
        ),
    ]
