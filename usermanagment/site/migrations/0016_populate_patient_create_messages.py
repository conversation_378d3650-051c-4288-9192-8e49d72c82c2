# Generated by Django 3.2.23 on 2024-09-02 11:32

from django.db import migrations, models

patient_create_email_message = """Hi٫
Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""

patient_create_email_message_ar = """Hi٫
Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""

patient_create_sms_message = """Hi٫
Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""

patient_create_sms_message_ar = """Hi٫
Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password."""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        patient_create_email_message=patient_create_email_message,
        patient_create_email_message_ar=patient_create_email_message_ar,
        patient_create_sms_message=patient_create_sms_message,
        patient_create_sms_message_ar=patient_create_sms_message_ar
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0015_auto_20240902_1432'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
