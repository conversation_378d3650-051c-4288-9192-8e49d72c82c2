# -*- coding: utf-8 -*-
# Generated by Django 1.10.5 on 2017-01-27 11:41
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations


def create_default_site(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    Site = apps.get_model("sites", "Site")
    site_id = getattr(settings, "SITE_ID", None)
    if site_id:
        Site.objects.get_or_create(
            id=site_id
        )
        SiteSettings.objects.get_or_create(
            site_id=site_id,
        )


class Migration(migrations.Migration):
    dependencies = [("site", "0001_initial")]

    operations = [
        migrations.RunPython(create_default_site, lambda app, schema_editor: None)
    ]
