# Generated by Django 3.2.12 on 2023-10-16 10:25

from django.db import migrations

patient_registration_message = """مرحبا بكم في iO Health!
    الرجاء استخدام هذا الرابط لتعيين كلمة المرور {password_set_link} """

registration_message = """
Welcome to iO Health! We are thrilled to have you as a member of our healthcare community. Your journey to better health begins now, and we're here to support your journey every step of the way.

To finish signing up, enter this verification code: {security_code}

If you have any questions or need assistance, feel free to reach out to our support team at [<EMAIL>].

Thank you for choosing iO Health.

Best regards,
iO Health
"""

registration_message_ar = """
مرحبًا بك في iO Health! نحن متحمسون لانضمامك إلى مجتمعنا الصحي. رحلتك نحو صحة أفضل تبدأ الآن، ونحن هنا لدعمك في كل خطوة على الطريق.

لإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}

إذا كان لديك أي أسئلة أو تحتاج إلى المساعدة، لا تتردد في التواصل مع فريق الدعم لدينا على [<EMAIL>].

شكرًا لاختيارك iO Health.

أطيب التحيات،
iO Health
"""

password_reset_message = """Dear {patient_name},

A request has been initiated to change the password associated with your iO Health account. To ensure the security of your account, we require you to confirm this change.

Your One-Time Password (OTP) for this confirmation is: {{security_code}}

Please enter this OTP on the password change page within the iO Health app to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).

Best regards,
iO Health
"""

password_reset_message_ar = """عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في iO Health. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.

رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}

يرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق iO Health لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).

مع أطيب التحيات،
iO Health
"""

patient_order_otp_message = """مرحباً {patient_name}٫
يمكنك إستلام طلبك رقم {order_id}
من صيدلية {branch_name}
رمز التحقق الخاص بك هو : {order_otp}
لمزيد من المعلومات يرجى زيارة :{url}"""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        patient_registration_message=patient_registration_message,
        registration_message=registration_message,
        registration_message_ar=registration_message_ar,
        password_reset_message=password_reset_message,
        password_reset_message_ar=password_reset_message_ar,
        patient_order_otp_message=patient_order_otp_message
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0004_auto_20231016_1025'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
