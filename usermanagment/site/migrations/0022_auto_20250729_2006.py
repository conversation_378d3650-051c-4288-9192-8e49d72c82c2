# Generated by Django 3.2.25 on 2025-07-29 17:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('site', '0021_populate_new_messages'),
    ]

    operations = [
        migrations.AlterField(
            model_name='sitesettings',
            name='change_email_mobile_email_message',
            field=models.TextField(blank=True, default='Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code}, Please do not share this OTP.\nBest regards,\n{project_name} Mobile App Team', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='change_email_mobile_email_message_ar',
            field=models.TextField(blank=True, default='رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز.\nمع أطيب التحيات،\n{project_name_ar} فريق تطبيق', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='change_email_mobile_sms_message',
            field=models.TextField(blank=True, default='Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code}, Please do not share this OTP.\nBest regards,\n{project_name} Mobile App Team', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='change_email_mobile_sms_message_ar',
            field=models.TextField(blank=True, default='رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز.\nمع أطيب التحيات،\n{project_name_ar} فريق تطبيق', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='delete_account_email_message',
            field=models.CharField(blank=True, default='\nA request has been initiated to delete your account.\nYour One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.\nBest regards,\n{project_name} Mobile App Team\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='delete_account_email_message_ar',
            field=models.CharField(blank=True, default='لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}، لا تقم بمشاركة الرمز.\n                                                        مع أطيب التحيات،\n                                                         {project_name_ar}فريق تطبيق ', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='delete_account_sms_message',
            field=models.CharField(blank=True, default='\nA request has been initiated to delete your account.\nYour One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.\nBest regards,\n{project_name} Mobile App Team\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='delete_account_sms_message_ar',
            field=models.CharField(blank=True, default='لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}، لا تقم بمشاركة الرمز.\n                                                      مع أطيب التحيات،\n                                                       {project_name_ar}فريق تطبيق ', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='otp_login_email_message',
            field=models.TextField(blank=True, default='Your One-Time Password (OTP) for this login: {security_code}, Please do not share this OTP.\n    Best regards,\n    {project_name} Mobile App Team', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='otp_login_email_message_ar',
            field=models.TextField(blank=True, default='رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code}, لا تقم بمشاركة الرمز.\n    مع أطيب التحيات،\n    {project_name_ar} فريق تطبيق', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='otp_login_sms_message',
            field=models.TextField(blank=True, default='Your One-Time Password (OTP) for this login is: {security_code}, Please do not share this OTP.\n    Best regards,\n    {project_name} Mobile App Team', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='otp_login_sms_message_ar',
            field=models.TextField(blank=True, default='رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code}, لا تقم بمشاركة الرمز.\n    مع أطيب التحيات،\n    {project_name_ar} فريق تطبيق', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_email_message',
            field=models.TextField(blank=True, default='Dear {patient_name},\n\nA request has been initiated to change the password associated with your {project_name} Mobile App account. To ensure the security of your account, we require you to confirm this change.\n\nYour One-Time Password (OTP) for this confirmation is: {{security_code}}. Our team will not ask for your credentials. Please do not share this OTP.\n\nPlease enter this OTP on the password change page within the {project_name} Mobile App to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).\n\nBest regards,\n{project_name} Mobile App Team\n', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_email_message_ar',
            field=models.TextField(blank=True, default='عزيزي/عزيزتي {patient_name},\n\nلقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في تطبيق {project_name_ar}. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.\n\nرمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}. لا تقم بمشاركة الرمز\n\nيرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق {project_name_ar} لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).\n\nمع أطيب التحيات،\n{project_name_ar} فريق تطبيق\n', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_sms_message',
            field=models.CharField(blank=True, default='Dear {patient_name},\n\nA request has been initiated to change the password associated with your {project_name} Mobile App account.\nYour One-Time Password (OTP) for this confirmation is: {{security_code}}. Please do not share this OTP.\nBest regards,\n{project_name} Mobile App team\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_sms_message_ar',
            field=models.CharField(blank=True, default='عزيزي/عزيزتي {patient_name},\n\nلقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في تطبيق {project_name_ar}.\nرمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}. لا تقم بمشاركة الرمز\nمع أطيب التحيات،\n{project_name_ar} فريق تطبيق\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='simple_otp_email_message',
            field=models.TextField(blank=True, default='Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.\nBest regards,\n{project_name} Mobile App Team', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='simple_otp_email_message_ar',
            field=models.TextField(blank=True, default='رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز\nمع أطيب التحيات،\n{project_name_ar} فريق تطبيق', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='simple_otp_sms_message',
            field=models.TextField(blank=True, default='Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.\nBest regards,\n{project_name} Mobile App Team', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='simple_otp_sms_message_ar',
            field=models.TextField(blank=True, default='رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز\nمع أطيب التحيات،\n{project_name_ar} فريق تطبيق', null=True),
        ),
    ]
