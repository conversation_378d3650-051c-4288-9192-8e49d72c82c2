from django.db import migrations

simple_otp_sms_message = """Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

simple_otp_sms_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

simple_otp_email_message = """Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

simple_otp_email_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

change_email_mobile_sms_message = """Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

change_email_mobile_sms_message_ar = """رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

change_email_mobile_email_message = """Your One-Time Password (OTP) to change email or mobile for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

change_email_mobile_email_message_ar = """رمز الاستخدام الواحد (OTP) لتعديل البريد الالكتروني أو رقم الهاتف الخاص بعملية التأكيد هذه هو: {security_code}, لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

password_reset_sms_message = """Dear {patient_name},

A request has been initiated to change the password associated with your {project_name} Mobile App account.
Your One-Time Password (OTP) for this confirmation is: {{security_code}}. Please do not share this OTP.
Best regards,
{project_name} Mobile App team"""

password_reset_sms_message_ar = """عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في تطبيق {project_name_ar}.
رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}. لا تقم بمشاركة الرمز
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

password_reset_email_message = """Dear {patient_name},

A request has been initiated to change the password associated with your {project_name} Mobile App account. To ensure the security of your account, we require you to confirm this change.

Your One-Time Password (OTP) for this confirmation is: {{security_code}}. Our team will not ask for your credentials. Please do not share this OTP.

Please enter this OTP on the password change page within the {project_name} Mobile App to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).

Best regards,
{project_name} Mobile App Team"""

password_reset_email_message_ar = """عزيزي/عزيزتي {patient_name},

لقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في تطبيق {project_name_ar}. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.

رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}. لا تقم بمشاركة الرمز

يرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق {project_name_ar} لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).

مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

delete_account_sms_message = """A request has been initiated to delete your account.
Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

delete_account_sms_message_ar = """لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}، لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

delete_account_email_message = """A request has been initiated to delete your account.
Your One-Time Password (OTP) for this confirmation is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

delete_account_email_message_ar = """لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}، لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

otp_login_sms_message = """Your One-Time Password (OTP) for this login is: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

otp_login_sms_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code}, لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""

otp_login_email_message = """Your One-Time Password (OTP) for this login: {security_code}, Please do not share this OTP.
Best regards,
{project_name} Mobile App Team"""

otp_login_email_message_ar = """رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code}, لا تقم بمشاركة الرمز.
مع أطيب التحيات،
{project_name_ar} فريق تطبيق"""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        simple_otp_sms_message=simple_otp_sms_message,
        simple_otp_sms_message_ar=simple_otp_sms_message_ar,
        simple_otp_email_message=simple_otp_email_message,
        simple_otp_email_message_ar=simple_otp_email_message_ar,
        change_email_mobile_sms_message=change_email_mobile_sms_message,
        change_email_mobile_sms_message_ar=change_email_mobile_sms_message_ar,
        change_email_mobile_email_message=change_email_mobile_email_message,
        change_email_mobile_email_message_ar=change_email_mobile_email_message_ar,
        password_reset_sms_message=password_reset_sms_message,
        password_reset_sms_message_ar=password_reset_sms_message_ar,
        password_reset_email_message=password_reset_email_message,
        password_reset_email_message_ar=password_reset_email_message_ar,
        delete_account_sms_message=delete_account_sms_message,
        delete_account_sms_message_ar=delete_account_sms_message_ar,
        delete_account_email_message=delete_account_email_message,
        delete_account_email_message_ar=delete_account_email_message_ar,
        otp_login_sms_message=otp_login_sms_message,
        otp_login_sms_message_ar=otp_login_sms_message_ar,
        otp_login_email_message=otp_login_email_message,
        otp_login_email_message_ar=otp_login_email_message_ar
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0020_populate_new_messages'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
