# Generated by Django 3.2.12 on 2023-10-16 09:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0002_add_default_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='password_reset_message',
            field=models.TextField(blank=True,
                                   default='Your Sehatti code\n\nHi {patient_name},\nTo reset your password please enter this verification code: {{security_code}}.\nIf you didn’t make this request please ignore this email.',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='password_reset_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز التحقق الخاص بك في صحتي\n\nمرحباً {patient_name}،\nلإعادة تعيين كلمة المرور الخاصة بك في صحتي ، أدخل رمز التحقق هذا: {{security_code}}\nإذا لم تقدم هذا الطلب الرجاء تجاهل هذه الرسالة.',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_order_otp_message',
            field=models.TextField(blank=True,
                                   default='مرحباً {patient_name}٫\nيمكنك إستلام طلبك رقم {order_id}\nمن صيدلية {branch_name}\nرمز التحقق الخاص بك هو : {order_otp}\nلمزيد من المعلومات يرجى زيارة :{url}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_registration_message',
            field=models.TextField(blank=True,
                                   default='مرحبا بكم في صحتك!\n    الرجاء استخدام هذا الرابط لتعيين كلمة المرور {password_set_link} ',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='registration_message',
            field=models.TextField(blank=True,
                                   default='Your Sehatti code\n\nWelcome to Sehatti!,\nTo finish signing up, enter this verification code: {security_code}\nIf you didn’t make this request please ignore this email.',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='registration_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز التحقق الخاص بك في صحتي\n\nمرحبا بكم في صحتي!\nلإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}\nإذا لم تقدم هذا الطلب الرجاء تجاهل هذه الرسالة.',
                                   null=True),
        ),
    ]
