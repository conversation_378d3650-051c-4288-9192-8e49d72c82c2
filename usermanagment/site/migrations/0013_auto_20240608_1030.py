# Generated by Django 3.2.23 on 2024-06-08 07:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('site', '0012_populate_modified_messages'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='delete_account_email_message',
            field=models.CharField(blank=True, default='\nA request has been initiated to delete your account.\nYour One-Time Password (OTP) for this confirmation is: {security_code},\nBest regards,\n{project_name}\n', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='delete_account_email_message_ar',
            field=models.CharField(blank=True, default='لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}،\n                                                        مع أطيب التحيات،\n                                                         {project_name}', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='delete_account_sms_message',
            field=models.CharField(blank=True, default='\nA request has been initiated to delete your account.\nYour One-Time Password (OTP) for this confirmation is: {security_code},\nBest regards,\n{project_name}\n', max_length=1024, null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='delete_account_sms_message_ar',
            field=models.CharField(blank=True, default='لقد تم تقديم طلب لحذف حسابك. رمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {security_code}،\n                                                      مع أطيب التحيات،\n                                                       {project_name}', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_email_message',
            field=models.TextField(blank=True, default='Dear {patient_name},\n\nA request has been initiated to change the password associated with your {project_name} account. To ensure the security of your account, we require you to confirm this change.\n\nYour One-Time Password (OTP) for this confirmation is: {{security_code}}\n\nPlease enter this OTP on the password change page within the {project_name} app to complete the process. If you did not initiate this change or have any concerns, please contact our support team immediately on (<EMAIL>).\n\nBest regards,\n{project_name}\n', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_email_message_ar',
            field=models.TextField(blank=True, default='عزيزي/عزيزتي {patient_name},\n\nلقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في {project_name}. لضمان حماية حسابك، نحتاج إلى تأكيدك على هذا التغيير.\n\nرمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}\n\nيرجى إدخال هذا الرمز في صفحة تغيير كلمة السر على تطبيق {project_name} لإتمام العملية. إذا لم تكن أنت من قام بطلب هذا التغيير أو إذا كان لديك أي استفسارات، يرجى التواصل مع فريق الدعم لدينا على الفور عبر البريد الإلكتروني  (<EMAIL>).\n\nمع أطيب التحيات،\n{project_name}\n', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_sms_message',
            field=models.CharField(blank=True, default='Dear {patient_name},\n\nA request has been initiated to change the password associated with your {project_name} account.\nYour One-Time Password (OTP) for this confirmation is: {{security_code}}\nBest regards,\n{project_name}\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='password_reset_sms_message_ar',
            field=models.CharField(blank=True, default='عزيزي/عزيزتي {patient_name},\n\nلقد تم تقديم طلب لتغيير كلمة السر الخاصة بحسابك في {project_name}.\nرمز الاستخدام الواحد (OTP) الخاص بعملية التأكيد هذه هو: {{security_code}}\nمع أطيب التحيات،\n{project_name}\n', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='registration_email_message',
            field=models.TextField(blank=True, default="\nWelcome to {project_name}! We are thrilled to have you as a member of our healthcare community. Your journey to better health begins now, and we're here to support your journey every step of the way.\n\nTo finish signing up, enter this verification code: {security_code}\n\nIf you have any questions or need assistance, feel free to reach out to our support team at [<EMAIL>].\n\nThank you for choosing {project_name}.\n\nBest regards,\n{project_name}\n", null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='registration_email_message_ar',
            field=models.TextField(blank=True, default='\nمرحبًا بك في {project_name}! نحن متحمسون لانضمامك إلى مجتمعنا الصحي. رحلتك نحو صحة أفضل تبدأ الآن، ونحن هنا لدعمك في كل خطوة على الطريق.\n\nلإنهاء تسجيل الدخول إلى حسابك في صحتي ، أدخل رمز التحقق هذا: {security_code}\n\nإذا كان لديك أي أسئلة أو تحتاج إلى المساعدة، لا تتردد في التواصل مع فريق الدعم لدينا على [<EMAIL>].\n\nشكرًا لاختيارك {project_name}.\n\nأطيب التحيات،\n{project_name}\n', null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='registration_sms_message',
            field=models.CharField(blank=True, default='Welcome to {project_name}! We are thrilled to have you as a member.\nTo finish signing up, enter this verification code: {security_code}\nBest regards,\n{project_name}', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='registration_sms_message_ar',
            field=models.CharField(blank=True, default='\nمرحبًا بك في {project_name}! نحن متحمسون لاستضافتك كعضو معنا.\nلإكمال عملية التسجيل، يرجى إدخال رمز التحقق هذا: {security_code}\nمع أطيب التحيات،\nفريق  {project_name}\n', max_length=1024, null=True),
        ),
    ]
