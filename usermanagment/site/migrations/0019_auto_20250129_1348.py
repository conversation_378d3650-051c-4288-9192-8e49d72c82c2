# Generated by Django 3.2.25 on 2025-01-29 13:48

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0018_populate_new_messages'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='otp_login_email_message',
            field=models.TextField(blank=True,
                                   default='Your One-Time Password (OTP) for this login: {security_code},\n    Best regards,\n    {project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='otp_login_email_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code},\n    مع أطيب التحيات،\n    {project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='otp_login_sms_message',
            field=models.TextField(blank=True,
                                   default='Your One-Time Password (OTP) for this login is: {security_code},\n    Best regards,\n    {project_name}',
                                   null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='otp_login_sms_message_ar',
            field=models.TextField(blank=True,
                                   default='رمز الاستخدام الواحد (OTP) الخاص بعملية تسجيل الدخول هذه هو: {security_code},\n    مع أطيب التحيات،\n    {project_name}',
                                   null=True),
        ),
    ]
