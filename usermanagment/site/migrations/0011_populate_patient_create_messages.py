from django.db import migrations

patient_create_email_message = """Hello {patient_name}٫
            Welcome! You're now a member of our app. Download the app now to view your profile and be on the way to your journey."""

patient_create_email_message_ar = """مرحباً {patient_name}٫
            انت الآن عضو في تطبيقنا! حمل تطبيقنا لرؤية ملفك وبدئ رحلتك."""

patient_create_sms_message = """Hello {patient_name}٫
            Welcome! You're now a member of our app. Download the app now to view your profile and be on the way to your journey."""

patient_create_sms_message_ar = """مرحباً {patient_name}٫
            انت الآن عضو في تطبيقنا! حمل تطبيقنا لرؤية ملفك وبدئ رحلتك."""


def populate_default_messages(apps, schema_editor):
    SiteSettings = apps.get_model("site", "SiteSettings")
    db_alias = schema_editor.connection.alias
    SiteSettings.objects.using(db_alias).update(
        patient_create_email_message=patient_create_email_message,
        patient_create_email_message_ar=patient_create_email_message_ar,
        patient_create_sms_message=patient_create_sms_message,
        patient_create_sms_message_ar=patient_create_sms_message_ar
    )


class Migration(migrations.Migration):
    dependencies = [
        ('site', '0010_auto_20240513_1030'),
    ]

    operations = [
        migrations.RunPython(populate_default_messages)
    ]
