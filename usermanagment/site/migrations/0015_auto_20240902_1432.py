# Generated by Django 3.2.23 on 2024-09-02 11:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('site', '0014_auto_20240902_1428'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_email_message',
            field=models.TextField(blank=True, default='Hi٫\n            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password.', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_email_message_ar',
            field=models.TextField(blank=True, default='Hi٫\n            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password.', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_sms_message',
            field=models.TextField(blank=True, default='Hi٫\n            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password.', null=True),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='patient_create_sms_message_ar',
            field=models.TextField(blank=True, default='Hi٫\n            Please download our {project_name} app for iOS: {ios_app_link} or Android: {android_app_link}. After installing, use your mobile number to reset your password.', null=True),
        ),
    ]
