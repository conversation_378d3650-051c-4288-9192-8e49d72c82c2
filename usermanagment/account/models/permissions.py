from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.db import models

from usermanagment.auth.permissions import PERMISSIONS_ENUMS
from usermanagment.core.models import AuditModel


class SystemPermission(Permission):
    """A global permission, not attached to a model"""

    def save(self, *args, **kwargs):
        ct, created = ContentType.objects.get_or_create(
            model=self._meta.verbose_name, app_label=self._meta.app_label,
        )
        self.content_type = ct
        super(SystemPermission, self).save(*args)

    class Meta:
        proxy = True
        verbose_name = "permission"

        permissions = (
            [(enum_value.codename, enum_value.description) for enum_values in
             PERMISSIONS_ENUMS for
             enum_value in enum_values]
        )


class GroupAudit(AuditModel):
    group = models.OneToOneField(Group, on_delete=models.CASCADE, related_name='audit')

    def __str__(self):
        return f"Audit for group: {self.group.name}"
