import logging

from django.conf import settings
from django.contrib.auth.models import (
    Abstract<PERSON><PERSON><PERSON><PERSON>,
    BaseUserManager,
    PermissionsMixin,
)
from django.contrib.gis.db import models as gis_models
from django.core.exceptions import ValidationError
from django.db import models, IntegrityError
from django.db.models import Q, Value, FilteredRelation, JSONField
from django.forms.models import model_to_dict
from django.utils import timezone
from django_keycloak.managers import KeycloakUserManager
from phonenumber_field.modelfields import PhoneNumber
from versatileimagefield.fields import VersatileImageField

from usermanagment import settings
from .possible_phone_number import PossiblePhoneNumberField
from .. import CustomerEvents, PersonGender, TwoAuthVerificationMethod
from ..utils import get_address_coordinates_from_google
from ..validators import validate_possible_number
from ...auth.enums import AppTypes, AppRoleTypes, CUSTOMER_NATIONAL_ID_LOGIN_SUFFIX, \
    VendorUserTypes, BiometricLoginType
from ...auth.exceptions import PermissionDenied
from ...auth.jwt_utils import verify_jwt_token
from ...auth.permissions import BasePermissionEnum
from ...core.db.fields import EnumField
from ...core.models import SoftDeletionManager, SoftDeletionModel, BaseTranslationModel, \
    AuditModel
from ...core.utils.json_serializer import CustomJsonEncoder
from ...core.utils.translations import TranslationProxy
from ...graphql.utils.request_utils import get_current_user

logger = logging.getLogger(__name__)


class AddressQueryset(models.QuerySet):
    def annotate_default(self, user):
        # Set default shipping/billing address pk to None
        # if default shipping/billing address doesn't exist
        default_shipping_address_pk, default_billing_address_pk = None, None
        if user.default_shipping_address:
            default_shipping_address_pk = user.default_shipping_address.pk
        if user.default_billing_address:
            default_billing_address_pk = user.default_billing_address.pk

        return user.addresses.annotate(
            user_default_shipping_address_pk=Value(
                default_shipping_address_pk, models.IntegerField()
            ),
            user_default_billing_address_pk=Value(
                default_billing_address_pk, models.IntegerField()
            ),
        )


class Address(gis_models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    name_ar = models.CharField(max_length=255, null=True, blank=True)
    street_address_1 = models.CharField(max_length=256, blank=True)
    street_address_1_ar = models.CharField(max_length=255, blank=True)
    street_address_2 = models.CharField(max_length=256, blank=True)
    street_address_2_ar = models.CharField(max_length=255, blank=True)
    city = models.ForeignKey(
        "block.City",
        related_name="addresses",
        on_delete=models.PROTECT,
        null=False, blank=False, db_index=True
    )
    block = models.ForeignKey(
        "block.Block",
        related_name="addresses",
        on_delete=models.PROTECT,
        null=True, blank=False, db_index=True
    )
    area = models.CharField(max_length=128, blank=False,null=False)
    area_ar = models.CharField(max_length=255, blank=True)
    district = models.CharField(max_length=128, blank=True)
    district_ar = models.CharField(max_length=255, blank=True)
    region = models.CharField(max_length=128, blank=True)
    region_ar = models.CharField(max_length=255, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    phone = PossiblePhoneNumberField(blank=True, default="")
    building_name = models.CharField(max_length=256, null=True, blank=True)
    building_name_ar = models.CharField(max_length=255, null=True, blank=True)
    building_number = models.CharField(max_length=256, null=True, blank=True)
    unit_number = models.CharField(max_length=256, null=True, blank=True)

    coordinates = JSONField(default=dict,
                            encoder=CustomJsonEncoder,
                            null=True,
                            blank=True)
    location = gis_models.PointField(srid=4326, null=True, blank=True)

    objects = AddressQueryset.as_manager()

    translated = TranslationProxy()

    class Meta:
        db_table = "user_account_address"
        ordering = ("pk",)

    def __eq__(self, other):
        if not isinstance(other, Address):
            return False
        return self.as_data() == other.as_data()

    __hash__ = models.Model.__hash__

    def as_data(self):
        """Return the address as a dict suitable for passing as kwargs.

        Result does not contain the primary key or an associated user.
        """
        # TODO [MOA] find way to copy address translations
        data = model_to_dict(self, exclude=["id", "user", "city"])
        if isinstance(data["phone"], PhoneNumber):
            data["phone"] = data["phone"].as_e164
        data["city"] = self.city
        data["block"] = self.block
        return data

    def get_copy(self):
        """Return a new instance of the same address."""
        return Address.objects.create(**self.as_data())

    def save(self, *args, **kwargs):
        get_address_coordinates_from_google(self)
        super().save(*args, **kwargs)


class AddressTranslation(BaseTranslationModel):
    address = models.ForeignKey(
        Address, related_name="translations", on_delete=models.CASCADE
    )

    name = models.CharField(max_length=255, null=True, blank=False)
    street_address_1 = models.CharField(max_length=255)
    street_address_2 = models.CharField(max_length=255)
    area = models.CharField(max_length=255)
    district = models.CharField(max_length=255)
    region = models.CharField(max_length=255)
    building_name = models.CharField(max_length=255)

    class Meta:
        db_table = "user_account_addresstranslation"
        unique_together = (("language_code", "address"),)


class UserManager(KeycloakUserManager, BaseUserManager, SoftDeletionManager):
    def create_user(
            self, email, password=None, is_active=True, **extra_fields
    ):
        """Create a user instance with the given email and password."""
        email = UserManager.normalize_email(email)
        # Google OAuth2 backend send unnecessary username field
        extra_fields.pop("username", None)

        user = self.model(
            email=email, is_active=is_active, **extra_fields
        )
        user.full_clean(exclude=['password'])
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields['app_type'] = AppTypes.ADMIN
        extra_fields['app_role'] = AppRoleTypes.ADMIN
        return self.create_user(
            email, password, **extra_fields
        )

    def customers(self, branch_id=None, has_chat_with=False):
        current_user = get_current_user()
        qs = self.get_queryset().filter(app_type=AppTypes.CUSTOMER)
        if not current_user.is_superuser:
            filters = []
            annotates = {}
            if current_user.is_vendor_admin and not branch_id:
                q_filter = Q(order_branches__vendor_id=current_user.vendor_id)
                if has_chat_with:
                    q_filter |= Q(
                        sent_messages__branch__vendor_id=current_user.vendor_id)
                filters.append(q_filter)

            elif branch_id:
                annotates = {
                    "sentmessages": FilteredRelation('sent_messages', condition=Q(
                        sent_messages__branch_id=branch_id)),
                    "receivedmessages": FilteredRelation('received_messages',
                                                         condition=Q(
                                                             received_messages__branch_id=branch_id))
                }

                if not has_chat_with:
                    filters.append(Q(order_branches__isnull=False) | Q(
                        sentmessages__id__isnull=False) | Q(
                        receivedmessages__id__isnull=False))
                else:
                    filters.append(Q(sentmessages__id__isnull=False) | Q(
                        receivedmessages__id__isnull=False))

            else:
                current_user_branches = [
                    branch_id] if branch_id else current_user.branches.all()
                q_filter = Q(order_branches__in=current_user_branches)
                if has_chat_with:
                    q_filter |= Q(sent_messages__branch__in=current_user_branches)
                filters.append(q_filter)

            qs = qs.annotate(**annotates).filter(*filters)

        return qs.distinct()

    def staff(self):
        return self.get_queryset().filter(app_type=AppTypes.ADMIN,
                                          app_role=AppRoleTypes.USER)

    def get_by_natural_key(self, username):
        # return self.get_queryset().get(
        #     Q(username=username) | Q(sso_id=username))

        return self.get_queryset().get(id=username)

    def create_from_token(self, token, password=None, **kwargs):
        """
        Create a new user from a valid token
        """
        user_info = verify_jwt_token(token)
        sso_id = user_info.get("sub")
        preferred_username = user_info.get("preferred_username")
        try:
            validate_possible_number(preferred_username)
        except ValidationError:
            preferred_username = None

        try:
            try:
                user = self.get(sso_id=sso_id)
            except User.DoesNotExist:
                if user_info.get('app_type') and user_info.get('app_type') == AppTypes.CUSTOMER:
                    raise PermissionDenied()
                extra_data = self.prepare_customer_data(preferred_username, sso_id,
                                                        user_info)
                user = self.model(
                    **extra_data
                )
                user.save(using=self._db)
                from .. import events
                events.customer_account_created_from_sso_event(user=user)
        except Exception as e:
            logger.error("error while logging user in ", exc_info=e)
            raise PermissionDenied()

        return user

    @staticmethod
    def prepare_customer_data(preferred_username, sso_id, user_info):
        email = user_info.get("email")
        extra_data = {
            "email": email,
            "mobile": user_info.get('mobile', preferred_username),
            "first_name": user_info.get("given_name"),
            "last_name": user_info.get("family_name"),
            "sso_id": sso_id,
        }
        if email and CUSTOMER_NATIONAL_ID_LOGIN_SUFFIX in email:
            extra_data['national_id'] = email.replace(CUSTOMER_NATIONAL_ID_LOGIN_SUFFIX,
                                                      "")
        return extra_data

    def get_by_keycloak_id(self, username):
        return self.get_by_natural_key(username)


class User(PermissionsMixin, AbstractBaseUser, SoftDeletionModel, AuditModel):
    password = None
    email = models.EmailField(null=True, blank=True, unique=True)
    national_id = models.CharField(max_length=30, null=True, blank=True, db_index=True)
    first_name = models.CharField(max_length=50, null=True, blank=True)
    second_name = models.CharField(max_length=50, null=True, blank=True)
    third_name = models.CharField(max_length=50, null=True, blank=True)
    last_name = models.CharField(max_length=50, null=True, blank=True)
    full_name = models.CharField(max_length=200, null=True, blank=True)

    first_name_ar = models.CharField(max_length=50, null=True, blank=True)
    second_name_ar = models.CharField(max_length=50, null=True, blank=True)
    third_name_ar = models.CharField(max_length=50, null=True, blank=True)
    last_name_ar = models.CharField(max_length=50, null=True, blank=True)
    full_name_ar = models.CharField(max_length=200, null=True, blank=True)


    gender = models.CharField(max_length=6, choices=PersonGender.choices, null=True,
                              blank=True, db_index=True)
    date_of_birth = models.DateField(null=True, blank=True)
    photo = VersatileImageField(blank=True, null=True, max_length=700)
    addresses = models.ManyToManyField(
        Address, blank=True, related_name="user_addresses"
    )
    is_active = models.BooleanField(default=False, db_index=True)
    note = models.CharField(max_length=2000, null=True, blank=True)
    date_joined = models.DateTimeField(default=timezone.now, editable=False,
                                       db_index=True)
    default_shipping_address = models.ForeignKey(
        Address, related_name="+", null=True, blank=True, on_delete=models.SET_NULL
    )
    default_billing_address = models.ForeignKey(
        Address, related_name="+", null=True, blank=True, on_delete=models.SET_NULL
    )
    avatar = models.CharField(max_length=256, null=True, blank=True)

    mobile = PossiblePhoneNumberField(null=True, blank=True, db_index=True)

    mobile_verified = models.BooleanField(default=False)
    email_verified = models.BooleanField(default=False)

    USERNAME_FIELD = "id"

    app_type = EnumField(max_length=20,
                         choices=AppTypes.choices(),
                         null=False, blank=False,
                         default=AppTypes.CUSTOMER, db_index=True)

    app_role = EnumField(max_length=20,
                         choices=AppRoleTypes.choices(),
                         null=False, blank=False,
                         default=AppRoleTypes.USER, db_index=True)

    vendor_user_type = EnumField(max_length=100,
                                 choices=VendorUserTypes.choices(),
                                 null=True, blank=True, db_index=True)

    sso_id = models.CharField(max_length=254, null=False, blank=True, unique=True)

    vendor = models.ForeignKey("vendor.Vendor", null=True, blank=True,
                               related_name="users", on_delete=models.CASCADE)

    payer = models.ForeignKey("payer.Payer", null=True, blank=True,
                              related_name="users", on_delete=models.CASCADE)

    meeting_platform_id = models.CharField(max_length=256, null=True, blank=True,
                                           unique=False)

    favorite_vendors = models.ManyToManyField(
        "vendor.Vendor",
        blank=True,
        related_name="favorite_customers",
        through='vendor.CustomerFavoriteVendor',
        through_fields=('customer', 'vendor')
    )

    total_money_spent = models.DecimalField(default=0, decimal_places=2, max_digits=10)
    total_orders_count = models.DecimalField(default=0, decimal_places=2, max_digits=10)

    last_password_reset_date = models.DateTimeField(null=True, blank=True)

    vendor_id_null_to_zero = models.IntegerField(
        blank=True,
        editable=False,
        null=True,
    )

    parent_user = models.ForeignKey("self", on_delete=models.CASCADE,
                                    related_name="dependents", null=True, blank=True)

    relation_type = models.CharField(max_length=255, null=True, blank=True)

    preferred_language = models.ForeignKey(
        "account.Language", null=True, blank=True, on_delete=models.SET_NULL,
        related_name="users", db_index=True
    )

    terms_and_conditions_accepted_version = models.FloatField(max_length=255, null=True,
                                                              blank=True, default=0)

    default_branch = models.ForeignKey(
        "vendor.Branch", related_name="+", null=True, blank=True,
        on_delete=models.SET_NULL
    )

    delete_reason = models.CharField(max_length=255, null=True, blank=True)

    two_factor_auth_enabled = models.BooleanField(default=False)

    two_factor_auth_secret = models.CharField(max_length=255, null=True, blank=True)

    two_factor_auth_verification_method = EnumField(
        max_length=20,
        choices=TwoAuthVerificationMethod.CHOICES,
        null=True, blank=True
    )

    ask_to_enable_bio_login = models.BooleanField(default=True)

    marketing_consent = models.BooleanField(null=True, blank=True, default=True)

    objects = UserManager()

    class Meta:
        ordering = ("email", "pk")
        constraints = [
            models.UniqueConstraint(
                fields=["app_type", "mobile", "vendor_id_null_to_zero"],
                name='account_user_app_type_mobile_vendor_uniq'
            ),
            models.UniqueConstraint(
                fields=["app_type", "national_id", "vendor_id_null_to_zero"],
                name='account_user_app_type_national_id_vendor_uniq'
            ),
        ]

    def has_perm(self, perm: BasePermissionEnum, obj=None):  # type: ignore
        # This method is overridden to accept perm as BasePermissionEnum
        if not self.is_authenticated:
            return False
        perm_value = perm.codename if hasattr(perm, "value") else perm  # type: ignore
        return super().has_perm(perm_value, obj)

    def save(self, *args, **kwargs):

        self.vendor_id_null_to_zero = self.vendor.id if self.vendor else 0
        self.full_name = ((self.first_name + ' ' if self.first_name else '') + \
                          (self.second_name + ' ' if self.second_name else '') + \
                          (self.third_name + ' ' if self.third_name else '') + \
                          (self.last_name if self.last_name else '')).strip()
        self.full_name_ar = ((self.first_name_ar + ' ' if self.first_name_ar else '') + \
                            (self.second_name_ar + ' ' if self.second_name_ar else '') + \
                            (self.third_name_ar + ' ' if self.third_name_ar else '') + \
                            (self.last_name_ar if self.last_name_ar else '')).strip()
        try:
            super().save(*args, **kwargs)
        except IntegrityError as e:

            if "account_user_app_type_mobile_vendor_uniq" in e.args[1]:
                raise ValidationError({"mobile": "mobile already exists"})

            elif "account_user_app_type_national_id_vendor_uniq" in e.args[1]:
                raise ValidationError({"national_id": "national_id already exists"})

            else:
                raise ValidationError(e.args[1])

    @property
    def is_superuser(self):
        return self.app_type == AppTypes.ADMIN and self.app_role == AppRoleTypes.ADMIN

    @property
    def is_staff(self):
        return self.app_type == AppTypes.ADMIN and self.app_role == AppRoleTypes.USER

    @property
    def is_admin(self):
        return self.app_type == AppTypes.ADMIN

    @property
    def is_vendor_admin(self):
        return self.app_type == AppTypes.VENDOR and self.app_role == AppRoleTypes.ADMIN

    @property
    def is_payer_admin(self):
        return self.app_type == AppTypes.PAYER and self.app_role == AppRoleTypes.ADMIN

    @property
    def is_pharmacist(self):
        return self.vendor_user_type == VendorUserTypes.PHARMACIST

    @property
    def is_vendor_staff(self):
        return self.app_type == AppTypes.VENDOR and self.app_role == AppRoleTypes.USER

    @property
    def is_doctor(self):
        return self.vendor_user_type == VendorUserTypes.DOCTOR

    @property
    def is_payer_staff(self):
        return self.app_type == AppTypes.PAYER

    @property
    def is_nurse(self):
        return self.vendor_user_type == VendorUserTypes.NURSE

    @property
    def is_receptionist(self):
        return self.vendor_user_type == VendorUserTypes.RECEPTIONIST

    @property
    def is_dental_hygienist(self):
        return self.vendor_user_type == VendorUserTypes.DENTAL_HYGIENIST

    @property
    def is_diabetes_educator(self):
        return self.vendor_user_type == VendorUserTypes.DIABETES_EDUCATOR

    @property
    def is_fitness_coach(self):
        return self.vendor_user_type == VendorUserTypes.FITNESS_COACH

    @property
    def is_nutritionist(self):
        return self.vendor_user_type == VendorUserTypes.NUTRITIONIST

    @property
    def is_optometrist(self):
        return self.vendor_user_type == VendorUserTypes.OPTOMETRIST

    @property
    def is_podiatric_medical_assistant(self):
        return self.vendor_user_type == VendorUserTypes.PODIATRIC_MEDICAL_ASSISTANT

    @property
    def is_psychologist(self):
        return self.vendor_user_type == VendorUserTypes.PSYCHOLOGIST

    @property
    def is_social_worker(self):
        return self.vendor_user_type == VendorUserTypes.SOCIAL_WORKER

    @property
    def is_vendor(self):
        return self.app_type == AppTypes.VENDOR

    @property
    def is_consumer(self):
        return self.app_type == AppTypes.CUSTOMER

    @property
    def is_payer(self):
        return self.app_type == AppTypes.PAYER

    @property
    def is_aggregator(self):
        return self.app_type == AppTypes.AGGREGATOR

    @property
    def patient_id(self):
        patient = getattr(self, 'patient', None)
        return patient.pk if patient else None

    def has_one_of_permissions(self, permissions):
        return any(self.has_perm(perm) for perm in permissions)

    @property
    def doctor_id(self):
        doctor = getattr(self, 'doctor', None)
        return doctor.pk if doctor else None

    @property
    def nurse_id(self):
        nurse = getattr(self, 'nurse', None)
        return nurse.pk if nurse else None

    @property
    def pharmacist_id(self):
        pharmacist = getattr(self, 'pharmacist', None)
        return pharmacist.pk if pharmacist else None

    @property
    def receptionist_id(self):
        receptionist = getattr(self, 'receptionist', None)
        return receptionist.pk if receptionist else None

    @property
    def manager_id(self):
        manager = getattr(self, 'manager', None)
        return manager.pk if manager else None

    @property
    def dental_hygienist_id(self):
        dental_hygienist = getattr(self, 'dental_hygienist', None)
        return dental_hygienist.pk if dental_hygienist else None

    @property
    def diabetes_educator_id(self):
        diabetes_educator = getattr(self, 'diabetes_educator', None)
        return diabetes_educator.pk if diabetes_educator else None

    @property
    def fitness_coach_id(self):
        fitness_coach = getattr(self, 'fitness_coach', None)
        return fitness_coach.pk if fitness_coach else None

    @property
    def nutritionist_id(self):
        nutritionist = getattr(self, 'nutritionist', None)
        return nutritionist.pk if nutritionist else None

    @property
    def optometrist_id(self):
        optometrist = getattr(self, 'optometrist', None)
        return optometrist.pk if optometrist else None

    @property
    def podiatric_medical_assistant_id(self):
        podiatric_medical_assistant = getattr(self, 'podiatric_medical_assistant', None)
        return podiatric_medical_assistant.pk if podiatric_medical_assistant else None

    @property
    def psychologist_id(self):
        psychologist = getattr(self, 'psychologist', None)
        return psychologist.pk if psychologist else None

    @property
    def social_worker_id(self):
        social_worker = getattr(self, 'social_worker', None)
        return social_worker.pk if social_worker else None

    @property
    def driver_id(self):
        driver = getattr(self, 'driver', None)
        return driver.pk if driver else None


class CustomerNote(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=True, null=True, on_delete=models.SET_NULL
    )
    date = models.DateTimeField(db_index=True, auto_now_add=True)
    content = models.CharField(max_length=2000)
    is_public = models.BooleanField(default=True)
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="notes", on_delete=models.CASCADE
    )

    class Meta:
        ordering = ("date",)


class CustomerEvent(models.Model):
    """Model used to store events that happened during the customer lifecycle."""

    date = models.DateTimeField(default=timezone.now, editable=False)
    type = EnumField(
        max_length=255,
        choices=[
            (type_name.upper(), type_name) for type_name, _ in CustomerEvents.CHOICES
        ],
    )

    order_id = models.IntegerField(null=True)
    parameters = JSONField(blank=True, default=dict, encoder=CustomJsonEncoder)

    user = models.ForeignKey(User, related_name="events", on_delete=models.CASCADE)

    class Meta:
        ordering = ("date",)

    def __repr__(self):
        return f"{self.__class__.__name__}(type={self.type!r}, user={self.user!r})"


class ConsumerViewPreference(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="view_preferences",
        blank=False,
        null=False,
        on_delete=models.CASCADE
    )
    view = models.CharField(max_length=255, null=False, blank=False)
    data = models.JSONField(blank=False)

    class Meta:
        unique_together = (("user", "view"),)


class EmailPhoneVerification(models.Model):
    email = models.EmailField(null=True)
    mobile = PossiblePhoneNumberField(null=True)
    verification_code = models.CharField(max_length=20)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    session_token = models.CharField(max_length=500)
    app_type = EnumField(max_length=20, choices=AppTypes.choices(), null=True,
                         blank=True)
    vendor = models.ForeignKey("vendor.Vendor", null=True, blank=True,
                               on_delete=models.CASCADE)
    is_token_used = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        expiration_time = settings.EMAIL_PHONE_VERIFICATION.get(
            "SECURITY_CODE_EXPIRATION_TIME")

        if not self.expires_at:
            self.expires_at = timezone.now() + timezone.timedelta(
                seconds=expiration_time)
        super().save(*args, **kwargs)


class Language(models.Model):
    code = models.CharField(max_length=10, unique=True, null=False, blank=False,
                            db_index=True)
    display = models.CharField(max_length=100, null=False, blank=False, db_index=True)
    display_ar = models.CharField(max_length=100, null=True, blank=True, db_index=True)


class BiometricLoginDevice(AuditModel):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="biometric_devices",
        blank=False,
        null=False,
        on_delete=models.CASCADE
    )
    device_id = models.CharField(max_length=255, null=False, blank=False,unique=True)

    device_name = models.CharField(max_length=255, null=False, blank=False)

    type = models.CharField(max_length=20, choices=BiometricLoginType.choices,
                            null=False, blank=False)
    public_key = models.TextField(null=False, blank=False)


class BulkMessageRequest(models.Model):
    requester = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    user_count = models.IntegerField()
    input_data = models.TextField()

    def __str__(self):
        return f"BulkMessageRequest by {self.requester} on {self.created_at}"
