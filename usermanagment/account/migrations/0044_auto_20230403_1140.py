# Generated by Django 3.2.12 on 2023-04-03 11:40

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ('account', '0043_alter_systempermission_options'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='vendor_type',
        ),
        migrations.AddField(
            model_name='user',
            name='vendor_user_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[
                ('Manager', 'Manager'), ('Pharmacist', 'Pharmacist'),
                ('Nurse', 'Nurse'), ('Doctor', 'Doctor')], max_length=20, null=True),
        ),
    ]
