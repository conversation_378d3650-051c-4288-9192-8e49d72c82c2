# Generated by Django 3.2.25 on 2025-02-02 13:23

from django.db import migrations
from django.db import migrations, connection


def create_filtered_unique_index(apps, schema_editor):
    print('------------------'+connection.vendor+'------------------')
    if connection.vendor == 'microsoft':
        schema_editor.execute("""
                     ALTER TABLE account_user
            DROP CONSTRAINT account_user_app_type_mobile_vendor_uniq;
        """)
        schema_editor.execute("""
            CREATE UNIQUE INDEX account_user_app_type_mobile_vendor_uniq
            ON account_user (app_type,mobile,vendor_id_null_to_zero)
            WHERE mobile IS NOT NULL;
        """)


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0121_alter_user_meeting_platform_id'),
    ]

    operations = [
        migrations.RunPython(create_filtered_unique_index),
    ]
