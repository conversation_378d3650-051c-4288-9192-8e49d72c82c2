# Generated by Django 3.0.6 on 2021-12-28 12:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0003_auto_20211228_1401'),
        ('vendor', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='favorite_vendors',
            field=models.ManyToManyField(blank=True, related_name='favorite_customers', through='vendor.CustomerFavoriteVendor', to='vendor.Vendor'),
        ),
        migrations.AddField(
            model_name='user',
            name='vendor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='users', to='vendor.Vendor'),
        ),
        migrations.DeleteModel(
            name='BranchUser',
        ),
    ]
