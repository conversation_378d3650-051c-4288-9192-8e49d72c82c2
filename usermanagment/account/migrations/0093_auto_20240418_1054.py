# Generated by Django 3.2.23 on 2024-04-18 08:54

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0092_alter_user_vendor_user_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='health_license_end_date',
        ),
        migrations.RemoveField(
            model_name='user',
            name='health_license_number',
        ),
        migrations.RemoveField(
            model_name='user',
            name='health_license_start_date',
        ),
        migrations.AlterField(
            model_name='user',
            name='vendor_user_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Manager', 'Manager'), ('Pharmacist', 'Pharmacist'), ('Nurse', 'Nurse'), ('Doctor', 'Doctor'), ('Receptionist', 'Receptionist'), ('Dental Hygienist', 'Dental Hygienist'), ('Diabetes Educator', 'Diabetes Educator'), ('Fitness Coach', 'Fitness Coach'), ('Nutritionist', 'Nutritionist'), ('Optometrist', 'Optometrist'), ('Podiatric Medical Assistant', 'Podiatric Medical Assistant'), ('Psychologist', 'Psychologist'), ('Social Worker', 'Social Worker')], db_index=True, max_length=100, null=True),
        ),
    ]
