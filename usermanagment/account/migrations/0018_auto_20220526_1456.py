# Generated by Django 3.2.12 on 2022-05-26 14:56

from django.db import migrations, models
import usermanagment.core.utils.json_serializer


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0017_alter_systempermission_options'),
    ]

    operations = [
        migrations.AlterField(
            model_name='address',
            name='coordinates',
            field=models.JSONField(blank=True, default=dict, encoder=usermanagment.core.utils.json_serializer.CustomJsonEncoder, null=True),
        ),
        migrations.AlterField(
            model_name='customerevent',
            name='parameters',
            field=models.JSONField(blank=True, default=dict, encoder=usermanagment.core.utils.json_serializer.CustomJsonEncoder),
        ),
    ]
