# Generated by Django 3.2.12 on 2023-10-07 11:53

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0074_auto_20231003_1509'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='relation_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Son', 'Son'), ('Daughter', 'Daughter'), ('Wife', 'Wife'), ('Husband', 'Husband'), ('Father', 'Father'), ('Mother', 'Mother'), ('Brother', 'Brother'), ('Sister', 'Sister'), ('Grand Father', 'Grand Father'), ('Grand Mother', 'Grand Mother')], max_length=20, null=True),
        ),
    ]
