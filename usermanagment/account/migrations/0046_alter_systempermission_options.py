# Generated by Django 3.2.12 on 2023-04-26 08:37

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0045_alter_systempermission_options'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='systempermission',
            options={'permissions': [('manage_users', 'Can Manage Users'), ('manage_staff', 'Can Manage Staff'), ('manage_permissions', 'Can Manage Permissions'), ('view_customers', 'Can View Customers'), ('view_users', 'Can View Users'), ('view_staff', 'Can View Staff'), ('manage_vendors', 'Can Manage vendors'), ('manage_branches', 'Can Manage branches'), ('manage_blocks', 'Can Manage blocks'), ('manage_cities', 'Can Manage cities'), ('manage_subscriptions', 'Can Manage subscriptions'), ('view_subscriptions', 'Can View subscriptions'), ('manage_wallets', 'Can Manage wallets'), ('view_wallets', 'Can View wallets'), ('manage_chat', 'Can Manage chat'), ('manage_patients', 'Can Manage patients'), ('view_patients', 'Can View patients'), ('manage_patient_medical_history', 'Can Manage patient medical history'), ('manage_doctors', 'Can Manage doctors'), ('manage_specializations', 'Can Manage subscriptions'), ('manage_qualifications', 'Can Manage qualifications'), ('manage_discounts', 'Can Manage discounts'), ('manage_plugins', 'Can Manage plugins'), ('manage_settings', 'Can Manage settings'), ('manage_translations', 'Can Manage translations'), ('manage_checkouts', 'Can Manage checkouts'), ('manage_orders', 'Can Manage orders'), ('manage_products', 'Can Manage products'), ('manage_product_stocks', 'Can Manage product stocks'), ('manage_shipping', 'Can Manage shipping'), ('manage_pricing_rules', 'Can Manage pricing rules'), ('manage_rejection_reasons', 'Can Manage order rejection reasons'), ('manage_invoices', 'Can Manage invoices.'), ('manage_prescriptions', 'Can Manage prescriptions'), ('assign_prescription_order_to_vendors', 'Can assign prescription order to vendors'), ('convert_virtual_order_to_real_one', 'Can convert virtual order to real one'), ('manage_early_refill_reasons', 'Can Manage early refill reasons'), ('manage_medical_delivery_requests', 'Can Manage medical delivery requests'), ('manage_payments', 'Can Manage payments'), ('manage_code_system_reviewers', 'Can Manage code system reviewers'), ('publish_code_system', 'Can Publish code system'), ('upload_code_system_file', 'Can Upload code system file'), ('manage_code_system_editor_reviewers', 'Can Manage code system editor/reviewer'), ('manage_code_system_lists', 'Can Manage code system lists'), ('view_code_system_lists', 'Can View code system lists'), ('manage_rule_engine_rules', 'Can Manage rule engine rules'), ('workflow_manage_orders', 'Can Manage Orders lifecycle'), ('manage_pharmacy_credentials', 'Can Manage pharmacy credentials'), ('manage_health_programs', 'Can Manage health programs'), ('manage_health_programs_care_for_fields', 'Can Manage health programs care for fields'), ('manage_visits', 'Can Manage visits'), ('manage_visit_rejection_reasons', 'Can Manage visit rejection reasons'), ('manage_visit_cancel_reasons', 'Can Manage visit cancel reasons'), ('manage_medications', 'Can Manage medications'), ('manage_diagnosis', 'Can Manage diagnosis'), ('manage_scientific_details', 'Can Manage scientific details'), ('manage_visit_summary', 'Can Manage visit summary'), ('manage_health_channels', 'Can Manage health channels'), ('manage_health_channels_categories', 'Can Manage health channels categories'), ('medication_scientific_details', 'Can Manage Medication Scientific Details'), ('manage_program_templates', 'Can Manage Program Templates'), ('manage_programs', 'Can Manage Programs'), ('manage_program_teams', 'Can Manage Program Teams'), ('manage_insurance', 'Can Manage insurances'), ('manage_insurance_networks', 'Can Manage insurance networks'), ('manage_appointments', 'Can Manage appointments')], 'verbose_name': 'permission'},
        ),
    ]
