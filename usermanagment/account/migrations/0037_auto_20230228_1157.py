# Generated by Django 3.2.12 on 2023-02-28 09:57

from django.db import migrations, models
import usermanagment.account.models.possible_phone_number


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0036_auto_20230214_1025'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='username',
            field=models.CharField(blank=True, max_length=256, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(max_length=254),
        ),
        migrations.AlterField(
            model_name='user',
            name='mobile',
            field=usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(max_length=128, null=True, region=None),
        ),
        migrations.AlterField(
            model_name='user',
            name='national_id',
            field=models.Char<PERSON>ield(blank=True, max_length=30, null=True),
        ),
    ]
