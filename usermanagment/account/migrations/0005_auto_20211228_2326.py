# Generated by Django 3.0.6 on 2021-12-28 21:26

from django.db import migrations, models

import usermanagment.auth.enums


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0004_auto_20211228_1401'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='app_role',
            field=models.CharField(choices=[('Admin', 'Admin'), ('User', 'User')], default=usermanagment.auth.enums.AppRoleTypes['USER'], max_length=20),
        ),
        migrations.AddField(
            model_name='user',
            name='app_type',
            field=models.CharField(choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'), ('Customer', 'Customer')], default=usermanagment.auth.enums.AppTypes['CUSTOMER'], max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='type',
            field=models.Char<PERSON>ield(choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'), ('Customer', 'Customer')], max_length=20),
        ),
    ]
