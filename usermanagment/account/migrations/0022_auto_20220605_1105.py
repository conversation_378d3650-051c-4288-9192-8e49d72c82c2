# Generated by Django 3.2.12 on 2022-06-05 11:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0021_alter_customernote_content'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='meeting_platform_id',
        ),
        migrations.AddField(
            model_name='user',
            name='health_license_end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='health_license_number',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='health_license_start_date',
            field=models.DateField(blank=True, null=True),
        ),
    ]
