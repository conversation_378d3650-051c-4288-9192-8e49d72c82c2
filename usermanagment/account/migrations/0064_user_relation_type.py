# Generated by Django 3.2.12 on 2023-08-20 07:39

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0063_alter_systempermission_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='relation_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Son', 'Son'), ('Daughter', 'Daughter'), ('Wife', 'Wife'), ('Husband', 'Husband'), ('Father', 'Father'), ('Mother', 'Mother'), ('Brother', 'Brother'), ('Sister', 'Sister'), ('Grand Father', 'Grand Father'), ('Grand Mother', 'Grand Mother'), ('Grand Son', 'Grand Son'), ('Grand Daughter', 'Grand Daughter'), ('Uncle', 'Uncle'), ('Aunt', 'Aunt'), ('<PERSON><PERSON>he<PERSON>', 'Nephew'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('Cousin', 'Cousin'), ('Friend', 'Friend'), ('Other', 'Other')], max_length=20, null=True),
        ),
    ]
