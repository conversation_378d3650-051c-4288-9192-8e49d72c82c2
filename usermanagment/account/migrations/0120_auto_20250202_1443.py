from django.db import migrations, connection

def create_filtered_unique_index(apps, schema_editor):
    print('------------------'+connection.vendor+'------------------')
    if connection.vendor == 'microsoft':
        schema_editor.execute("""
                     ALTER TABLE account_user
            DROP CONSTRAINT account_user_app_type_national_id_vendor_uniq;
        """)
        schema_editor.execute("""
            CREATE UNIQUE INDEX account_user_app_type_national_id_vendor_uniq
            ON account_user (app_type,national_id,vendor_id_null_to_zero)
            WHERE national_id IS NOT NULL;
        """)

class Migration(migrations.Migration):

    dependencies = [
        ('account', '0119_alter_systempermission_options'),
    ]

    operations = [
        migrations.RunPython(create_filtered_unique_index),
    ]
