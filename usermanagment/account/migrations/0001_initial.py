# Generated by Django 3.0.6 on 2021-12-19 15:33

import django.contrib.gis.db.models.fields
import django.db.models
import django.utils.timezone
import django_countries.fields
import versatileimagefield.fields
from django.conf import settings
from django.db import migrations, models

import usermanagment.account.models
import usermanagment.core.utils.json_serializer


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('email', models.EmailField(max_length=254, unique=True, null=False, blank=False)),
                ('first_name', models.CharField(blank=True, max_length=256)),
                ('last_name', models.CharField(blank=True, max_length=256)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('note', models.TextField(blank=True, null=True)),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('avatar', versatileimagefield.fields.VersatileImageField(blank=True, null=True, upload_to='user-avatars')),
                ('vendor_id', models.IntegerField(blank=True, null=True)),
                ('mobile', usermanagment.account.models.PossiblePhoneNumberField(max_length=128, null=True, region=None, unique=True)),
                ('is_vendor_admin', models.BooleanField(default=False)),
                ('mobile_verified', models.BooleanField(default=False)),
                ('email_verified', models.BooleanField(default=False)),
                ('type', models.CharField(choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'), ('Customer', 'Customer')], max_length=20)),
                ('meeting_platform_id', models.CharField(blank=True, max_length=256, null=True)),
                ('sso_id', models.CharField(blank=True, max_length=256, null=False, unique=True)),
            ],
            options={
                'ordering': ('email',),
                'permissions': (('manage_users', 'Manage users.'), ('manage_staff', 'Manage staff.'), ('view_customers', 'View customers.')),
            },
            managers=[
                ('objects', usermanagment.account.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(blank=True, max_length=256)),
                ('last_name', models.CharField(blank=True, max_length=256)),
                ('company_name', models.CharField(blank=True, max_length=256)),
                ('street_address_1', models.CharField(blank=True, max_length=256)),
                ('street_address_2', models.CharField(blank=True, max_length=256)),
                ('city', models.CharField(blank=True, max_length=256)),
                ('city_area', models.CharField(blank=True, max_length=128)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', django_countries.fields.CountryField(blank=True, default='SA', max_length=2)),
                ('country_area', models.CharField(blank=True, max_length=128)),
                ('phone', usermanagment.account.models.PossiblePhoneNumberField(blank=True, default='', max_length=128, region=None)),
                ('building_number', models.CharField(blank=True, max_length=256, null=True)),
                ('apartment_number', models.CharField(blank=True, max_length=256, null=True)),
                ('coordinates', models.JSONField(blank=True, default=dict, encoder=usermanagment.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('location', django.contrib.gis.db.models.fields.PointField(blank=True, null=True, srid=4326)),
            ],
            options={
                'ordering': ('pk',),
            },
        ),
        migrations.CreateModel(
            name='CustomerNote',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('content', models.TextField()),
                ('is_public', models.BooleanField(default=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('date',),
            },
        ),
        migrations.CreateModel(
            name='CustomerEvent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('type', models.CharField(choices=[('ACCOUNT_CREATED', 'account_created'), ('PASSWORD_RESET_LINK_SENT', 'password_reset_link_sent'), ('PASSWORD_RESET', 'password_reset'), ('EMAIL_CHANGED_REQUEST', 'email_changed_request'), ('PASSWORD_CHANGED', 'password_changed'), ('EMAIL_CHANGED', 'email_changed'), ('PLACED_ORDER', 'placed_order'), ('NOTE_ADDED_TO_ORDER', 'note_added_to_order'), ('DIGITAL_LINK_DOWNLOADED', 'digital_link_downloaded'), ('CUSTOMER_DELETED', 'customer_deleted'), ('NAME_ASSIGNED', 'name_assigned'), ('EMAIL_ASSIGNED', 'email_assigned'), ('NOTE_ADDED', 'note_added')], max_length=255)),
                ('order_id', models.IntegerField(null=True)),
                ('parameters', models.JSONField(blank=True, default=dict, encoder=usermanagment.core.utils.json_serializer.CustomJsonEncoder)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('date',),
            },
        ),
        migrations.CreateModel(
            name='BranchUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('branch_id', models.IntegerField()),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branchuser', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
