# Generated by Django 3.2.23 on 2024-06-12 09:15

from django.db import migrations
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0095_alter_user_full_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='relation_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Son', 'Son'), ('Daughter', 'Daughter'), ('Wife', 'Wife'), ('Husband', 'Husband'), ('Father', 'Father'), ('Mother', 'Mother'), ('Brother', 'Brother'), ('Sister', 'Sister'), ('Grand Father', 'Grand Father'), ('Grand Mother', 'Grand Mother'), ('Other', 'Other')], max_length=20, null=True),
        ),
    ]
