# Generated by Django 3.2.12 on 2023-05-15 11:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0048_alter_user_meeting_platform_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='username',
        ),
        migrations.AddField(
            model_name='user',
            name='vendor_id_null_to_zero',
            field=models.IntegerField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AddConstraint(
            model_name='user',
            constraint=models.UniqueConstraint(
                fields=('app_type', 'mobile', 'vendor_id_null_to_zero'),
                name='account_user_app_type_mobile_vendor_uniq'),
        ),
        migrations.AddConstraint(
            model_name='user',
            constraint=models.UniqueConstraint(
                fields=('app_type', 'national_id', 'vendor_id_null_to_zero'),
                name='account_user_app_type_national_id_vendor_uniq'),
        ),
    ]
