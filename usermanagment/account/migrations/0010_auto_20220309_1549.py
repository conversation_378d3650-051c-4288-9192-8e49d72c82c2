# Generated by Django 3.0.6 on 2022-03-09 13:49

import django.contrib.auth.models
from django.db import migrations, models
import django.db.models.deletion
import usermanagment.auth.enums


class Migration(migrations.Migration):

    dependencies = [
        ('block', '0001_initial'),
        ('auth', '0011_update_proxy_permissions'),
        ('account', '0009_auto_20220124_2015'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemPermission',
            fields=[
            ],
            options={
                'verbose_name': 'permission',
                'permissions': [('manage_users', 'Can Manage Users'), ('manage_staff', 'Can Manage Staff'), ('view_customers', 'Can View Customers'), ('manage_vendors', 'Can Manage vendors'), ('manage_branches', 'Can Manage branches'), ('manage_blocks', 'Can Manage blocks'), ('manage_subscriptions', 'Can Manage subscriptions'), ('manage_wallets', 'Can Manage wallets'), ('manage_chat', 'Can Manage chat'), ('manage_patients', 'Can Manage patients'), ('manage_patient_medical_history', 'Can Manage patient medical history'), ('manage_discounts', 'Can Manage discounts'), ('manage_plugins', 'Can Manage plugins'), ('manage_settings', 'Can Manage settings'), ('manage_translations', 'Can Manage translations'), ('manage_checkouts', 'Can Manage checkouts'), ('manage_orders', 'Can Manage oredrs'), ('manage_products', 'Can Manage products'), ('manage_product_stocks', 'Can Manage product stocks'), ('manage_shipping', 'Can Manage shipping'), ('manage_pricing_rules', 'Can Manage pricing rules'), ('manage_rejection_reasons', 'Can Manage order rejection reasons'), ('manage_invoices', 'Can Manage invoices.'), ('manage_prescriptions', 'Can Manage prescriptions'), ('assign_prescription_order_to_vendors', 'Can assign prescription order to vendors'), ('convert_virtual_order_to_real_one', 'Can convert virtual order to real one'), ('manage_early_refill_reasons', 'Can Manage early refill reasons'), ('manage_payments', 'Can Manage payments'), ('manage_code_system_reviewers', 'Can Manage code system reviewers'), ('publish_code_system', 'Can publish code system'), ('upload_code_system_file', 'Can upload code system file'), ('manage_rule_engine_rules', 'Can Manage rule engine rules')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('auth.permission',),
            managers=[
                ('objects', django.contrib.auth.models.PermissionManager()),
            ],
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ('email',)},
        ),
        migrations.RenameField(
            model_name='address',
            old_name='city_area',
            new_name='area',
        ),
        migrations.RenameField(
            model_name='address',
            old_name='apartment_number',
            new_name='building_name',
        ),
        migrations.RenameField(
            model_name='address',
            old_name='country_area',
            new_name='district',
        ),
        migrations.RemoveField(
            model_name='address',
            name='company_name',
        ),
        migrations.RemoveField(
            model_name='address',
            name='country',
        ),
        migrations.RemoveField(
            model_name='address',
            name='first_name',
        ),
        migrations.RemoveField(
            model_name='address',
            name='last_name',
        ),
        migrations.AddField(
            model_name='address',
            name='region',
            field=models.CharField(blank=True, max_length=128),
        ),
        migrations.AddField(
            model_name='address',
            name='unit_number',
            field=models.CharField(blank=True, max_length=256, null=True),
        ),
        migrations.AlterField(
            model_name='address',
            name='city',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to='block.City'),
        ),
        migrations.AlterField(
            model_name='user',
            name='app_type',
            field=models.CharField(choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'), ('Customer', 'Customer'), ('Aggregator', 'Aggregator')], default=usermanagment.auth.enums.AppTypes['CUSTOMER'], max_length=20),
        ),
        migrations.CreateModel(
            name='AddressTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=10)),
                ('street_address_1', models.CharField(max_length=255)),
                ('street_address_2', models.CharField(max_length=255)),
                ('area', models.CharField(max_length=255)),
                ('district', models.CharField(max_length=255)),
                ('region', models.CharField(max_length=255)),
                ('building_name', models.CharField(max_length=255)),
                ('address', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='account.Address')),
            ],
            options={
                'unique_together': {('language_code', 'address')},
            },
        ),
    ]
