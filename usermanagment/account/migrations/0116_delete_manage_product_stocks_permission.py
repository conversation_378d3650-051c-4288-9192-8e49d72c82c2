from django.db import migrations


def delete_permission(apps, schema_editor):
    AuthPermission = apps.get_model('auth', 'Permission')
    AuthGroupPermissions = apps.get_model('auth', 'Group_permissions')
    KeycloakPermission = apps.get_model('keycloak_permissions', 'KeycloakPermission')

    try:
        # Retrieve the auth_permission object to delete
        permission = AuthPermission.objects.get(codename='manage_product_stocks')

        # Delete related entries in keycloak_permissions_keycloakpermission
        KeycloakPermission.objects.filter(permission=permission).delete()

        # Remove the Many-to-Many relationships with auth_group
        AuthGroupPermissions.objects.filter(permission=permission).delete()

        # Delete the auth_permission object
        permission.delete()

    except AuthPermission.DoesNotExist:
        # Permission does not exist, skip deletion
        pass


class Migration(migrations.Migration):
    dependencies = [
        ('account', '0115_alter_systempermission_options'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('keycloak_permissions', '0004_groupconfiguration_payer'),
    ]

    operations = [
        migrations.RunPython(delete_permission),
    ]
