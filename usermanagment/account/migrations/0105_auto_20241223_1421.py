# Generated by Django 3.2.25 on 2024-12-23 12:21

from django.db import migrations, models
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0104_alter_systempermission_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='two_factor_auth_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='two_factor_auth_secret',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='two_factor_auth_verification_method',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('email', 'Email'), ('mobile', 'Mobile'), ('authenticator', 'Authenticator')], max_length=20, null=True),
        ),
    ]
