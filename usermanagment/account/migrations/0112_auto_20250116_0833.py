# Generated by Django 3.2.25 on 2025-01-16 08:33

from django.db import migrations
import usermanagment.auth.enums


class Migration(migrations.Migration):
    dependencies = [
        ('account', '0110_alter_systempermission_options'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='systempermission',
            options={'permissions': [('manage_users', 'Can Manage Users'),
                                     ('manage_staff', 'Can Manage Staff'), (
                                     'manage_permission_group',
                                     'Can Manage Permission Groups'),
                                     ('view_customers', 'Can View Customers'),
                                     ('view_users', 'Can View Users'),
                                     ('view_staff', 'Can View Staff'),
                                     ('manage_languages', 'Can Manage Languages'),
                                     ('manage_vendors', 'Can Manage vendors'),
                                     ('manage_departments', 'Can Manage departments'),
                                     ('manage_branches', 'Can Manage branches'),
                                     ('manage_blocks', 'Can Manage blocks'),
                                     ('manage_cities', 'Can Manage cities'), (
                                     'manage_subscriptions',
                                     'Can Manage subscriptions'),
                                     ('view_subscriptions', 'Can View subscriptions'),
                                     ('manage_wallets', 'Can Manage wallets'),
                                     ('view_wallets', 'Can View wallets'),
                                     ('manage_chat', 'Can Manage chat'),
                                     ('manage_patients', 'Can Manage patients'),
                                     ('view_patients', 'Can View patients'),
                                     ('verify_national_ids', 'Can Verify National IDs'),
                                     ('verify_member_ids', 'Can Verify Member IDs'), (
                                     'manage_medical_edits',
                                     'Can Manage medical edits'),
                                     ('validate_procedures', 'Can Validate procedures'),
                                     ('manage_patient_medical_history',
                                      'Can Manage patient medical history'),
                                     ('manage_doctors', 'Can Manage doctors'), (
                                     'manage_health_conditions',
                                     ['Can manage health conditions',
                                      usermanagment.auth.enums.AppTypes['ADMIN']]), (
                                     'manage_health_symptoms',
                                     ['Can manage health symptoms',
                                      usermanagment.auth.enums.AppTypes['ADMIN']]), (
                                     'manage_specializations',
                                     'Can Manage subscriptions'), (
                                     'manage_qualifications',
                                     'Can Manage qualifications'),
                                     ('manage_discounts', 'Can Manage discounts'),
                                     ('manage_plugins', 'Can Manage plugins'),
                                     ('manage_settings', 'Can Manage settings'),
                                     ('manage_translations', 'Can Manage translations'),
                                     ('manage_dashboard', 'Can Manage dashboard'),
                                     ('manage_checkouts', 'Can Manage checkouts'),
                                     ('manage_orders', 'Can Manage orders'), (
                                     'manage_health_package_orders',
                                     'Can Manage health package orders'),
                                     ('view_orders', 'Can View orders'), (
                                     'manage_delivery_time_slots',
                                     'Can Manage for delivery time slots'), (
                                     'customer_support_manage_orders',
                                     'Customer Support Manage orders'), (
                                     'manage_marketplace_orders',
                                     'Can Manage marketplace orders'),
                                     ('manage_products', 'Can Manage products'),
                                     ('approve_products', 'Can Approve products'), (
                                     'manage_product_categories',
                                     'Can Manage product categories'),
                                     ('manage_brands', 'Can Manage brands'),
                                     ('manage_shipping', 'Can Manage shipping'), (
                                     'manage_pricing_rules',
                                     'Can Manage pricing rules'), (
                                     'manage_rejection_reasons',
                                     'Can Manage order rejection reasons'),
                                     ('manage_invoices', 'Can Manage invoices.'), (
                                     'manage_prescriptions',
                                     'Can Manage prescriptions'), (
                                     'assign_prescription_order_to_vendors',
                                     'Can assign prescription order to vendors'), (
                                     'convert_virtual_order_to_real_one',
                                     'Can convert virtual order to real one'), (
                                     'manage_early_refill_reasons',
                                     'Can Manage early refill reasons'), (
                                     'manage_activity_tracker',
                                     'Can Manage Activity Tracker'), (
                                     'manage_medical_delivery_requests',
                                     'Can Manage medical delivery requests'), (
                                     'manage_health_packages',
                                     'Can Manage health packages'), (
                                     'approve_health_packages',
                                     'Can Approve health packages'), (
                                     'manage_health_package_categories',
                                     'Can Manage health package categories'),
                                     ('manage_payments', 'Can Manage payments'), (
                                     'manage_code_system_reviewers',
                                     'Can Manage code system reviewers'),
                                     ('publish_code_system', 'Can Publish code system'),
                                     ('upload_code_system_file',
                                      'Can Upload code system file'), (
                                     'manage_code_system_editor_reviewers',
                                     'Can Manage code system editor/reviewer'), (
                                     'manage_code_system_lists',
                                     'Can Manage code system lists'), (
                                     'view_code_system_lists',
                                     'Can View code system lists'), (
                                     'manage_rule_engine_rules',
                                     'Can Manage rule engine rules'), (
                                     'manage_risk_stratification',
                                     'Can Manage risk stratification'),
                                     ('manage_labs', 'Can Manage labs'),
                                     ('manage_parameters', 'Can Manage parameters'), (
                                     'workflow_manage_orders',
                                     'Can Manage Orders lifecycle'), (
                                     'manage_pharmacy_credentials',
                                     'Can Manage pharmacy credentials'), (
                                     'manage_health_programs',
                                     'Can Manage health programs'), (
                                     'manage_health_programs_care_for_fields',
                                     'Can Manage health programs care for fields'),
                                     ('manage_visits', 'Can Manage visits'), (
                                     'manage_out_patient_journeys',
                                     'Can Manage out patient journeys'), (
                                     'manage_visit_rejection_reasons',
                                     'Can Manage visit rejection reasons'), (
                                     'manage_visit_cancel_reasons',
                                     'Can Manage visit cancel reasons'),
                                     ('manage_medications', 'Can Manage medications'),
                                     ('manage_diagnosis', 'Can Manage diagnosis'), (
                                     'manage_scientific_details',
                                     'Can Manage scientific details'), (
                                     'manage_visit_summary',
                                     'Can Manage visit summary'), (
                                     'manage_health_channels',
                                     'Can Manage health channels'), (
                                     'manage_health_channels_categories',
                                     'Can Manage health channels categories'), (
                                     'medication_scientific_details',
                                     'Can Manage Medication Scientific Details'), (
                                     'manage_program_templates',
                                     'Can Manage Program Templates'),
                                     ('manage_programs', 'Can Manage Programs'), (
                                     'manage_program_teams',
                                     'Can Manage Program Teams'), (
                                     'view_program_templates',
                                     'Can View Program Templates'),
                                     ('view_programs', 'Can View Programs'),
                                     ('view_program_teams', 'Can View Program Teams'), (
                                     'manage_callbacks_requests',
                                     'Can Manage Callbacks Requests'), (
                                     'manage_medical_forms',
                                     'Can Manage Medical Forms'), (
                                     'manage_health_messages',
                                     'Can Manage Health Messages'),
                                     ('view_visits', 'Can View Visits'),
                                     ('view_appointments', 'Can View Appointments'), (
                                     'view_health_programs',
                                     'Can View Health Programs'), (
                                     'manage_social_and_streaming',
                                     'Can Manage Social and Streaming'), (
                                     'view_patient_enrollment_request',
                                     'Can View Patient Enrollment Request'), (
                                     'manage_guided_care_patients',
                                     'Can Manage Guided Care Patients'),
                                     ('manage_insurance', 'Can Manage insurances'), (
                                     'manage_insurance_networks',
                                     'Can Manage insurance networks'),
                                     ('manage_surveys', 'Can Manage surveys'),
                                     ('view_surveys', 'Can View surveys'),
                                     ('manage_appointments', 'Can Manage appointments'),
                                     ('manage_promotions', 'Can Manage promotions'), (
                                     'manage_admin_promotions',
                                     'Can Manage admin promotions'),
                                     ('manage_chat_flow', 'Can Manage chat flow'), (
                                     'manage_chat_flow_contributer',
                                     'Can Manage chat flow contributer'),
                                     ('manage_articles', 'Can Manage articles'), (
                                     'manage_edits_and_actions',
                                     'Can Manage Edits and Actions'),
                                     ('view_transaction', 'Can View Transaction'), (
                                     'manage_validation_requests',
                                     'Can Manage Validation Requests'), (
                                     'manage_medical_necessity',
                                     'Can Manage Medical Necessity'), (
                                     'manage_optima_payer_credentials',
                                     'Can Manage Optima Payer Credentials'),
                                     ('manage_payers', 'Can Manage payers')],
                     'verbose_name': 'permission'},
        ),
    ]
