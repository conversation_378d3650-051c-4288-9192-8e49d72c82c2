# Generated by Django 3.2.25 on 2025-07-31 08:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0140_bulkmessagerequest'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='first_name',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='first_name_ar',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='full_name',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='full_name_ar',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='last_name',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='last_name_ar',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='user',
            name='second_name',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='second_name_ar',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='third_name',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='third_name_ar',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
