# Generated by Django 3.2.12 on 2023-10-03 12:09

from django.db import migrations, models
import usermanagment.account.models.possible_phone_number
import usermanagment.auth.enums
import usermanagment.core.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0073_alter_systempermission_options'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='app_role',
            field=usermanagment.core.db.fields.EnumField(choices=[('Admin', 'Admin'), ('User', 'User')], db_index=True, default=usermanagment.auth.enums.AppRoleTypes['USER'], max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='app_type',
            field=usermanagment.core.db.fields.EnumField(choices=[('Admin', 'Admin'), ('Vendor', 'Vendor'), ('Customer', 'Customer'), ('Aggregator', 'Aggregator'), ('Payer', 'Payer')], db_index=True, default=usermanagment.auth.enums.AppTypes['CUSTOMER'], max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='mobile',
            field=usermanagment.account.models.possible_phone_number.PossiblePhoneNumberField(blank=True, db_index=True, max_length=128, null=True, region=None),
        ),
        migrations.AlterField(
            model_name='user',
            name='national_id',
            field=models.CharField(blank=True, db_index=True, max_length=30, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='vendor_user_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[('Manager', 'Manager'), ('Pharmacist', 'Pharmacist'), ('Nurse', 'Nurse'), ('Doctor', 'Doctor')], db_index=True, max_length=20, null=True),
        ),
    ]
