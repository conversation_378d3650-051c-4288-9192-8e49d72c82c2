# Generated by Django 3.2.12 on 2023-09-11 12:40

from django.db import migrations

LANGUAGES = [
    ("ar", "Arabic", "العربية"),
    ("az", "Azerbaijani", "الأذربيجانية"),
    ("bg", "Bulgarian", "البلغرية"),
    ("bn", "Bengali", "البنغالية"),
    ("ca", "Catalan", "الكتالونية"),
    ("cs", "Czech", "التشيكية"),
    ("da", "Danish", "الدانماركية"),
    ("de", "German", "الألمانية"),
    ("el", "Greek", "اليونانية"),
    ("en", "English", "الإنجليزية"),
    ("es", "Spanish", "الأسبانية"),
    ("es-co", "Colombian Spanish", "الأسبانية الكولومبية"),
    ("et", "Estonian", "الإستونية"),
    ("fa", "Persian", "الفارسية"),
    ("fi", "Finnish", "الفنلندية"),
    ("fr", "French", "الفرنسية"),
    ("hi", "Hindi", "الهندية"),
    ("hu", "Hungarian", "الهنغارية"),
    ("hy", "Armenian", "الأرمينية"),
    ("id", "Indonesian", "الإندونيسية"),
    ("is", "Icelandic", "الأيسلندية"),
    ("it", "Italian", "الإيطالية"),
    ("ja", "Japanese", "اليابانية"),
    ("ko", "Korean", "الكورية"),
    ("lt", "Lithuanian", "الليتوانية"),
    ("mn", "Mongolian", "المنغولية"),
    ("nb", "Norwegian", "النرويجية"),
    ("nl", "Dutch", "الهولندية"),
    ("pl", "Polish", "البولندية"),
    ("pt", "Portuguese", "البرتغالية"),
    ("pt-br", "Brazilian Portuguese", "البرتغالية البرازيلية"),
    ("ro", "Romanian", "الرومانية"),
    ("ru", "Russian", "الروسية"),
    ("sk", "Slovak", "السلوفاكية"),
    ("sl", "Slovenian", "السلوفينية"),
    ("sq", "Albanian", "الألبانية"),
    ("sr", "Serbian", "الصربية"),
    ("sv", "Swedish", "السويدية"),
    ("sw", "Swahili", "السواحلية"),
    ("ta", "Tamil", "التاميلية"),
    ("th", "Thai", "التايلاندية"),
    ("tr", "Turkish", "التركية"),
    ("uk", "Ukrainian", "الأوكرانية"),
    ("vi", "Vietnamese", "الفيتنامية"),
    ("zh-hans", "Simplified Chinese", "الصينية المبسطة"),
    ("zh-hant", "Traditional Chinese", "الصينية التقليدية"),
]


def populate_language_table(apps, schema_editor):
    Language = apps.get_model("account", "Language")
    db_alias = schema_editor.connection.alias
    default_languages = [
        Language(code=language[0], display=language[1], display_ar=language[2]) for
        language in LANGUAGES]

    Language.objects.using(db_alias).bulk_create(default_languages)


def clear_language_table(apps, schema_editor):
    Language = apps.get_model("account", "Language")
    db_alias = schema_editor.connection.alias
    Language.objects.using(db_alias).all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('account', '0070_auto_20230911_0616'),
    ]

    operations = [
        migrations.RunPython(populate_language_table, clear_language_table)
    ]
