# Generated by Django 3.2.12 on 2023-06-06 08:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0051_user_parent_user'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='user',
            name='full_name',
            field=models.Char<PERSON>ield(blank=True, max_length=600, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='first_name',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='last_name',
            field=models.Char<PERSON>ield(blank=True, max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='second_name',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='third_name',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=150, null=True),
        ),
        migrations.RunSQL(
            sql="""
            update account_user  set full_name=
            CONCAT_WS(' ',first_name,second_name,third_name,last_name)
            """
        ),
    ]
