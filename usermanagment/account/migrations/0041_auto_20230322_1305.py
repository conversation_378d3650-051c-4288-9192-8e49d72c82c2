# Generated by Django 3.2.12 on 2023-03-22 13:05

from django.db import migrations, models
import usermanagment.core.db.fields
import versatileimagefield.fields


class Migration(migrations.Migration):
    dependencies = [
        ('account', '0040_user_payer'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='gender',
            field=models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')],
                                   max_length=6, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='photo',
            field=versatileimagefield.fields.VersatileImageField(blank=True, null=True,
                                                                 upload_to='doctor-photos'),
        ),
        migrations.AddField(
            model_name='user',
            name='second_name',
            field=models.CharField(blank=True, max_length=256, null=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='user',
            name='third_name',
            field=models.Char<PERSON>ield(blank=True, max_length=256, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='vendor_type',
            field=usermanagment.core.db.fields.EnumField(blank=True, choices=[
                ('Pharmacist', 'Pharmacist'), ('Nurse', 'Nurse'), ('Doctor', 'Doctor')],
                                                         max_length=20, null=True),
        ),
    ]
