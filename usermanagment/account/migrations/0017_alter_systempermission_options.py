# Generated by Django 3.2.12 on 2022-04-26 20:55

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0016_auto_20220410_1017'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='systempermission',
            options={'permissions': [('manage_users', 'Can Manage Users'), ('manage_staff', 'Can Manage Staff'), ('view_customers', 'Can View Customers'), ('manage_vendors', 'Can Manage vendors'), ('manage_branches', 'Can Manage branches'), ('manage_blocks', 'Can Manage blocks'), ('manage_subscriptions', 'Can Manage subscriptions'), ('manage_wallets', 'Can Manage wallets'), ('manage_chat', 'Can Manage chat'), ('manage_patients', 'Can Manage patients'), ('manage_patient_medical_history', 'Can Manage patient medical history'), ('manage_discounts', 'Can Manage discounts'), ('manage_plugins', 'Can Manage plugins'), ('manage_settings', 'Can Manage settings'), ('manage_translations', 'Can Manage translations'), ('manage_checkouts', 'Can Manage checkouts'), ('manage_orders', 'Can Manage oredrs'), ('manage_products', 'Can Manage products'), ('manage_product_stocks', 'Can Manage product stocks'), ('manage_shipping', 'Can Manage shipping'), ('manage_pricing_rules', 'Can Manage pricing rules'), ('manage_rejection_reasons', 'Can Manage order rejection reasons'), ('manage_invoices', 'Can Manage invoices.'), ('manage_prescriptions', 'Can Manage prescriptions'), ('assign_prescription_order_to_vendors', 'Can assign prescription order to vendors'), ('convert_virtual_order_to_real_one', 'Can convert virtual order to real one'), ('manage_early_refill_reasons', 'Can Manage early refill reasons'), ('manage_payments', 'Can Manage payments'), ('manage_code_system_reviewers', 'Can Manage code system reviewers'), ('publish_code_system', 'Can publish code system'), ('upload_code_system_file', 'Can upload code system file'), ('manage_code_system_editor_reviewers', 'Can manage code system editor/reviewer'), ('manage_rule_engine_rules', 'Can Manage rule engine rules'), ('workflow_manage_orders', 'Can Manage Orders lifecycle')], 'verbose_name': 'permission'},
        ),
    ]
