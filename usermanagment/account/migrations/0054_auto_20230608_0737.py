# Generated by Django 3.2.12 on 2023-06-08 07:37

from django.db import migrations, models
import django.db.models.deletion
import usermanagment.core.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ('vendor', '0031_auto_20230525_0932'),
        ('account', '0053_alter_systempermission_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='emailphoneverification',
            name='app_type',
            field=usermanagment.core.db.fields.EnumField(blank=True,
                                                         choices=[('Admin', 'Admin'),
                                                                  ('Vendor', 'Vendor'),
                                                                  ('Customer',
                                                                   'Customer'), (
                                                                  'Aggregator',
                                                                  'Aggregator'),
                                                                  ('Payer', 'Payer')],
                                                         max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='emailphoneverification',
            name='vendor',
            field=models.ForeignKey(blank=True, null=True,
                                    on_delete=django.db.models.deletion.CASCADE,
                                    to='vendor.vendor'),
        ),
    ]
