from django.db.models import TextChoices


class CustomerEvents:
    """The different customer event types."""

    # Account related events
    ACCOUNT_CREATED = "account_created"
    ACCOUNT_CREATED_FROM_SSO = "account_created_from_sso"
    PASSWORD_RESET_LINK_SENT = "password_reset_link_sent"
    PASSWORD_RESET = "password_reset"
    PASSWORD_CHANGED = "password_changed"
    EMAIL_CHANGE_REQUEST = "email_changed_request"
    EMAIL_CHANGED = "email_changed"

    # Order related events
    PLACED_ORDER = "placed_order"  # created an order
    NOTE_ADDED_TO_ORDER = "note_added_to_order"  # added a note to one of their orders
    DIGITAL_LINK_DOWNLOADED = "digital_link_downloaded"  # downloaded a digital good

    # Staff actions over customers events
    CUSTOMER_DELETED = "customer_deleted"  # staff user deleted a customer
    EMAIL_ASSIGNED = "email_assigned"  # the staff user assigned a email to the customer
    NAME_ASSIGNED = "name_assigned"  # the staff user added set a name to the customer
    NOTE_ADDED = "note_added"  # the staff user added a note to the customer

    CHOICES = [
        (ACCOUNT_CREATED, "The account was created"),
        (ACCOUNT_CREATED_FROM_SSO, "The account was created from the sso"),
        (PASSWORD_RESET_LINK_SENT, "Password reset link was sent to the customer"),
        (PASSWORD_RESET, "The account password was reset"),
        (
            EMAIL_CHANGE_REQUEST,
            "The user requested to change the account's email address.",
        ),
        (PASSWORD_CHANGED, "The account password was changed"),
        (EMAIL_CHANGED, "The account email address was changed"),
        (PLACED_ORDER, "An order was placed"),
        (NOTE_ADDED_TO_ORDER, "A note was added"),
        (DIGITAL_LINK_DOWNLOADED, "A digital good was downloaded"),
        (CUSTOMER_DELETED, "A customer was deleted"),
        (NAME_ASSIGNED, "A customer's name was edited"),
        (EMAIL_ASSIGNED, "A customer's email address was edited"),
        (NOTE_ADDED, "A note was added to the customer"),
    ]

class TwoAuthVerificationMethod:
    EMAIL = "email"
    MOBILE = "mobile"
    AUTHENTICATOR = "authenticator"

    CHOICES = [
        (EMAIL, "Email"),
        (MOBILE, "Mobile"),
        (AUTHENTICATOR, "Authenticator"),
    ]

class AddressType:
    BILLING = "billing"
    SHIPPING = "shipping"

    CHOICES = [
        (BILLING, "Billing"),
        (SHIPPING, "Shipping"),
    ]


ADDRESS_TRANSLATABLE_FIELDS = ['name', 'street_address_1', 'street_address_2', 'area',
                               'district', 'region', 'building_name']


class PersonGender(TextChoices):
    MALE = "Male"
    FEMALE = "Female"


class PasswordResetMethod(TextChoices):
    EMAIL = "email"
    MOBILE = "mobile"


class SendBulkMessagesMethod(TextChoices):
    EMAIL = "email"
    MOBILE = "mobile"


class OTPOperation(TextChoices):
    PATIENT_REGISTRATION = "patient_registration"
    TWO_FACTOR_AUTHENTICATION = "two_factor_authentication"
    CHANGE_EMAIL_MOBILE = "change_email_mobile"
    LOGIN_BY_OTP = "login_by_otp"
