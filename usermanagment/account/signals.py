from threading import Thread

from django.contrib.auth.models import Group
from django.db.models.signals import pre_delete, post_save
from django.dispatch import receiver
from django.utils.timezone import now
from graphene import Node

from .models import User, GroupAudit
from ..auth.enums import VendorUserTypes
from ..doctor.models import Doctor
from ..open_search.open_search_service import OpenSearchService


def sync_doctor(instance):
    doctor = Doctor.objects.get(user=instance)
    open_search_service = OpenSearchService()
    open_search_service.sync_doctors([doctor])


@receiver(post_save, sender=User)
def open_search_save_sync(sender, instance: User, **kwargs):
    if instance.vendor_user_type and instance.vendor_user_type == VendorUserTypes.DOCTOR:
        Thread(target=sync_doctor,
               args=(instance,), daemon=False).start()


def delete_doctor(instance):
    doctor = Doctor.objects.get(user=instance)
    id = Node.to_global_id("Doctor", doctor.id)
    open_search_service = OpenSearchService()
    open_search_service.delete(id)


@receiver(pre_delete, sender=User)
def open_search_delete_sync(sender, instance: User, **kwargs):
    if instance.vendor_user_type and instance.vendor_user_type == VendorUserTypes.DOCTOR:
        Thread(target=delete_doctor,
               args=(instance,), daemon=False).start()


@receiver(post_save, sender=Group)
def create_or_update_group_audit(sender, instance, created, **kwargs):
    if created or not hasattr(instance, 'audit') or instance.audit is None:
        GroupAudit.objects.create(group=instance)
        instance.audit.modified = now()
        instance.audit.save()
    else:
        # When an existing group is updated
        instance.audit.modified = now()
        instance.audit.save()
