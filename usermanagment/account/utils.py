import logging

import jwt
from django.conf import settings
from django.contrib.auth import models as auth_models
from django.contrib.gis.db.models.functions import Intersection
from django.contrib.gis.geos import Point
from django.core.exceptions import ValidationError
from django.db.models import F
from django.utils import timezone

from . import AddressType
from ..account.error_codes import AccountErrorCode
from ..block.models import Block
from ..core.permissions import (VENDOR_ADMIN_AUTH_GROUP_NAME,
                                PHARMACIST_USER_AUTH_GROUP_NAME,
                                DOCTOR_USER_AUTH_GROUP_NAME,
                                NURSE_USER_AUTH_GROUP_NAME,
                                PAYER_USER_AUTH_GROUP_NAME,
                                PAYER_ADMIN_AUTH_GROUP_NAME,
                                PATIENT_USER_AUTH_GROUP_NAME,
                                RECEPTIONIST_USER_AUTH_GROUP_NAME,
                                DIABETES_EDUCATOR_USER_AUTH_GROUP_NAME,
                                FITNESS_COACH_USER_AUTH_GROUP_NAME,
                                DENTAL_HYGIENIST_USER_AUTH_GROUP_NAME,
                                PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_NAME,
                                SOCIAL_WORKER_USER_AUTH_GROUP_NAME,
                                PSYCHOLOGIST_USER_AUTH_GROUP_NAME,
                                OPTOMETRIST_USER_AUTH_GROUP_NAME,
                                NUTRITIONIST_USER_AUTH_GROUP_NAME, RCM_AUTH_GROUP_NAME)
from ..map.google_maps_service import GoogleMapService

logger = logging.getLogger(__name__)


def store_user_address(user, address, address_type):
    """Add address to user address book and set as default one."""
    address_data = address.as_data()

    address = user.addresses.filter(**address_data).first()
    if address is None:
        address = user.addresses.create(**address_data)

    if address_type == AddressType.BILLING:
        if not user.default_billing_address:
            set_user_default_billing_address(user, address)
    elif address_type == AddressType.SHIPPING:
        if not user.default_shipping_address:
            set_user_default_shipping_address(user, address)


def set_user_default_billing_address(user, address):
    user.default_billing_address = address
    user.save(update_fields=["default_billing_address"])


def set_user_default_shipping_address(user, address):
    user.default_shipping_address = address
    user.save(update_fields=["default_shipping_address"])


def change_user_default_address(user, address, address_type):
    if address_type == AddressType.BILLING:
        if user.default_billing_address:
            user.addresses.add(user.default_billing_address)
        set_user_default_billing_address(user, address)
    elif address_type == AddressType.SHIPPING:
        if user.default_shipping_address:
            user.addresses.add(user.default_shipping_address)
        set_user_default_shipping_address(user, address)


def create_jwt_token(token_data):
    expiration_date = timezone.now() + timezone.timedelta(hours=1)
    token_kwargs = {"exp": expiration_date}
    token_kwargs.update(token_data)
    token = jwt.encode(token_kwargs, settings.SECRET_KEY, algorithm="HS512").decode()
    return token


def decode_jwt_token(token):
    try:
        decoded_token = jwt.decode(
            token.encode(), settings.SECRET_KEY, algorithms=["HS512"]
        )
    except jwt.PyJWTError:
        raise ValidationError(
            {
                "token": ValidationError(
                    "Invalid or expired token.", code=AccountErrorCode.INVALID
                )
            }
        )
    return decoded_token


def get_address_coordinates_from_google(address):
    """Call google geocode API to map address to lat/lng"""
    logger.debug("getting address-{} location".format(address.pk))
    coordinates = address.coordinates if address.coordinates else GoogleMapService().from_address_to_long_lat(
        address)
    location = None
    if coordinates:
        try:
            location = Point(x=coordinates['lng'], y=coordinates['lat'], srid=4326)
        except Exception :
            pass
    address.coordinates = coordinates
    address.location = location
    return coordinates, location


def get_super_vendor_group():
    return auth_models.Group.objects.get(name=VENDOR_ADMIN_AUTH_GROUP_NAME)


def get_pharmacist_vendor_group():
    return auth_models.Group.objects.get(name=PHARMACIST_USER_AUTH_GROUP_NAME)


def get_doctor_group():
    return auth_models.Group.objects.get(name=DOCTOR_USER_AUTH_GROUP_NAME)


def get_dental_hygienist():
    return auth_models.Group.objects.get(name=DENTAL_HYGIENIST_USER_AUTH_GROUP_NAME)


def get_diabetes_educator():
    return auth_models.Group.objects.get(name=DIABETES_EDUCATOR_USER_AUTH_GROUP_NAME)


def get_fitness_coach():
    return auth_models.Group.objects.get(name=FITNESS_COACH_USER_AUTH_GROUP_NAME)


def get_nutritionist():
    return auth_models.Group.objects.get(name=NUTRITIONIST_USER_AUTH_GROUP_NAME)


def get_optometrist():
    return auth_models.Group.objects.get(name=OPTOMETRIST_USER_AUTH_GROUP_NAME)


def get_podiatric_medical_assistant():
    return auth_models.Group.objects.get(
        name=PODIATRIC_MEDICAL_ASSISTANT_USER_AUTH_GROUP_NAME)


def get_psychologist():
    return auth_models.Group.objects.get(name=PSYCHOLOGIST_USER_AUTH_GROUP_NAME)


def get_social_worker():
    return auth_models.Group.objects.get(name=SOCIAL_WORKER_USER_AUTH_GROUP_NAME)

def get_rcm_group():
    return auth_models.Group.objects.get(name=RCM_AUTH_GROUP_NAME)


def get_nurse_group():
    return auth_models.Group.objects.get(name=NURSE_USER_AUTH_GROUP_NAME)


def get_receptionist_group():
    return auth_models.Group.objects.get(name=RECEPTIONIST_USER_AUTH_GROUP_NAME)


def get_payer_user_group():
    return auth_models.Group.objects.get(name=PAYER_USER_AUTH_GROUP_NAME)


def get_payer_admin_group():
    return auth_models.Group.objects.get(name=PAYER_ADMIN_AUTH_GROUP_NAME)


def get_patient_group():
    return auth_models.Group.objects.get(name=PATIENT_USER_AUTH_GROUP_NAME)

# def create_group(name, permissions, users):
#     group, _ = auth_models.Group.objects.get_or_create(name=name)
#     group.permissions.add(*permissions)
#     group.user_set.add(*users)
#     return group
