import logging

from django.contrib.sites.models import Site

from usermanagment.kafka.data_class.notification import SmsNotification
from usermanagment.kafka.kafka_messagin_service import KafkaMessageService

logger = logging.getLogger(__name__)


def generate_and_send_order_mobile_otp(user,phone_number, patient_name, patient_id,
                                       order_id, branch_name,
                                       order_otp, url):
    logger.debug(f"sending SMS order {order_id} OTP confirmation to "
                 f"{phone_number}, patient users id {patient_id}")

    site_settings = Site.objects.get_current().settings

    if user.preferred_language and user.preferred_language.code == 'ar':
        if user.email:
            message = site_settings.patient_order_otp_email_message_ar
        else:
            message = site_settings.patient_order_otp_sms_message_ar
    else:
        if user.email:
            message = site_settings.patient_order_otp_email_message
        else:
            message = site_settings.patient_order_otp_sms_message_ar

    message = message.format(
        patient_name=patient_name,
        order_id=order_id,
        branch_name=branch_name,
        order_otp=order_otp,
        url=url
    )
    send_sms(phone_number, message, user_id=patient_id)


def send_sms(to, body, user_id: int = None):
    to = str(to)
    try:
        KafkaMessageService().send_sms_notification_message(
            user_id,
            SmsNotification(
                recipient_mobile_number=to,
                body=body
            )
        )
    except Exception as error:
        logger.error("Error while calling sms backend API", exc_info=error)
