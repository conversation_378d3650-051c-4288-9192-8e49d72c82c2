import logging

from usermanagment.kafka.data_class.notification import EmailNotification
from usermanagment.kafka.kafka_messagin_service import KafkaMessageService

logger = logging.getLogger(__name__)


def send_email(to, title, body, user_id: int = None):
    to = str(to)
    try:
        KafkaMessageService().send_email_notification_message(
            user_id,
            EmailNotification(
                recipient_email=to,
                title=title,
                body=body
            )
        )
    except Exception as error:
        logger.error("Error while calling email backend API", exc_info=error)
