[uwsgi]
die-on-term = true
http-socket = :$(PORT)
log-format = UWSGI uwsgi "%(method) %(uri) %(proto)" %(status) %(size) %(msecs)ms [PID:%(pid):Worker-%(wid)] [RSS:%(rssM)MB]
master = true
max-requests = 100
memory-report = true
module = usermanagment.wsgi:application
processes = 4
static-map = /static=/app/static
mimefile = /etc/mime.types
ignore-sigpipe = true
ignore-write-errors = true
disable-write-exception=true
