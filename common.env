
DB_HOST=host.docker.internal
DB_USER=root
DB_PASS=Test@12345
ENV=test
DB_TYPE=mysql

DEFAULT_FROM_EMAIL=<EMAIL>
SECRET_KEY=changeme

GOOGLE_APP_KEY=

K<PERSON><PERSON><PERSON><PERSON><PERSON>_SERVER_URL=http://keycloak:9080
KE<PERSON>CLOAK_REALM=test-sehhati
KEYCLOAK_CLIENT_ID=
KEYCLOAK_CLIENT_SECRET_KEY=
KEYCLOAK_ADMIN_CLIENT_ID=
KEYCLOAK_ADMIN_USERNAME=
KEYCLOAK_ADMIN_PASSWORD=

LOAD_CUSTOM_SETTINGS=True

FEDERATED_BACKEND_URL=
FEDERATED_BACKEND_ADMIN_USERNAME=
FEDERATED_BACKEND_ADMIN_PASSWORD=

TOKEN_SIG_PRIVATE_KEY_PATH=

KAFKA_BOOTSTRAP_SERVERS=

PASSWORD_SET_LINK=

REDIS_URL=


GOOGLE_CHAT_SPACE_URL=
