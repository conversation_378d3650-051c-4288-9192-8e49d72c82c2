# ARGO CI
name: Argo Deployment

on:
  push:
    tags:
      - sehatuk.release.**
jobs:
  build:
    uses: my-workforce/ci-cd-actions/.github/workflows/build-deploy-k8s.yml@main
    with:
      docker-image: user-management
      kustomize-path: overlays/io/test/user-management
    secrets:
      NEW_DOCKER_REGISTRY_USERNAME: ${{ secrets.NEW_DOCKER_REGISTRY_USERNAME }}
      NEW_DOCKER_REGISTRY_PASSWORD: ${{ secrets.NEW_DOCKER_REGISTRY_PASSWORD }}
      GIT_TOKEN: ${{ secrets.GIT_TOKEN }}
