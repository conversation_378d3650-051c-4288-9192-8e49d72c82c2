#!/usr/bin/bash

SUCCESS=0
PERMISSION_ERROR=1
GIT_PULL_ERROR=2
BUILD_ERROR=3
DOCKER_ERROR=4


if ! sudo chown -R ubuntu:ubuntu .; then
    echo "Failed to set permissions"
    exit $PERMISSION_ERROR
fi

if ! git pull; then
    echo "Failed to pull latest changes"
    exit $GIT_PULL_ERROR
fi

if ! docker-compose build; then
    echo "Failed to build the service"
    exit $BUILD_ERROR
fi

if ! docker-compose run -e --rm usermanagement python3 manage.py migrate; then
  echo "Failed to migrate usermanagement"
  exit $DOCKER_ERROR
fi

if ! docker-compose up -d; then
    echo "Failed to start the service"
    exit $DOCKER_ERROR
fi

exit $SUCCESS
