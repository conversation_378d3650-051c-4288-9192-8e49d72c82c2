version: '2.3'

services:
  usermanagement:
    ports:
      - "8001:8000"
    build: .
    restart: unless-stopped
    networks:
      - sehacity-backend-tier
    volumes:
      - ./usermanagment/:/app/usermanagment:Z
      - ./templates/:/app/templates:Z
      - /keys/:/app/keys
      # shared volume between worker and api for media
      - usermanagment-media:/app/media
    env_file: common.env

volumes:
  usermanagment-media:
    driver: local

networks:
  sehacity-backend-tier:
    driver: bridge
    name: sehacity-backend-tier
